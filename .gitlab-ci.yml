stages:
  - build
  - package
  - deploy

variables:
  CI_REGISTRY_IMAGE: "058264385393.dkr.ecr.me-south-1.amazonaws.com/${CI_ENVIRONMENT_NAME}/ewc-frontend"

.k8s-small:
  tags:
    - gcp
    - kubernetes
  variables:
    KUBERNETES_MEMORY_REQUEST: '2Gi'
    KUBERNETES_MEMORY_LIMIT: '2Gi'
    KUBERNETES_CPU_REQUEST: '1'
    KUBERNETES_CPU_LIMIT: '1'
    JDK_JAVA_OPTIONS: "-Xms1G -Xmx1G"
    NODE_OPTIONS: '--max_old_space_size=1900'

.k8s-medium:
  tags:
    - gcp
    - kubernetes
  variables:
    KUBERNETES_MEMORY_REQUEST: '4Gi'
    KUBERNETES_MEMORY_LIMIT: '4Gi'
    KUBERNETES_CPU_REQUEST: '2'
    KUBERNETES_CPU_LIMIT: '2'
    JDK_JAVA_OPTIONS: "-Xms2G -Xmx2G"
    NODE_OPTIONS: '--max_old_space_size=3800'

.machine-medium: # 4CPU, 4GB
  tags:
    - gcp
    - docker-medium
  variables:
    JDK_JAVA_OPTIONS: "-Xms2G -Xmx2G"
    NODE_OPTIONS: '--max_old_space_size=3800'

.env-qa:
  environment:
    name: qa
    action: prepare
  variables:
    ENV: qa
    NEXT_PUBLIC_STRAPI_URL: "https://strapi.stage.ewcf.cw.esl.systems"
    NEXT_PUBLIC_CDN_BASE_URL: "d2wwn9xcyag63s.cloudfront.net"
    NEXT_PUBLIC_APP_BASE_URL: "https://d1lmz5sxtogh9o.cloudfront.net"
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: "auth.stage.ewcf.cw.esl.systems"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_ID: "me-south-1_tucxSewrb"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_CLIENT_ID: "6klvfg6uv1962hiplupahds0qi"
    AWS_REGION: "me-south-1"
    AWS_ACCESS_KEY_ID: "********************"
    AWS_SECRET_ACCESS_KEY: "44KyogqLjnk62EJOSaKPHePRmBXIcRgcUS+/DJFA"
    NEXT_PUBLIC_BRAZE_API_KEY: "c3efd4a8-a291-4008-a156-47d62fce1ba0"
    NEXT_PUBLIC_BRAZE_BASE_URL: "sdk.fra-01.braze.eu"
    NEXT_PUBLIC_GRAPH_API_URL: "https://dzuvpjnlus2rx.cloudfront.net/2025-03-11/graphql"
  only:
    - qa
    - develop
    - EWC-79

.env-stage:
  environment:
    name: stage
    action: prepare
  variables:
    ENV: staging
    NEXT_PUBLIC_STRAPI_URL: "https://strapi.prod.ewcf.cw.esl.systems"
    NEXT_PUBLIC_CDN_BASE_URL: "d3h9qea4qy4169.cloudfront.net"
    NEXT_PUBLIC_APP_BASE_URL: "https://dkfvssyvuxd8g.cloudfront.net"
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: "auth.stage.ewcf.cw.esl.systems"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_ID: "me-south-1_tucxSewrb"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_CLIENT_ID: "6klvfg6uv1962hiplupahds0qi"
    AWS_REGION: "me-south-1"
    AWS_ACCESS_KEY_ID: "********************"
    AWS_SECRET_ACCESS_KEY: "44KyogqLjnk62EJOSaKPHePRmBXIcRgcUS+/DJFA"
    NEXT_PUBLIC_BRAZE_API_KEY: "c3efd4a8-a291-4008-a156-47d62fce1ba0"
    NEXT_PUBLIC_BRAZE_BASE_URL: "sdk.fra-01.braze.eu"
    NEXT_PUBLIC_GRAPH_API_URL: "https://dzuvpjnlus2rx.cloudfront.net/2025-03-11/graphql"
  only:
    - develop
    - master

.env-prod:
  environment:
    name: prod
    action: prepare
  variables:
    ENV: production
    NEXT_PUBLIC_STRAPI_URL: "https://strapi.prod.ewcf.cw.esl.systems"
    NEXT_PUBLIC_CDN_BASE_URL: "d3h9qea4qy4169.cloudfront.net"
    NEXT_PUBLIC_APP_BASE_URL: "https://esportsworldcup.com"
    NEXT_PUBLIC_AWS_COGNITO_DOMAIN: "auth.esportsworldcup.com"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_ID: "me-south-1_XTifEZ3UT"
    NEXT_PUBLIC_AWS_COGNITO_USER_POOL_CLIENT_ID: "4ermmk26rlgopcu7hhn1fdb8et"
    AWS_REGION: "me-south-1"
    AWS_ACCESS_KEY_ID: "********************"
    AWS_SECRET_ACCESS_KEY: "44KyogqLjnk62EJOSaKPHePRmBXIcRgcUS+/DJFA"
    NEXT_PUBLIC_BRAZE_API_KEY: "ba9b50e3-ea43-49e8-aa0f-2ad56b28b953"
    NEXT_PUBLIC_BRAZE_BASE_URL: "sdk.fra-01.braze.eu"
    NEXT_PUBLIC_GRAPH_API_URL: "https://dzuvpjnlus2rx.cloudfront.net/2025-03-11/graphql"
  only:
    - master

.build:
  image: node:22-alpine
  extends: .machine-medium
  stage: build
  cache:
    paths:
      - .cache/
      - .next/cache/
  variables:
    NEXT_TELEMETRY_DISABLED: 1
  before_script:
    - mkdir -p .cache/npm
    - npm set cache .cache/npm
  script:
    - npm install
    - npm run build
  artifacts:
    paths:
      - .next/standalone
      - .next/static
      - public

.package:
  stage: package
  extends: .k8s-small
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  script:
    - mkdir -p /kaniko/.docker
    - echo "{\"credsStore\":\"ecr-login\"}" > /kaniko/.docker/config.json
    - /kaniko/executor --context "${CI_PROJECT_DIR}" --destination "${CI_REGISTRY_IMAGE}:${CI_COMMIT_SHA}"

.deploy:
  stage: deploy
  extends: .k8s-small
  when: manual
  image: alpine/k8s:1.32.3
  environment:
    action: start
  variables:
    HELM_NAMESPACE: "${CI_ENVIRONMENT_NAME}"
    HELM_RELEASE: "${CI_PROJECT_NAME}"
  before_script:
    - aws eks update-kubeconfig --name ${EKS_CLUSTER_NAME}
    - helm repo add stakater https://stakater.github.io/stakater-charts
  script:
    - envsubst < deployment/${CI_ENVIRONMENT_NAME}.yaml | helm upgrade ${HELM_RELEASE} stakater/application --version 6.0.2
      --namespace ${HELM_NAMESPACE}
      --install
      --atomic
      --wait
      --wait-for-jobs
      --debug
      --values -
    - helm -n ${HELM_NAMESPACE} status ${HELM_RELEASE}

# Pipeline for QA env
build:qa:
  extends:
    - .env-qa
    - .build

package:qa:
  extends:
    - .env-qa
    - .package
  needs:
    - build:qa

deploy:qa:
  extends:
    - .env-qa
    - .deploy
  needs:
    - package:qa

# Pipeline for Stage env
build:stage:
  extends:
    - .env-stage
    - .build

package:stage:
  extends:
    - .env-stage
    - .package
  needs:
    - build:stage

deploy:stage:
  extends:
    - .env-stage
    - .deploy
  needs:
    - package:stage

# Pipeline for Prod env
build:prod:
  extends:
    - .env-prod
    - .build

package:prod:
  extends:
    - .env-prod
    - .package
  needs:
    - build:prod

deploy:prod:
  extends:
    - .env-prod
    - .deploy
  needs:
    - package:prod
