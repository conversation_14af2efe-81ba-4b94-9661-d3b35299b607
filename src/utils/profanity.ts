import { englishDataset, englishRecommendedTransformers, RegExpMatcher } from 'obscenity';

const profanityMatcher = new RegExpMatcher({
  ...englishDataset.build(),
  ...englishRecommendedTransformers,
});

export function getProfaneAttributes<T extends Record<string, any>>(input: T) {
  const profaneAttributes = [];

  for (const key in input) {
    const value = input[key];

    if (typeof value === 'string' && profanityMatcher.hasMatch(value)) {
      profaneAttributes.push(key);
    }
  }

  return profaneAttributes;
}

export function containsProfanities(input: string[]) {
  for (const value of input) {
    if (profanityMatcher.hasMatch(value)) {
      return true;
    }
  }

  return false;
}
