const englishOrdinalRules = new Intl.PluralRules('en', { type: 'ordinal' });
const suffixes: Partial<Record<Intl.LDMLPluralRule, string>> = {
  zero: 'th',
  one: 'st',
  two: 'nd',
  few: 'rd',
  other: 'th',
};

export function formatToOrdinal(number: number) {
  const category = englishOrdinalRules.select(number);
  const suffix = suffixes[category] as string;

  return `${number}${suffix}`;
}

const CurrencyFormat = new Intl.NumberFormat('en-US');
export function formatToCurrency(number: number) {
  return CurrencyFormat.format(number);
}
