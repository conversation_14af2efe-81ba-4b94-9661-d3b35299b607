import { MatchSeriesMetadata, MatchSeriesMetadataType } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';

export type LocalizedMap = Record<string, string>; // metadata.id → localized label

/**
 * Creates a localized match descriptor from metadata items using translation mappings.
 * This function combines GROUP, BRACKET, and ROUND metadata into a human-readable string.
 * 
 * @param metadata - Array of metadata items from match series
 * @param localizedLabels - Map of metadata IDs to their localized labels
 * @returns Localized match descriptor string
 */
export function getLocalizedMatchDescriptor(
  metadata: MatchSeriesMetadata[],
  localizedLabels: LocalizedMap
): string {
  // Group items by type
  const groupNames = metadata
    .filter(m => m.type === MatchSeriesMetadataType.GROUP)
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0))
    .map(m => localizedLabels[m.id] || m.name);

  const bracketItem = metadata.find(m => m.type === MatchSeriesMetadataType.BRACKET);
  const bracketLabel = bracketItem?.name?.toLowerCase();
  const bracket = bracketItem &&
    !['bracket', 'regular'].includes(bracketLabel || '')
    ? (localizedLabels[bracketItem.id] || bracketItem.name)
    : null;

  const roundItem = metadata.find(m => m.type === MatchSeriesMetadataType.ROUND);
  const round = roundItem
    ? (localizedLabels[roundItem.id] || roundItem.name)
    : null;

  // Combine parts
  const parts = [groupNames.join(' ').trim(), bracket, round].filter(Boolean);
  return parts.length ? parts.join(' – ') : 'Match';
}

/**
 * Creates a localized map from siteConfig translations for metadata IDs.
 * This function extracts metadata-specific translations from the global translations object.
 * 
 * @param translations - Global translations object from siteConfig
 * @returns Map of metadata IDs to localized labels
 */
export function createLocalizedMapFromTranslations(
  translations: JsonFieldType | null | undefined
): LocalizedMap {
  if (!translations) return {};
  
  // Extract metadata translations (keys that start with 'metadata_')
  const localizedMap: LocalizedMap = {};
  
  Object.entries(translations).forEach(([key, value]) => {
    if (key.startsWith('metadata_')) {
      // Remove 'metadata_' prefix to get the actual metadata ID
      const metadataId = key.replace('metadata_', '');
      localizedMap[metadataId] = value;
    }
  });
  
  return localizedMap;
}
