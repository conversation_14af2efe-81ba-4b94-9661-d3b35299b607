import i18nConfig from '../../i18nConfig';

export function constructUrl(url: string, queryParams: Record<string, string>) {
  const callbackUrl = new URL(url);

  for (const key in queryParams) {
    callbackUrl.searchParams.set(key, queryParams[key]);
  }

  return callbackUrl;
}

export function normalize(path: string): string {
  return path.replace(/\/+$/, '') || '/';
}

export function stripLocalePrefix(path: string): string {
  const segments = path.split('/').filter(Boolean);

  if (segments.length === 0) return '/';

  if (i18nConfig.locales.includes(segments[0])) {
    return '/' + segments.slice(1).join('/');
  }

  return path;
}
