import { NextRequest, NextResponse } from 'next/server';
import { i18nRouter } from 'next-i18n-router';
import { Config } from 'next-i18n-router/dist/types';
import { nextBasicAuthMiddleware } from 'nextjs-basic-auth-middleware';

import i18nConfig from '../i18nConfig';
import { TTL_1_DAY, TTL_1_YEAR } from './config/cache';
import { ALL_LOCALES } from './hooks/i18n/const';

const staleWhileRevalidateDirective = `stale-while-revalidate=${TTL_1_YEAR}`;
const staleIfErrorDirective = `stale-if-error=${TTL_1_YEAR}`;

export function middleware(request: NextRequest) {
  const authResponse = nextBasicAuthMiddleware({}, request);

  if (authResponse.status === 401) {
    return authResponse;
  }

  if (request.nextUrl.pathname === '/auth/twitch') {
    return getTwitchAuthRedirectResponse(request);
  }

  return getI18nResponse(request);
}

function getTwitchAuthRedirectResponse(request: NextRequest) {
  const claimsParam = encodeURIComponent(
    'claims={"id_token":{"email":null,"email_verified":null},"userinfo":{"email":null,"email_verified":null}}',
  );
  const queryParams = `${request.nextUrl.search}&${claimsParam}`;

  return NextResponse.redirect(`https://id.twitch.tv/oauth2/authorize${queryParams}`);
}

function getI18nResponse(request: NextRequest) {
  const i18nResponse = i18nRouter(request, i18nConfig as Config);

  const isLocalizedUrl = ALL_LOCALES.some((l) => request.nextUrl.pathname.includes(l));
  if (isLocalizedUrl) {
    i18nResponse.headers.set(
      'Cache-Control',
      `public, s-maxage=${process.env.REVALIDATE_TIME_IN_SECONDS}, ${staleWhileRevalidateDirective}, ${staleIfErrorDirective}`,
    );
  } else {
    i18nResponse.headers.set(
      'Cache-Control',
      `public, s-maxage=${TTL_1_DAY}, ${staleWhileRevalidateDirective}, ${staleIfErrorDirective}`,
    );
  }

  i18nResponse.headers.set('x-debug-request-time', new Date().toISOString());
  return i18nResponse;
}

export const config = {
  matcher: ['/((?!api|static|.*\\..*|_next).*)'],
};
