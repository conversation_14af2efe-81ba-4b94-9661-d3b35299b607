import {
  AdminLinkProviderForUserCommand,
  CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';
import { CognitoJwtVerifier } from 'aws-jwt-verify';
import { CognitoIdTokenPayload } from 'aws-jwt-verify/jwt-model';
import { NextRequest, NextResponse } from 'next/server';

import { COGNITO_CLIENT_ID, COGNITO_USER_POOL_ID } from '@/config/env/client';
import { AWS_ACCESS_KEY_ID, AWS_REGION, AWS_SECRET_ACCESS_KEY } from '@/config/env/server';

const client = new CognitoIdentityProviderClient({
  region: AWS_REGION,
  credentials: { accessKeyId: AWS_ACCESS_KEY_ID, secretAccessKey: AWS_SECRET_ACCESS_KEY },
});

const verifier = CognitoJwtVerifier.create({
  userPoolId: COGNITO_USER_POOL_ID,
  tokenUse: 'id',
  clientId: COGNITO_CLIENT_ID,
});

export async function POST(req: NextRequest) {
  try {
    const { currentUserToken, newAccountToken } = await req.json();

    let verifiedCurrentUserToken: CognitoIdTokenPayload;
    try {
      verifiedCurrentUserToken = await verifier.verify(currentUserToken);
    } catch (error: any) {
      return NextResponse.json({ message: error.message ?? 'Current user token is invalid.' }, { status: 400 });
    }

    let verifiedNewAccountToken: CognitoIdTokenPayload;
    try {
      verifiedNewAccountToken = await verifier.verify(newAccountToken);
    } catch (error: any) {
      return NextResponse.json({ message: error.message ?? 'New account token is invalid.' }, { status: 400 });
    }

    if (verifiedCurrentUserToken.sub !== verifiedNewAccountToken.sub) {
      const provider = verifiedNewAccountToken.identities[0].providerName;
      const command = new AdminLinkProviderForUserCommand({
        UserPoolId: COGNITO_USER_POOL_ID,
        DestinationUser: {
          ProviderName: 'Cognito',
          ProviderAttributeValue: verifiedCurrentUserToken.sub,
        },
        SourceUser: {
          ProviderName: provider,
          ProviderAttributeName: 'Cognito_Subject',
          ProviderAttributeValue: verifiedNewAccountToken.sub,
        },
      });
      await client.send(command);
    }

    return NextResponse.json({ message: 'Account linked.' });
  } catch (error: any) {
    return NextResponse.json({ message: error.message ?? 'Internal Server Error' }, { status: 500 });
  }
}
