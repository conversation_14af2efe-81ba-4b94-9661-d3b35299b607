import {
  AdminDisableProviderForUserCommand,
  CognitoIdentityProviderClient,
} from '@aws-sdk/client-cognito-identity-provider';
import { CognitoJwtVerifier } from 'aws-jwt-verify';
import { CognitoIdTokenPayload } from 'aws-jwt-verify/jwt-model';
import { NextRequest, NextResponse } from 'next/server';

import { COGNITO_CLIENT_ID, COGNITO_USER_POOL_ID } from '@/config/env/client';
import { AWS_ACCESS_KEY_ID, AWS_REGION, AWS_SECRET_ACCESS_KEY } from '@/config/env/server';

const client = new CognitoIdentityProviderClient({
  region: AWS_REGION,
  credentials: { accessKeyId: AWS_ACCESS_KEY_ID, secretAccessKey: AWS_SECRET_ACCESS_KEY },
});

const verifier = CognitoJwtVerifier.create({
  userPoolId: COGNITO_USER_POOL_ID,
  tokenUse: 'id',
  clientId: COGNITO_CLIENT_ID,
});

export async function POST(req: NextRequest) {
  try {
    const { providerName, token } = await req.json();

    if (!providerName) {
      return NextResponse.json({ message: 'Missing provider name field.' }, { status: 400 });
    }

    let verifiedToken: CognitoIdTokenPayload;
    try {
      verifiedToken = await verifier.verify(token);
    } catch (error: any) {
      return NextResponse.json({ message: error.message ?? 'Provided token is invalid.' }, { status: 400 });
    }

    const identity = verifiedToken.identities.find((i) => i.providerName === providerName);
    if (!identity) {
      return NextResponse.json({ message: `Provider ${providerName} not linked.` }, { status: 400 });
    }

    const command = new AdminDisableProviderForUserCommand({
      UserPoolId: COGNITO_USER_POOL_ID,
      User: {
        ProviderName: providerName,
        ProviderAttributeName: 'Cognito_Subject',
        ProviderAttributeValue: identity.userId,
      },
    });

    await client.send(command);
    return NextResponse.json({ message: 'Provider unlinked successfully.' });
  } catch (error: any) {
    return NextResponse.json({ message: error.message ?? 'Internal Server Error' }, { status: 500 });
  }
}
