import { revalidatePath } from 'next/cache';

import { strapiToWebLocale } from '@/strapi/api/client';

enum WebhookEvent {
  ENTRY_CREATE = 'entry.create',
  ENTRY_UPDATE = 'entry.update',
  ENTRY_DELETE = 'entry.delete',
  ENTRY_PUBLISH = 'entry.publish',
  ENTRY_UNPUBLISH = 'entry.unpublish',
  MEDIA_CREATE = 'media.create',
  MEDIA_UPDATE = 'media.update',
  MEDIA_DELETE = 'media.delete',
}

const REVALIDATE_EVENTS = [WebhookEvent.ENTRY_PUBLISH, WebhookEvent.ENTRY_UNPUBLISH, WebhookEvent.ENTRY_DELETE];

interface WebhookData {
  event: WebhookEvent;
  createdAt: string;
  model: string;
  entry: {
    id: number;
    slug: string;
    locale: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
}

export async function POST(request: Request) {
  return new Response('Unauthorized', { status: 401 });

  // const headersList = await headers();

  // const apiKey = headersList.get('X-API-Key');
  // if (apiKey !== NEXT_API_KEY) {
  //   return new Response('Unauthorized', { status: 401 });
  // }

  const data = (await request.json()) as WebhookData;
  if (!REVALIDATE_EVENTS.includes(data.event)) {
    return new Response('Nothing revalidated', { status: 200 });
  }

  switch (data.model) {
    case 'homepage':
      revalidateHomepage(data);
      break;
    case 'page-previous-tournament':
      revalidatePreviousTournamentPage(data);
      break;
    case 'news-page':
      revalidateNewsPage(data);
      break;
    case 'page':
      revalidatePage(data);
      break;
    case 'news-article':
      revalidateNewsArticle(data);
      break;
    default:
      return Response.json({ revalidated: false });
  }

  return Response.json({ revalidated: true });
}

function revalidateHomepage(data: WebhookData) {
  const { locale } = data.entry;

  const path = `/${strapiToWebLocale(locale)}`;
  console.log('revalidating homepage:', path);

  revalidatePath(path);
}

function revalidatePreviousTournamentPage(data: WebhookData) {
  const { locale } = data.entry;

  const path = `/${strapiToWebLocale(locale)}/2024`;
  console.log('revalidating previous tournament page:', path);

  revalidatePath(path);
}

function revalidateNewsPage(data: WebhookData) {
  const { locale } = data.entry;

  const path = `/${strapiToWebLocale(locale)}/news`;
  console.log('revalidating news page:', path);

  revalidatePath(path);
}

function revalidatePage(data: WebhookData) {
  const { slug, locale } = data.entry;

  const path = `/${strapiToWebLocale(locale)}/${slug}`;
  console.log('revalidating page:', path);

  revalidatePath(path);
}

function revalidateNewsArticle(data: WebhookData) {
  const { slug, locale } = data.entry;

  const path = `/${strapiToWebLocale(locale)}/news/${slug}`;
  console.log('revalidating news article:', path);

  revalidatePath(path);
}
