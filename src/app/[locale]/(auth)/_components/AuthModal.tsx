import { SimpleRichTextContent } from '@/components/SimpleRichTextContent';
import { RichTextContentType } from '@/strapi/types/helper';

type Props = React.PropsWithChildren<{
  title: string | null;
  subtitle: string | null;
  footer: RichTextContentType | null;
}>;

export const AuthModal = ({ title, subtitle, footer, children }: Props) => {
  return (
    <div className="relative mx-4 my-8 w-full max-w-[450px] lg:max-w-[510px]">
      <div className="rounded-lg bg-white p-2 pt-4 md:rounded-2xl md:p-4 md:pt-8 lg:rounded-3xl xl:p-8 xl:pt-16">
        <div className="flex flex-col gap-4 lg:gap-8">
          {(title || subtitle) && (
            <div className="text-dark-default flex flex-col items-center gap-1 text-center">
              {title && <h1 className="text-h3">{title}</h1>}
              {subtitle && <p className="text-paragraph">{subtitle}</p>}
            </div>
          )}
          <div className="flex flex-col gap-2 md:gap-4">{children}</div>
          {footer && (
            <SimpleRichTextContent
              content={footer}
              linkClassname="text-gold-primary underline"
              paragraphClassname="text-dark-default font-base text-center text-xs leading-normal font-medium md:text-sm"
            />
          )}
        </div>
      </div>
    </div>
  );
};
