'use client';

import { useLogout } from '@/services/auth/hooks';
import { Button } from '@/ui/components/Button';

export const SignoutButton = ({ text = 'sign out' }: { text: string | null }) => {
  const logout = useLogout();

  return (
    <Button
      brazeEventProperties={{ button_name: `Signout Button`, location: `Profile Page - Header` }}
      text={text}
      variant="secondary"
      onClick={logout}
    />
  );
};
