'use client';

import { FormProvider } from 'react-hook-form';

import { FormPasswordInput } from '@/components/form/FormPasswordInput';
import { FormTextInput } from '@/components/form/FormTextInput';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';

import { LoginFormValues, useLoginForm } from './hooks';

export interface LoginFormProps {
  emailFieldPlaceholder: string | null;
  passwordFieldPlaceholder: string | null;
  loginButtonText: string | null;
  signupButtonText: string | null;
  signupUrl: string;
  translations?: JsonFieldType | null;
  onLogin: (values: LoginFormValues) => Promise<void>;
}

export const LoginForm = ({
  emailFieldPlaceholder,
  passwordFieldPlaceholder,
  loginButtonText,
  signupButtonText,
  signupUrl,
  translations,
  onLogin,
}: LoginFormProps) => {
  const formMethods = useLoginForm(translations);
  const { formState, handleSubmit } = formMethods;

  return (
    <FormProvider {...formMethods}>
      <form className="flex flex-col gap-2 self-stretch lg:gap-4" onSubmit={handleSubmit(onLogin)}>
        <FormTextInput name="username" placeholder={emailFieldPlaceholder ?? 'Your email address'} />
        <FormPasswordInput name="password" placeholder={passwordFieldPlaceholder ?? 'Your password'} />
        <Button
          brazeEventProperties={{ button_name: 'Login', location: 'Login Modal' }}
          isDisabled={!formState.isValid || formState.isSubmitting}
          isFullWidth
          text={loginButtonText ?? 'login'}
          type="submit"
        />
        <Button
          brazeEventProperties={{ button_name: 'Create Account', location: 'Login Modal' }}
          isFullWidth
          link={signupUrl}
          text={signupButtonText ?? 'create a free ewc account'}
          type="button"
          variant="secondary"
        />
      </form>
    </FormProvider>
  );
};
