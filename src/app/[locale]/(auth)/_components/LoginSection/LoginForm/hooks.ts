import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { getEmailSchema } from '@/config/schema/auth';
import { JsonFieldType } from '@/strapi/types/helper';

function getLoginFormSchema(translations?: JsonFieldType | null) {
  return yup.object({
    username: getEmailSchema(translations),
    password: yup.string().required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.'),
  });
}

export interface LoginFormValues {
  username: string;
  password: string;
}

export function useLoginForm(translations?: JsonFieldType | null) {
  return useForm<LoginFormValues>({
    defaultValues: { username: '', password: '' },
    resolver: yupResolver(getLoginFormSchema(translations)),
    mode: 'onTouched',
  });
}
