import { useEffect } from 'react';

import { OauthQueryParams } from '../../_utils';
import { getInvalidQueryParams, redirectWithTokens } from './utils';

export function useRedirectIfLoggedIn(params: OauthQueryParams) {
  const invalidParams = getInvalidQueryParams(params);

  useEffect(() => {
    async function redirectIfLoggedIn() {
      try {
        await redirectWithTokens(params);
      } catch {}
    }

    if (invalidParams.length === 0) {
      redirectIfLoggedIn();
    }
  }, [invalidParams.length, params]);
}
