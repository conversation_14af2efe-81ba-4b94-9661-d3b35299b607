'use client';

import { useSearchParams } from 'next/navigation';

import { signIn } from '@/services/auth/cognito';
import { signInWithProviderExternal } from '@/services/auth/cognito/utils';
import { LoginPageData } from '@/strapi/api/single/authPages/types';
import { showErrorToast, showInfoToast } from '@/ui/components/Toast';

import { AuthModal } from '../../_components/AuthModal';
import { LoginSection } from '../../_components/LoginSection';
import { LoginFormValues } from '../../_components/LoginSection';
import { pullOauthQueryParams } from '../../_utils';
import { useRedirectIfLoggedIn } from './hooks';
import { getInvalidQueryParams, redirectWithTokens } from './utils';

export const ExternalLoginModal = ({
  title,
  subtitle,
  footerText,
  externalLoginMissingFieldsText,
  ...rest
}: LoginPageData) => {
  const searchParams = useSearchParams();
  const params = pullOauthQueryParams(searchParams);

  useRedirectIfLoggedIn(params);

  async function loginUser(values: LoginFormValues) {
    try {
      const { isSignedIn, nextStep } = await signIn(values);
      if (!isSignedIn) {
        showInfoToast({ title: 'Login error!', description: `Next step: ${nextStep.signInStep}.` });
        return;
      }

      await redirectWithTokens(params);
    } catch (error: any) {
      showErrorToast({ title: 'Error while logging in.', description: error.message });
    }
  }

  const invalidParams = getInvalidQueryParams(params);
  return (
    <AuthModal footer={footerText} subtitle={subtitle} title={title}>
      {invalidParams.length === 0 ? (
        <LoginSection
          {...rest}
          signupUrl={`signup?${searchParams.toString()}`}
          onLogin={loginUser}
          onProviderClick={async (p) => signInWithProviderExternal(p, searchParams)}
        />
      ) : (
        <p className="text-dark-default text-paragraph text-center">
          {externalLoginMissingFieldsText ?? 'Error! Some required query parameters are missing or invalid'}:{' '}
          {invalidParams.join(', ')}
        </p>
      )}
    </AuthModal>
  );
};
