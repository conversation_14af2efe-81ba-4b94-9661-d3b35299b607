export interface OauthQueryParams {
  client_id: string;
  redirect_uri: string;
  scope: string;
  response_type: string;
  response_mode: string;
  state: string;
}

export function pullOauthQueryParams(params: URLSearchParams) {
  const client_id = params.get('client_id');
  const redirect_uri = params.get('redirect_uri');
  const scope = params.get('scope');
  const response_type = params.get('response_type');
  const response_mode = params.get('response_mode');
  const state = params.get('state');

  return { client_id, redirect_uri, scope, response_type, response_mode, state } as OauthQueryParams;
}
