import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { getPasswordConfirmSchema, getPasswordSchema } from '@/config/schema/auth';
import { JsonFieldType } from '@/strapi/types/helper';

function getUpdatePasswordSchema(translations?: JsonFieldType | null) {
  return yup.object({
    oldPassword: getPasswordSchema(translations),
    password: getPasswordSchema(translations),
    passwordConfirm: getPasswordConfirmSchema(translations),
  });
}

export interface UpdatePasswordFormValues {
  oldPassword: string;
  password: string;
  passwordConfirm: string;
}

export function useUpdatePasswordForm(translations?: JsonFieldType | null) {
  return useForm<UpdatePasswordFormValues>({
    defaultValues: { oldPassword: '', password: '', passwordConfirm: '' },
    resolver: yupResolver(getUpdatePasswordSchema(translations)),
    mode: 'onTouched',
  });
}
