'use client';

import { useState } from 'react';
import { FormProvider } from 'react-hook-form';

import { FormTextInput } from '@/components/form/FormTextInput';
import { LocalizedLink } from '@/components/LocalizedLink';
import { SimpleRichTextContent } from '@/components/SimpleRichTextContent';
import { resetPassword } from '@/services/auth/cognito';
import { ForgotPasswordPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType, RichTextContentType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { showErrorToast, showInfoToast } from '@/ui/components/Toast';

import { ForgotPasswordFormValues, useForgotPasswordForm } from './hooks';

type ResetPasswordSectionProps = ForgotPasswordPageData & {
  translations?: JsonFieldType | null;
};

export const ResetPasswordSection = (props: ResetPasswordSectionProps) => {
  const [isSendResetEmailSection, setIsResetEmailSection] = useState(true);

  return isSendResetEmailSection ? (
    <SendResetEmailSection onResetEmailSent={() => setIsResetEmailSection(false)} {...props} />
  ) : (
    <GoToResetPasswordPageSection text={props.confirmationCodeSentText} />
  );
};

type Props = ResetPasswordSectionProps & { onResetEmailSent: () => void };

const SendResetEmailSection = ({
  emailFieldPlaceholder,
  sendEmailButtonText,
  translations,
  onResetEmailSent,
}: Props) => {
  const formMethods = useForgotPasswordForm(translations);
  const { handleSubmit, formState } = formMethods;

  async function sendResetEmail({ username }: ForgotPasswordFormValues) {
    try {
      const { nextStep } = await resetPassword({ username });
      if (nextStep.resetPasswordStep === 'CONFIRM_RESET_PASSWORD_WITH_CODE') {
        onResetEmailSent();
        return;
      }

      showInfoToast({
        title: 'Password reset not completed!',
        description: `Next step: ${nextStep.resetPasswordStep}.`,
      });
    } catch (error: any) {
      showErrorToast({ title: 'Password reset error!', description: error.message });
    }
  }

  const { isValid, isSubmitting } = formState;
  return (
    <FormProvider {...formMethods}>
      <form className="flex flex-col gap-4" onSubmit={handleSubmit(sendResetEmail)}>
        <FormTextInput name="username" placeholder={emailFieldPlaceholder ?? 'Your email address'} />
        <Button
          brazeEventProperties={{
            button_name: `Send Reset Email Button`,
            location: `Forgot Password Form`,
          }}
          isDisabled={!isValid || isSubmitting}
          isFullWidth
          text={sendEmailButtonText ?? 'send reset email'}
          type="submit"
        />
      </form>
    </FormProvider>
  );
};

const GoToResetPasswordPageSection = ({ text }: { text: RichTextContentType | null }) => {
  if (text) {
    return (
      <SimpleRichTextContent
        content={text}
        linkClassname="text-gold-primary underline"
        paragraphClassname="text-paragraph text-center"
      />
    );
  }

  return (
    <p className="text-paragraph text-center">
      We’ve sent a password reset confirmation code to your email. Please check your inbox and enter the code on the{' '}
      <LocalizedLink
        brazeEventProperties={{ location: 'Go To Reset Password Section', button_name: 'Reset Password Page Link' }}
        className="text-gold-primary underline"
        href="/reset-password"
      >
        password reset page
      </LocalizedLink>
      .
    </p>
  );
};
