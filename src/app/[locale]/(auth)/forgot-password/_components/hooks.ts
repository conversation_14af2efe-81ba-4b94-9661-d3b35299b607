import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { getEmailSchema } from '@/config/schema/auth';
import { JsonFieldType } from '@/strapi/types/helper';

function getForgotPasswordSchema(translations?: JsonFieldType | null) {
  return yup.object({
    username: getEmailSchema(translations),
  });
}

export interface ForgotPasswordFormValues {
  username: string;
}

export function useForgotPasswordForm(translations?: JsonFieldType | null) {
  return useForm<ForgotPasswordFormValues>({
    defaultValues: { username: '' },
    resolver: yupResolver(getForgotPasswordSchema(translations)),
    mode: 'onTouched',
  });
}
