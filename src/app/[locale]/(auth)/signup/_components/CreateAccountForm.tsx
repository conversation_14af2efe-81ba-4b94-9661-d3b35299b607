'use client';

import { FormProvider } from 'react-hook-form';

import { FormCheckbox } from '@/components/form/FormCheckbox';
import { FormCountrySelect } from '@/components/form/FormCountrySelect';
import { FormPasswordInput } from '@/components/form/FormPasswordInput';
import { FormTextInput } from '@/components/form/FormTextInput';
import { SignupPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { getProfaneAttributes } from '@/utils/profanity';

import { SignupFormValues, useSignupForm } from './hooks';

type Props = Omit<SignupPageData, 'termsOfUseAlertTitle' | 'termsOfUseAlertBody' | 'termsOfUseAlertButtonText'> & {
  translations?: JsonFieldType | null;
  onSignup: (values: SignupFormValues) => Promise<void>;
};

export const CreateAccountForm = ({
  emailFieldPlaceholder,
  nameFieldPlaceholder,
  surnameFieldPlaceholder,
  usernameFieldPlaceholder,
  passwordFieldPlaceholder,
  passwordConfirmFieldPlaceholder,
  countryFieldPlaceholder,
  termsOfUseCheckboxText,
  newsCheckboxText,
  marketingCheckboxText,
  signupButtonText,
  translations,
  onSignup,
}: Props) => {
  const formMethods = useSignupForm(translations);
  const { handleSubmit, formState, setError } = formMethods;

  async function validateAndSignup(values: SignupFormValues) {
    const profaneAttributes = getProfaneAttributes(values);

    if (profaneAttributes.length === 0) {
      return onSignup(values);
    }

    for (const attr of profaneAttributes) {
      setError(attr, {
        message:
          translations?.['profanityErrorMessage'] ??
          'This input contains inappropriate language. Please remove any profanity.',
      });
    }
  }

  return (
    <FormProvider {...formMethods}>
      <form
        className="flex flex-col items-center gap-2 self-stretch md:gap-4"
        onSubmit={handleSubmit(validateAndSignup)}
      >
        <div className="flex flex-col gap-2 self-stretch lg:gap-4">
          <FormTextInput name="email" placeholder={emailFieldPlaceholder ?? 'Your email address'} />
          <div className="flex gap-2 max-md:flex-col lg:gap-4">
            <FormTextInput name="name" placeholder={nameFieldPlaceholder ?? 'Name'} />
            <FormTextInput name="surname" placeholder={surnameFieldPlaceholder ?? 'Surname'} />
          </div>
          <FormTextInput name="username" placeholder={usernameFieldPlaceholder ?? 'Username'} />
          <FormPasswordInput name="password" placeholder={passwordFieldPlaceholder ?? 'Your password'} />
          <FormPasswordInput
            name="passwordConfirm"
            placeholder={passwordConfirmFieldPlaceholder ?? 'Confirm password'}
          />
          <FormCountrySelect placeholder={countryFieldPlaceholder ?? 'Country'} />
        </div>
        <div className="flex flex-col gap-4 self-start">
          <FormCheckbox
            label={termsOfUseCheckboxText ?? 'I accept the Terms of Use of the platform.'}
            name="termsAccepted"
          />
          <FormCheckbox
            label={
              newsCheckboxText ??
              'I’m happy to receive news, results and press releases about the Esports World Cup in email.'
            }
            name="newsAccepted"
          />
          <FormCheckbox
            label={
              marketingCheckboxText ??
              'I’m happy to receive marketing, promotional offers and discounts from the Esports World Cup in email.'
            }
            name="marketingAccepted"
          />
        </div>
        <Button
          brazeEventProperties={{
            button_name: `Signup Button`,
            location: `Signup Modal`,
          }}
          isDisabled={!formState.isValid || formState.isSubmitting}
          isFullWidth
          text={signupButtonText ?? 'create account'}
          type="submit"
        />
      </form>
    </FormProvider>
  );
};
