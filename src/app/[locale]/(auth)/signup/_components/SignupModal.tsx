'use client';

import { useRouter, useSearchParams } from 'next/navigation';

import { LocalizedLink } from '@/components/LocalizedLink';
import { signup } from '@/services/auth/cognito';
import { AuthMethod } from '@/services/auth/types';
import { logBrazeEvent, updateBrazeUser } from '@/services/braze';
import { SignupPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { showErrorToast, showInfoToast } from '@/ui/components/Toast';

import { AuthModal } from '../../_components/AuthModal';
import { SocialLoginSection } from '../../_components/SocialLoginSection';
import { TextDivider } from '../../_components/TextDivider';
import { CreateAccountForm } from './CreateAccountForm';
import { SignupFormValues, useSocialLoginHandler } from './hooks';

type Props = SignupPageData & {
  translations?: JsonFieldType | null;
};

export const SignupModal = ({
  translations,
  termsOfUseAlertTitle,
  termsOfUseAlertBody,
  termsOfUseAlertButtonText,
  ...rest
}: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const handleSocialLogin = useSocialLoginHandler(searchParams, {
    termsOfUseAlertTitle,
    termsOfUseAlertBody,
    termsOfUseAlertButtonText,
  });

  async function registerUser(values: SignupFormValues) {
    try {
      const { nextStep } = await signup(values);

      logBrazeEvent('user_signed_up', { channel: AuthMethod.USERNAME_PASSWORD });
      updateBrazeUser(values);

      if (nextStep.signUpStep === 'CONFIRM_SIGN_UP') {
        router.push(`confirm-email?${searchParams.toString()}&username=${encodeURIComponent(values.email)}`);
        return;
      }

      showInfoToast({ title: 'Signup not completed!', description: `Next step: ${nextStep.signUpStep}.` });
    } catch (error: any) {
      showErrorToast({ title: 'Signup error!', description: error.message });
    }
  }

  const isExternalLogin = !!searchParams.get('client_id');
  return (
    <AuthModal footer={rest.footerText} subtitle={rest.subtitle} title={rest.title}>
      <SocialLoginSection onProviderClick={handleSocialLogin} />
      <TextDivider text={rest.separatorText} />
      <CreateAccountForm {...rest} translations={translations} onSignup={registerUser} />
      <p className="text-button-default text-dark-default text-center">
        {rest.loginText}{' '}
        <LocalizedLink
          brazeEventProperties={{ location: 'Signup Modal', button_name: 'Login Page Link' }}
          className="text-gold-primary"
          href={isExternalLogin ? `auth?${searchParams.toString()}` : 'login'}
        >
          {translations?.['login'] ?? 'login'}!
        </LocalizedLink>
      </p>
    </AuthModal>
  );
};
