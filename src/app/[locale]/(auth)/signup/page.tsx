import { Metadata } from 'next';
import Image from 'next/image';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { UserLoggedInRedirect } from '@/services/auth/components/UserLoggedInRedirect';
import { getSignupPageData } from '@/strapi/api/single/authPages';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import BackgroundImage from '@/ui/assets/images/abstract-background-2.png';

import { SignupModal } from './_components';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const data = await getSignupPageData(locale);

  return {
    title: data?.seo?.metaTitle ?? 'Esports World Cup | Signup',
    description: data?.seo?.metaDescription ?? 'Esports World Cup | Signup',
  };
}

export default async function SignupPage({ params }: PageProps) {
  const locale = (await params).locale;
  const data = await getSignupPageData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (!data) {
    return notFound();
  }

  return (
    <div className="relative flex min-h-screen w-full items-center justify-center">
      <UserLoggedInRedirect />
      <Image alt="" className="absolute inset-0 h-full w-full object-cover" quality={100} src={BackgroundImage} />
      <SignupModal {...data} translations={siteConfig?.translations} />
    </div>
  );
}
