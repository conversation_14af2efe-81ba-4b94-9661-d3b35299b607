'use client';

import { resendSignUpCode } from 'aws-amplify/auth';

import { ConfirmEmailPageData } from '@/strapi/api/single/authPages/types';

type Props = ConfirmEmailPageData & { username: string };

export const ResendCodeSection = ({ username, separatorText, separatorRequestAgainText }: Props) => {
  if (!separatorText || !separatorRequestAgainText) {
    return null;
  }

  return (
    <div className="flex items-center gap-4 self-stretch">
      <div className="bg-gray h-px grow" />
      <p className="font-base text-dark-default shrink-0 text-base">
        {separatorText ? `${separatorText} ` : ''}
        {separatorRequestAgainText && (
          <span className="text-gold-primary cursor-pointer underline" onClick={() => resendSignUpCode({ username })}>
            {separatorRequestAgainText}
          </span>
        )}
      </p>
      <div className="bg-gray h-px grow" />
    </div>
  );
};
