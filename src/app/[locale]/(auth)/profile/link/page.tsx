import { PageProps } from '@/config/types/page';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

import { UserProfileGate } from '../../_components/UserProfileGate';
import { LinkAccountListener } from './_components/LinkAccountListener';

export * from '@/config/page-cache';

export default async function ProfileLinkPage({ params }: PageProps) {
  const { locale } = await params;
  const siteConfig = await getSiteConfig(locale);

  return (
    <div className="min-h-screen px-4 pt-5 pb-20 md:pt-10 lg:px-8 xl:pt-20 2xl:py-40">
      <div className="mx-auto w-full max-w-[1396px]">
        <UserProfileGate>
          <LinkAccountListener translations={siteConfig?.translations} />
        </UserProfileGate>
      </div>
    </div>
  );
}
