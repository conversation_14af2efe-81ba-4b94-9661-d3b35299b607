type Props = React.PropsWithChildren<{ title?: string | null; subtitle?: string | null }>;

export const ProfilePageSectionContainer = ({ title, subtitle, children }: Props) => {
  return (
    <div className="text-dark-default flex flex-col gap-4 self-stretch rounded-lg bg-white p-4 shadow-[0px_12px_16px_0px_#0000000F] md:rounded-2xl md:p-8 xl:gap-8 xl:rounded-4xl">
      {(title || subtitle) && (
        <>
          <div className="flex flex-col">
            {title && <h2 className="text-h5">{title}</h2>}
            {subtitle && <p className="text-paragraph">{subtitle}</p>}
          </div>
          <div className="bg-gray h-px" />
        </>
      )}
      {children}
    </div>
  );
};
