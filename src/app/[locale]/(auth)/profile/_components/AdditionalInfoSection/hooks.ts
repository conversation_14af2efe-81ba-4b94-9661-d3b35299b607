import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';

import { User } from '@/services/auth/types';

const additionalProfileSchema = yup.object({
  address1: yup.string().ensure(),
  address2: yup.string().ensure(),
  country: yup.string().ensure(),
  zipcode: yup.string().ensure(),
  city: yup.string().ensure(),
  state: yup.string().ensure(),
  favoriteGames: yup.array().of(yup.string().ensure()).required(),
  favoriteClubs: yup.array().of(yup.string().ensure()).required(),
  favoritePlayers: yup.array().of(yup.string().ensure()).required(),
});

export interface AdditionalProfileFormValues {
  address1: string;
  address2: string;
  country: string;
  zipcode: string;
  city: string;
  state: string;
  favoriteGames: string[];
  favoriteClubs: string[];
  favoritePlayers: string[];
}

export function useAdditionalProfileForm(user: User) {
  const [address1, address2] = user.address?.split('\n') ?? ['', ''];

  return useForm<AdditionalProfileFormValues>({
    defaultValues: {
      address1: address1 ?? '',
      address2: address2 ?? '',
      country: user['custom:country'] ?? '',
      zipcode: user['custom:zipcode'] ?? '',
      city: user['custom:city'] ?? '',
      state: user['custom:state'] ?? '',
      favoriteGames: user['custom:favourite_games']?.split(';') ?? [],
      favoriteClubs: user['custom:favourite_clubs']?.split(';') ?? [],
      favoritePlayers: user['custom:favourite_players']?.split(';') ?? [],
    },
    resolver: yupResolver(additionalProfileSchema),
    mode: 'onTouched',
  });
}
