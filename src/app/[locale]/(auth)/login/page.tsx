export * from '@/config/page-cache';

import { Metadata } from 'next';
import Image from 'next/image';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { UserLoggedInRedirect } from '@/services/auth/components/UserLoggedInRedirect';
import { getLoginPageData } from '@/strapi/api/single/authPages';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import BackgroundImage from '@/ui/assets/images/abstract-background-3.png';

import { LoginModal } from './_components';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const data = await getLoginPageData(locale);

  return {
    title: data?.seo?.metaTitle ?? 'Esports World Cup | Login',
    description: data?.seo?.metaDescription ?? 'This is Esports World Cup | Login',
  };
}

export default async function LoginPage({ params }: PageProps) {
  const locale = (await params).locale;
  const data = await getLoginPageData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (!data) {
    return notFound();
  }

  return (
    <div className="relative flex min-h-screen w-full items-center justify-center">
      <UserLoggedInRedirect />
      <Image alt="" className="absolute inset-0 h-full w-full object-cover" quality={100} src={BackgroundImage} />
      <LoginModal {...data} translations={siteConfig?.translations} />
    </div>
  );
}
