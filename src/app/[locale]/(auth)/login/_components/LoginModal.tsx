'use client';

import { useRouter } from 'next/navigation';

import { fetchCurrentUser, signIn, signInWithProviderInternal } from '@/services/auth/cognito';
import { useSetCurrentUserQuery } from '@/services/auth/hooks';
import { AuthMethod } from '@/services/auth/types';
import { changeBrazeUser } from '@/services/braze';
import { LoginPageData } from '@/strapi/api/single/authPages/types';
import { JsonFieldType } from '@/strapi/types/helper';
import { showErrorToast, showInfoToast } from '@/ui/components/Toast';

import { AuthModal } from '../../_components/AuthModal';
import { LoginSection } from '../../_components/LoginSection';
import { LoginFormValues } from '../../_components/LoginSection';
import { useAuthErrorToastInterceptor } from './hooks';

type Props = LoginPageData & {
  translations?: JsonFieldType | null;
};

export const LoginModal = ({ title, subtitle, footerText, ...rest }: Props) => {
  const router = useRouter();
  const setUserQuery = useSetCurrentUserQuery();
  useAuthErrorToastInterceptor();

  async function loginUser(values: LoginFormValues) {
    try {
      const { isSignedIn, nextStep } = await signIn(values);
      if (isSignedIn) {
        const user = await fetchCurrentUser();
        setUserQuery(user);
        changeBrazeUser(user.sub, AuthMethod.USERNAME_PASSWORD);

        router.push('profile');
        return;
      }

      showInfoToast({ title: 'Login not completed!', description: `Next step: ${nextStep.signInStep}.` });
    } catch (error: any) {
      showErrorToast({ title: 'Login error!', description: error.message });
    }
  }

  return (
    <AuthModal footer={footerText} subtitle={subtitle} title={title}>
      <LoginSection {...rest} signupUrl="signup" onLogin={loginUser} onProviderClick={signInWithProviderInternal} />
    </AuthModal>
  );
};
