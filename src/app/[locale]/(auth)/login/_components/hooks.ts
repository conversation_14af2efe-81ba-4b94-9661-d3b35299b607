import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { showErrorToast } from '@/ui/components/Toast';

export function useAuthErrorToastInterceptor() {
  const searchParams = useSearchParams();
  const authError = searchParams.get('error_description');

  useEffect(() => {
    if (authError) {
      setTimeout(() => showErrorToast({ title: 'Identity provider login error!', description: authError }), 1000);
    }
  }, [authError]);
}
