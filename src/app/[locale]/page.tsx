import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { BlockRenderer } from '@/blocks/BlockRenderer';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getHomepageData, getHomepageSeo } from '@/strapi/api/single/homepage';

import { HomepageHero } from './_components/Hero/HomepageHero';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getHomepageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function Home({ params }: PageProps) {
  const locale = (await params).locale;
  const data = await getHomepageData(locale);

  if (!data) {
    return notFound();
  }

  const { hero, blocks } = data;

  return (
    <>
      {hero && <HomepageHero {...hero} />}
      {blocks.map((b) => (
        <BlockRenderer {...b} key={`${b.__component}-${b.id}`} />
      ))}
    </>
  );
}
