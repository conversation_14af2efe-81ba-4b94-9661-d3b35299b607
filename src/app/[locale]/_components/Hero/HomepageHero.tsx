import { MdSouth } from 'react-icons/md';

import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import { Timer } from '@/components/Timer';
import { HeroTournamentUpcomingBlockType } from '@/strapi/types/hero';
import { Only } from '@/ui/components/Only';
import { isVideoMedia } from '@/utils/media';

export function HomepageHero({
  backgroundMedia,
  dateHeading,
  logo,
  subtitle,
  countdownTimerDate,
  scrollForMoreText,
}: HeroTournamentUpcomingBlockType) {
  return (
    <section className="font-primary relative z-10 font-bold">
      <div className="absolute start-0 top-0 -z-10 size-full">
        {isVideoMedia(backgroundMedia.url) ? (
          <StrapiVideo autoPlay className="size-full object-cover" loop muted video={backgroundMedia} />
        ) : (
          <StrapiImage className="size-full object-cover" image={backgroundMedia} />
        )}
      </div>
      <div className="px-4 pt-[200px] pb-[100px] lg:px-8 lg:py-[126px] xl:py-[151px] 2xl:py-[274px]">
        <div className="mx-auto flex w-fit max-w-6xl flex-col items-center gap-5 md:gap-8">
          <div className="to-gold-primary rounded-[9px] bg-gradient-to-r from-[#4E442D] px-2.5 py-1.5">
            <p className="text-sm leading-tight tracking-tight text-white uppercase md:text-base">{dateHeading}</p>
          </div>
          <StrapiImage
            alt="esports world cup logo"
            className="aspect-[5/1] w-[630px] xl:w-[834px] 2xl:w-[1024px]"
            image={logo}
          />
          <div className="max-w-[802px] text-center">
            <p className="text-sm leading-tight text-white uppercase md:text-[21px]">{subtitle}</p>
          </div>
          {countdownTimerDate && <Timer targetDatetime={countdownTimerDate} />}
        </div>
      </div>
      {scrollForMoreText && (
        <Only for="xlAndAbove">
          <ScrollForMoreFloat text={scrollForMoreText} />
        </Only>
      )}
    </section>
  );
}

const ScrollForMoreFloat = ({ text }: { text: string }) => (
  <div className="absolute inset-x-0 bottom-7">
    <div className="mx-auto w-fit rounded-[18px] bg-white/15 p-1.5 ps-[25px] backdrop-blur-[32px]">
      <div className="flex items-center gap-8">
        <p className="text-xs leading-tight text-white uppercase">{text}</p>
        <div className="flex size-[42px] items-center justify-center overflow-hidden rounded-full bg-white/10">
          <div className="relative -top-[15px] flex flex-col items-center gap-4">
            {[...new Array(2)].map((_, i) => (
              <MdSouth className="animate-scroll-for-more-drop size-4 text-white" key={i} />
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);
