import { StrapiImage } from '@/components/StrapiImage';

import { FooterProps } from './Footer';
import {
  CallCenterInfo,
  ChooseLanguageSection,
  CopyrightSection,
  FollowUsSection,
  LinksSection,
  TakeEwcSection,
} from './sections';

export const MobileFooter = ({
  callCenterInfo,
  legalLinks,
  copyright,
  logo,
  chooseLanguageLabel,
  apps,
  socials,
  disabledLocales,
}: FooterProps) => {
  return (
    <div className="flex flex-col items-center gap-6 md:gap-8">
      {logo && <StrapiImage alt="Esports World Cup logo" className="h-[87px] w-[540px]" image={logo} />}
      <ChooseLanguageSection disabledLocales={disabledLocales} label={chooseLanguageLabel} />
      <div className="flex gap-6 max-md:flex-col max-md:items-center md:justify-between md:gap-[26px] md:self-stretch">
        <div className="flex flex-col gap-6 max-md:items-center md:gap-[26px]">
          {callCenterInfo && <CallCenterInfo {...callCenterInfo} />}
          {legalLinks && <LinksSection links={legalLinks} />}
        </div>
        <div className="flex flex-col gap-6 max-md:items-center md:justify-between md:gap-[26px]">
          {apps && <TakeEwcSection {...apps} />}
          {socials && <FollowUsSection {...socials} />}
        </div>
      </div>
      {copyright && <CopyrightSection text={copyright} />}
    </div>
  );
};
