import Image from 'next/image';

import { Locale } from '@/hooks/i18n/const';
import { FooterData } from '@/strapi/api/single/footer';
import AbstractBgImage from '@/ui/assets/images/abstract-background.png';
import { Only } from '@/ui/components/Only';

import { DesktopFooter } from './DesktopFooter';
import { MobileFooter } from './MobileFooter';

export type FooterProps = FooterData & { disabledLocales?: Locale[] };

export function Footer(props: FooterProps) {
  return (
    <div className="flex flex-col pb-18 lg:pb-0">
      <div className="h-3 bg-gradient-to-r from-[#4E442D] to-[#987C4B]" />
      <div className="bg-dark-default relative">
        <Image alt="" className="absolute inset-0 size-full object-cover" quality={65} src={AbstractBgImage} />
        <div className="to-dark-default absolute inset-0 size-full bg-gradient-to-r from-transparent" />
        <div className="relative mx-auto max-w-6xl px-4 py-10 lg:pt-[115px] lg:pb-20 xl:px-8">
          <Only fallback={<MobileFooter {...props} />} for="lgAndAbove">
            <DesktopFooter {...props} />
          </Only>
        </div>
      </div>
    </div>
  );
}
