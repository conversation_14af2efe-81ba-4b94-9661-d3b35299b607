'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useLayoutEffect, useState } from 'react';

import { Locale } from '@/hooks/i18n/const';
import { RedirectType } from '@/strapi/types/collection/redirect';
import { normalize, stripLocalePrefix } from '@/utils/browser';

export function RedirectHandler({
  redirects,
  locale,
  children,
}: {
  redirects: RedirectType[];
  locale: Locale;
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [shouldRender, setShouldRender] = useState(false);

  useLayoutEffect(() => {
    if (!pathname || redirects.length === 0) {
      setShouldRender(true);
      return;
    }

    const pathToMatch = normalize(stripLocalePrefix(pathname));

    const matchedRedirect = redirects.find(({ fromPath, enabled }) => {
      if (!enabled || !fromPath?.startsWith('/')) return false;

      return fromPath === pathToMatch;
    });

    if (matchedRedirect) {
      const { toPath } = matchedRedirect;
      let redirectTo = toPath;
      if (toPath?.startsWith('/') && !toPath.startsWith(`/${locale}`)) {
        redirectTo = `/${locale}${toPath}`;
      }

      window.location.replace(redirectTo as string);
    } else {
      setShouldRender(true);
    }
  }, [pathname, redirects, locale, router]);

  return shouldRender ? children : null;
}
