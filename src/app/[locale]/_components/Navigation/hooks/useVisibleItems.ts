import { useEffect, useRef, useState } from 'react';
import { useWindowSize } from 'usehooks-ts';

const DEFAULT_ITEM_HEIGHT = 76;

interface NavigationItem {
  icon: string;
  title: string;
  url: string;
}

export function useVisibleItems(items: NavigationItem[] | undefined) {
  const { height: windowHeight } = useWindowSize();
  const [maxVisibleItems, setMaxVisibleItems] = useState(items?.length ?? 7);
  const containerRef = useRef<HTMLDivElement>(null);
  const navigationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!items || !containerRef.current || !navigationRef.current) return;

    const calculateVisibleItems = () => {
      const navigationRect = navigationRef.current?.getBoundingClientRect();
      const containerRect = containerRef.current?.getBoundingClientRect();

      if (!navigationRect || !containerRect || !containerRef.current) return;

      // get sibling element to our nav items container
      const parent = containerRef.current.parentElement;
      if (!parent) return;

      let heightBeforeContainer = 0;
      let heightAfterContainer = 0;
      let isBeforeContainer = true;

      // loop through all sidebar elements to calculate heights before and after our container
      Array.from(parent.children).forEach((child) => {
        if (child === containerRef.current) {
          isBeforeContainer = false;
          return;
        }

        const childHeight = child.getBoundingClientRect().height;
        if (isBeforeContainer) {
          heightBeforeContainer += childHeight;
        } else {
          heightAfterContainer += childHeight;
        }
      });

      const availableHeight = navigationRect.height - heightBeforeContainer - heightAfterContainer;

      // get navigation item height
      const firstItem = containerRef.current.children[0];
      const itemHeight = firstItem ? firstItem.getBoundingClientRect().height : DEFAULT_ITEM_HEIGHT;

      const maxItems = Math.floor(availableHeight / itemHeight);
      const adjustedMaxItems = items.length > maxItems ? maxItems - 1 : maxItems;

      setMaxVisibleItems(Math.max(1, adjustedMaxItems));
    };

    calculateVisibleItems();

    //  observer to watch for container size changes
    const resizeObserver = new ResizeObserver(calculateVisibleItems);
    resizeObserver.observe(navigationRef.current);
    resizeObserver.observe(containerRef.current);

    // observe the parent element to catch changes in other containers
    if (containerRef.current.parentElement) {
      resizeObserver.observe(containerRef.current.parentElement);

      // observe all sibling elements for height changes
      Array.from(containerRef.current.parentElement.children).forEach((child) => {
        if (child !== containerRef.current) {
          resizeObserver.observe(child);
        }
      });
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [windowHeight, items]);

  const visibleItems = items?.slice(0, maxVisibleItems) || [];
  const hiddenItems = items?.slice(maxVisibleItems) || [];
  const hasHiddenItems = hiddenItems.length > 0;

  // consider memoizing this
  return {
    visibleItems,
    hiddenItems,
    hasHiddenItems,
    containerRef,
    navigationRef,
  };
}
