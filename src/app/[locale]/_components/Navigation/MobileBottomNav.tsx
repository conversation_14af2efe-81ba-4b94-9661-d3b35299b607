'use client';

import clsx from 'clsx';
import { AnimatePresence, motion } from 'motion/react';
import { RefObject, useRef } from 'react';
import { FaGlobeAmericas } from 'react-icons/fa';
import { GoTriangleDown } from 'react-icons/go';
import { MdClose, MdMoreHoriz } from 'react-icons/md';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';
import { NavigationData } from '@/strapi/api/single/navigation';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { Button } from '@/ui/components/Button';

import { localeTextMap } from '../../../../components/LanguageDropdown/const';
import { useCurrentLocale } from '../../../../hooks/i18n';
import { useReplaceLocale } from '../../../../hooks/i18n';
import { NavigationItem } from './NavigationItem';
import { ProfileNavigationItem } from './ProfileButton';

export function MobileBottomNav({
  data,
  siteConfig,
  disabledLocales,
  isProfileButtonEnabled = true,
}: {
  data: NavigationData;
  siteConfig: SiteConfigData | null;
  disabledLocales?: Locale[];
  isProfileButtonEnabled?: boolean;
}) {
  const [isOpen, toggleIsOpen, setIsOpen] = useToggle(false);

  const closeMobileMenu = () => {
    setIsOpen(false);
  };

  const highlightedItems = data.items && data.items.filter((item) => item.appearsOnMobileNavigation);
  const nonHighlightedItems = data.items && data.items.filter((item) => !item.appearsOnMobileNavigation);

  const getColumns = () => {
    const isMenuButtonPresent =
      highlightedItems && nonHighlightedItems && highlightedItems.length > 0 && nonHighlightedItems.length > 0;
    const isOnlyHighlightedButtons =
      highlightedItems && nonHighlightedItems && highlightedItems.length > 0 && nonHighlightedItems.length === 0;

    if (!data.items) return 0;
    if (isMenuButtonPresent) return `repeat(${highlightedItems?.length + 1}, minmax(0, 1fr))`;
    if (isOnlyHighlightedButtons) return `repeat(${highlightedItems?.length}, minmax(0, 1fr))`;
    return `repeat(1, minmax(0, 1fr))`;
  };

  return (
    <>
      <div
        className="shadow-mobile-navigation fixed bottom-0 z-90 min-h-18 w-full bg-white/80 px-2.5 backdrop-blur-[30px] lg:hidden"
        style={{ paddingBottom: 'env(safe-area-inset-bottom)' }}
      >
        <div
          className="grid h-full w-full gap-2.5"
          style={{
            gridTemplateColumns: getColumns(),
          }}
        >
          {highlightedItems &&
            highlightedItems.map((item, i) => {
              return (
                <NavigationItem icon={item.icon} key={i} title={item.title} url={item.url} onClick={closeMobileMenu} />
              );
            })}
          {nonHighlightedItems && nonHighlightedItems.length > 0 && (
            <MoreButton isOpen={isOpen} siteConfig={siteConfig} onClick={toggleIsOpen} />
          )}
        </div>
      </div>
      <AnimatePresence>
        {isOpen && (
          <MoreMenu
            data={data}
            disabledLocales={disabledLocales}
            isProfileButtonEnabled={isProfileButtonEnabled}
            onClick={closeMobileMenu}
          />
        )}
      </AnimatePresence>
    </>
  );
}

function MoreButton({
  isOpen,
  siteConfig,
  onClick,
}: {
  isOpen: boolean;
  siteConfig: SiteConfigData | null;
  onClick: () => void;
}) {
  const translations = siteConfig?.translations;

  return (
    <motion.div
      animate={{ backgroundColor: isOpen ? 'rgba(21, 21, 21, 1)' : 'rgba(21, 21, 21, 0)' }}
      className="my-1.5 grid cursor-pointer items-start justify-center justify-items-center gap-1 rounded-lg p-1.5"
      onClick={onClick}
    >
      <div className="h-6 w-6">
        {isOpen ? <MdClose className="text-2xl" color="#fff" /> : <MdMoreHoriz className="text-2xl" color="#151515" />}
      </div>
      <span className={clsx('text-dark-default text-[10px] leading-[1.1] capitalize', isOpen && 'text-white')}>
        {isOpen ? (translations?.['close'] ?? 'Close') : (translations?.['more'] ?? 'More')}
      </span>
    </motion.div>
  );
}

function MoreMenu({
  data,
  disabledLocales,
  onClick,
  isProfileButtonEnabled = false,
}: {
  data: NavigationData;
  disabledLocales?: Locale[];
  onClick: () => void;
  isProfileButtonEnabled?: boolean;
}) {
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref as RefObject<HTMLElement>, onClick);

  return (
    <motion.div
      animate={{ y: 0, opacity: 1 }}
      className="shadow-mobile-navigation fixed end-0 bottom-24 z-80 me-2.5 mb-2.5 h-fit min-w-[280px] rounded-2xl bg-[#EEEDEE] px-2.5 py-1.5 backdrop-blur-[30px] lg:hidden"
      exit={{ y: 20, opacity: 0 }}
      initial={{ y: 20, opacity: 0 }}
      ref={ref}
      style={{ marginBottom: 'env(safe-area-inset-bottom)' }}
      transition={{ bounce: 0, duration: 0.2 }}
    >
      <div className="flex flex-col">
        {data.items &&
          data.items
            .filter((item) => !item.appearsOnMobileNavigation)
            .map((item, i) => (
              <NavigationItem
                icon={item.icon as any}
                key={i}
                layout="row"
                title={item.title}
                url={item.url}
                onClick={() => {
                  onClick();
                  logButtonClickedEvent({
                    location: 'Mobile Bottom Navigation - More Menu',
                    button_name: `Navigation Item (${item.title})`,
                  });
                }}
              />
            ))}
        <LanguageDropdown disabledLocales={disabledLocales} onClick={onClick} />
        {isProfileButtonEnabled && <ProfileNavigationItem />}
        {data.buttons && (
          <div className="flex flex-col gap-1.5">
            {data.buttons.map((button) => (
              <div className="[&>a>button]:rounded-[14px] [&>a>button]:text-[13px]" key={button.id}>
                <Button
                  {...button}
                  brazeEventProperties={{
                    button_name: `Button (${button.text})`,
                    location: `Mobile Bottom Navigation - More Menu`,
                  }}
                  isFullWidth
                  onClick={onClick}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
}

function LanguageDropdown({ disabledLocales, onClick }: { disabledLocales?: Locale[]; onClick: () => void }) {
  const [isOpen, toggleIsOpen, setIsOpen] = useToggle(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref as RefObject<HTMLElement>, () => setIsOpen(false));

  const locale = useCurrentLocale();
  const replaceLocale = useReplaceLocale();
  const otherLocales = ALL_LOCALES.filter((l) => l !== locale && !disabledLocales?.includes(l));

  return (
    <div className="bg-white-dirty flex flex-col gap-1 rounded-lg">
      <div className="bg-gray-easy flex cursor-pointer items-center gap-3 p-3" onClick={toggleIsOpen}>
        <div className="size-6 p-[1.5px]">
          <FaGlobeAmericas className="text-dark-default size-full" />
        </div>
        <span className="font-base text-dark-default text-[13px] leading-[1.1] font-bold capitalize">
          {localeTextMap[locale]}
        </span>
        <GoTriangleDown className={`text-dark-default ms-auto transition ${isOpen ? 'rotate-180' : ''}`} size={24} />
      </div>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            animate={{ height: 'auto' }}
            className="overflow-hidden"
            exit={{ height: 0 }}
            initial={{ height: 0 }}
            layout
            transition={{ bounce: 0, duration: 0.1 }}
          >
            {otherLocales.map((l) => (
              <div
                className="bg-white-dirty flex cursor-pointer items-center gap-3 p-3"
                key={l}
                onClick={() => {
                  replaceLocale(l);
                  onClick();
                  logButtonClickedEvent({
                    location: 'Mobile Bottom Navigation - Language Dropdown',
                    button_name: `Change Language to (${localeTextMap[l]})`,
                  });
                }}
              >
                <div className="size-6 p-[1.5px]">
                  <FaGlobeAmericas className="text-dark-default size-full" />
                </div>
                <span className="font-base text-dark-default text-[13px] leading-[1.1] font-bold capitalize">
                  {localeTextMap[l]}
                </span>
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
