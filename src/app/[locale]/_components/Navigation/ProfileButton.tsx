'use client';

import clsx from 'clsx';
import { FaCircleUser } from 'react-icons/fa6';

import { LocalizedLink } from '@/components/LocalizedLink';
import { useCurrentUser } from '@/services/auth/hooks';

export const ProfileButton = () => {
  const { user } = useCurrentUser();

  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Main Navigation', button_name: user ? `Profile Link` : 'Login Link' }}
      className={clsx(
        'flex cursor-pointer flex-col items-center gap-1 px-3 py-4 transition-colors',
        user ? 'text-gold-primary bg-white' : 'bg-dark-default text-white',
      )}
      href={user ? '/profile' : '/login'}
    >
      <FaCircleUser className="size-6" />
      <p className="font-primary max-w-full truncate text-[10px] leading-[1.1] font-bold">
        {user ? user.preferred_username : 'Login'}
      </p>
    </LocalizedLink>
  );
};

export const ProfileNavigationItem = () => {
  const { user } = useCurrentUser();

  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Main Navigation', button_name: user ? `Profile Link` : 'Login Link' }}
      className={clsx(
        'flex cursor-pointer items-center gap-3 p-3 transition-colors',
        user ? 'text-gold-primary' : 'text-dark-default',
      )}
      href={user ? '/profile' : '/login'}
    >
      <FaCircleUser className="size-6 p-0.5" />
      <p className="font-base text-[13px] leading-[1.1] font-bold">{user ? user.preferred_username : 'Login'}</p>
    </LocalizedLink>
  );
};
