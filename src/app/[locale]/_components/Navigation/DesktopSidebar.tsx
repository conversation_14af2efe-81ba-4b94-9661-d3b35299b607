'use client';

import { RefObject, useRef } from 'react';
import { FaGlobeAmericas } from 'react-icons/fa';
import * as MdIcons from 'react-icons/md';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';
import { NavigationData } from '@/strapi/api/single/navigation';
import { Button } from '@/ui/components/Button';

import { localeTextMap } from '../../../../components/LanguageDropdown/const';
import { useCurrentLocale, useReplaceLocale } from '../../../../hooks/i18n';
import { SiteConfigData } from '../../../../strapi/api/single/siteConfig';
import { DesktopMoreMenu } from './DesktopMoreMenu';
import { useVisibleItems } from './hooks/useVisibleItems';
import { NavigationItem } from './NavigationItem';
import { ProfileButton } from './ProfileButton';

export function DesktopSidebar({
  data,
  siteConfig,
  disabledLocales,
  isProfileButtonEnabled = true,
}: {
  data: NavigationData;
  siteConfig: SiteConfigData | null;
  disabledLocales?: Locale[];
  isProfileButtonEnabled?: boolean;
}) {
  const [isMoreMenuOpen, toggleMoreMenu, setMoreMenuOpen] = useToggle(false);
  const { visibleItems, hiddenItems, hasHiddenItems, containerRef, navigationRef } = useVisibleItems(data.items);

  return (
    <nav className="fixed z-90 hidden h-[100dvh] items-center justify-center lg:flex" ref={navigationRef}>
      <div className="shadow-sidebar w-sidebar-width flex h-full flex-col items-center justify-between bg-white">
        {data.logo && (
          <LocalizedLink
            brazeEventProperties={{
              location: 'Desktop Sidebar Navigation - Top Logo',
              button_name: `Logo Link (${data.logo?.link})`,
            }}
            href={data.logo.link ?? '/'}
          >
            <StrapiImage
              alt={data.logo.title ?? 'EWC Logo'}
              className="h-auto w-full px-1.5 pt-8"
              image={data.logo.image}
            />
          </LocalizedLink>
        )}
        {data.items && (
          <div className="flex w-full flex-col px-1.5" ref={containerRef}>
            {visibleItems.map((item, i) => (
              <NavigationItem icon={item.icon as keyof typeof MdIcons} key={i} title={item.title} url={item.url} />
            ))}
            {hasHiddenItems && (
              <button
                className="hover:shadow-sidebar-item group/more-menu relative flex h-full flex-col items-center justify-center gap-1 rounded-2xl p-3 text-center"
                onClick={() => {
                  toggleMoreMenu();
                  logButtonClickedEvent({ location: 'Desktop Sidebar Navigation', button_name: `More Menu Button` });
                }}
              >
                <span className="material-symbols-outlined text-dark-default group-hover/more-menu:text-gold-primary text-[26px]!">
                  more_horiz
                </span>
                <span className="font-base text-dark-default text-[10px] leading-[1.1] font-bold capitalize">
                  {siteConfig?.translations?.['more'] ?? 'More'}
                </span>
                <DesktopMoreMenu isOpen={isMoreMenuOpen} items={hiddenItems} onClose={() => setMoreMenuOpen(false)} />
              </button>
            )}
          </div>
        )}
        <div className="relative flex w-full flex-col">
          <LanguageSelector disabledLocales={disabledLocales} />
          {isProfileButtonEnabled && <ProfileButton />}
          {data.buttons &&
            data.buttons.map((button) => {
              return (
                <div
                  className="[&>a>button]:min-h-[54px] [&>a>button]:rounded-none! [&>a>button]:px-4 [&>a>button]:py-2.5 [&>a>button]:text-center [&>a>button]:text-[13px] [&>a>button]:leading-[115%]"
                  key={button.id}
                >
                  <Button
                    {...button}
                    brazeEventProperties={{
                      button_name: `Button (${button.text})`,
                      location: `Desktop Sidebar Navigation`,
                    }}
                    isFullWidth
                  />
                </div>
              );
            })}
        </div>
      </div>
    </nav>
  );
}

export function LanguageSelector({ disabledLocales }: { disabledLocales?: Locale[] }) {
  const [isOpen, toggleIsOpen, setIsOpen] = useToggle(false);
  const ref = useRef<HTMLDivElement>(null);

  useOnClickOutside(ref as RefObject<HTMLElement>, () => setIsOpen(false));

  const locale = useCurrentLocale();
  const replaceLocale = useReplaceLocale();
  const otherLocales = ALL_LOCALES.filter((l) => l !== locale && !disabledLocales?.includes(l));

  return (
    <div className="relative" ref={ref}>
      <button
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label={`Change language from ${localeTextMap[locale]}`}
        className="bg-gray-easy flex w-full cursor-pointer flex-col items-center justify-center gap-1 px-3 py-2.5"
        onClick={toggleIsOpen}
      >
        <FaGlobeAmericas className="text-[#272727]" size={26} />
        <span className="font-primary text-dark-default text-[13px] leading-[115%] capitalize">
          {localeTextMap[locale]}
        </span>
      </button>
      {isOpen && (
        <div
          aria-orientation="vertical"
          className="absolute start-[calc(100%+10px)] top-0 flex min-w-[240px] flex-col gap-0.5 rounded-lg bg-white p-1 shadow-md"
          role="menu"
        >
          {otherLocales.map((l) => (
            <button
              className="hover:bg-white-dirty flex w-full cursor-pointer items-center gap-2 rounded-lg p-3 text-left transition-colors"
              key={l}
              role="menuitem"
              tabIndex={0}
              onClick={() => {
                replaceLocale(l);
                setIsOpen(false);
                logButtonClickedEvent({
                  location: 'Desktop Sidebar Navigation - Language Selector',
                  button_name: `Change Language to ${localeTextMap[l]} Button`,
                });
              }}
            >
              <FaGlobeAmericas className="text-[#272727]" size={21} />
              <span className="font-primary text-dark-default text-[10px] leading-[100%] font-bold capitalize">
                {localeTextMap[l]}
              </span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
