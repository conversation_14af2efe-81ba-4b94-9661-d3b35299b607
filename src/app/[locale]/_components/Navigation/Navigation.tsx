import { NavigationData } from '@/strapi/api/single/navigation';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';

import { Locale } from '../../../../hooks/i18n/const';
import { DesktopSidebar } from './DesktopSidebar';
import { MobileBottomNav } from './MobileBottomNav';

export function Navigation({
  data,
  siteConfig,
  disabledLocales,
}: {
  data: NavigationData | null;
  siteConfig: SiteConfigData | null;
  disabledLocales?: Locale[];
}) {
  if (!data) return null;

  return (
    <>
      <DesktopSidebar data={data} disabledLocales={disabledLocales} isProfileButtonEnabled siteConfig={siteConfig} />
      <MobileBottomNav data={data} disabledLocales={disabledLocales} isProfileButtonEnabled siteConfig={siteConfig} />
    </>
  );
}
