'use client';

import { AnimatePresence, motion } from 'motion/react';
import { useRef } from 'react';
import * as MdIcons from 'react-icons/md';
import { useOnClickOutside } from 'usehooks-ts';

import { NavigationItem } from './NavigationItem';

interface Props {
  items: Array<{
    icon: string;
    title: string;
    url: string;
  }>;
  isOpen: boolean;
  onClose: () => void;
}

export function DesktopMoreMenu({ items, isOpen, onClose }: Props) {
  const ref = useRef<HTMLDivElement | null>(null);

  useOnClickOutside(ref as React.RefObject<HTMLDivElement>, onClose);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          animate={{ y: 0, opacity: 1 }}
          className="shadow-sidebar-item absolute start-[calc(100%+10px)] bottom-0 z-50 h-fit min-w-[280px] rounded-2xl bg-white p-2"
          exit={{ y: 20, opacity: 0 }}
          initial={{ y: 20, opacity: 0 }}
          ref={ref}
          transition={{ bounce: 0, duration: 0.2 }}
        >
          <div className="flex flex-col">
            {items.map((item, i) => (
              <NavigationItem
                icon={item.icon as keyof typeof MdIcons}
                key={i}
                layout="row"
                title={item.title}
                url={item.url}
                onClick={onClose}
              />
            ))}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
