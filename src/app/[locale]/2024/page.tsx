import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getPreviousTournamentPageData, getPreviousTournamentPageSeo } from '@/strapi/api/single/previousTournament';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

import { ClubChampionshipTable, Hero, ProudWinnersGrid, WinnersShowcase } from './_components';
import { EwcRibbonBlock } from './_components/EwcRibbonBlock';
import NewsCards2024Block from './_components/NewsCards2024Block';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getPreviousTournamentPageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | 2024',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function Ewc2024Page({ params }: PageProps) {
  const locale = (await params).locale;
  const pageData = await getPreviousTournamentPageData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (!pageData) {
    return notFound();
  }

  return (
    <>
      <Hero />
      <div className="relative">
        <div className="absolute inset-x-0 -top-60 mx-auto w-full max-w-6xl px-4 xl:px-8">
          <WinnersShowcase {...pageData?.hero} />
        </div>
        <div className="h-[300px] md:h-110" />
        <ProudWinnersGrid {...pageData?.winners} translations={siteConfig?.translations} />
        <ClubChampionshipTable {...pageData?.clubChampionship} translations={siteConfig?.translations} />
      </div>
      <EwcRibbonBlock ribbon={pageData?.ribbon} />
      <NewsCards2024Block locale={locale} {...pageData?.news} />
      {/* <NewsletterBlock
        buttonText="subscribe now"
        title="wanna be the first one to know?"
        topText="Subscribe to our newsletter!"
      /> */}
    </>
  );
}
