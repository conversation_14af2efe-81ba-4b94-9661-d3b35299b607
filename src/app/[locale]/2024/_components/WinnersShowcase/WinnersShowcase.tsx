import clsx from 'clsx';
import Image from 'next/image';

import { PreviousTournamentHero } from '@/strapi/api/single/previousTournament/types';
import WinnersImage from '@/ui/assets/images/winners2024.png';

import FalconsLogo from '../assets/clubs/falcons.png';
import { Badge } from './Badge';
import { badges } from './const';

const borderStyles = 'rounded-lg md:rounded-xl lg:rounded-4xl';

type Props = Partial<PreviousTournamentHero>;

export const WinnersShowcase = ({ heading, description, pointsLabel, prizeLabel }: Props) => (
  <section className="relative">
    <div
      className={clsx('bg-dark-default h-[50vw] max-h-[540px]', 'flex w-full items-start justify-center', borderStyles)}
    >
      <Image
        alt="2024 winners with the EWC trophy"
        className={clsx('absolute inset-0 h-2/3 w-full object-cover object-center', borderStyles)}
        priority
        quality={25}
        src={WinnersImage}
      />
      <div className={clsx('absolute inset-0 bg-black/20', borderStyles)} />
      <Image
        alt=""
        className="relative mt-[25px] size-[64px] md:mt-10 md:size-[160px] lg:mt-11 lg:size-[210px]"
        quality={25}
        src={FalconsLogo}
      />
    </div>
    <div
      className={clsx(
        'absolute top-[114px] md:top-[240px] lg:top-[298px]',
        'flex w-full max-lg:flex-col',
        'divide-[#ddd] bg-white shadow-md max-lg:divide-y-[1px] lg:divide-x-[1px]',
        borderStyles,
      )}
    >
      <TeamDescriptionSection {...{ heading, description }} />
      <div className="flex grow divide-[#DDDDDD] max-lg:py-3 max-md:justify-around lg:flex-col lg:divide-y-[1px]">
        <MetricSection description={pointsLabel ?? 'CC Points'} metric="5,665" />
        <MetricSection description={prizeLabel ?? 'Year Of the tournament'} metric="$ 7,000,000" />
      </div>
    </div>
  </section>
);

const TeamDescriptionSection = ({ heading, description }: Pick<Props, 'heading' | 'description'>) => (
  <div className="flex grow flex-col gap-5 p-4 md:p-8 lg:p-10">
    <div className="flex flex-col gap-2 max-md:items-center">
      <p className="text-button-big">{heading ?? 'EWC 2024 Club Champions'}</p>
      <p className="text-h2 text-radial-gold">Team Falcons</p>
    </div>
    <div className="flex flex-wrap gap-1 max-md:justify-center">
      {badges.map((b, i) => (
        <Badge key={i} text={b.text} variant={b.variant} />
      ))}
    </div>
    <p className="text-dark-default text-paragraph max-md:text-center">
      {description ??
        `Team Falcons is a Saudi esports organization. Founded in 2017, the organization fields rosters in multiple
      esports. The org has garnered a reputation for pursuing high-profile players and coaches, being able to
      successfully construct "superteams" in the process`}
    </p>
  </div>
);

interface MetricSectionProps {
  metric: string;
  description: string;
}

const MetricSection = ({ metric, description }: MetricSectionProps) => (
  <div className="flex items-center md:px-8 lg:flex-1">
    <div className="flex flex-col gap-2 max-md:items-center">
      <p
        className={clsx(
          'font-primary text-radial-gold leading-[1] font-bold',
          'tracking-[-4%] whitespace-nowrap uppercase',
          'text-[21px] md:text-[32px] lg:text-[48px] xl:text-[64px]',
        )}
      >
        {metric}
      </p>
      <p className="text-xs leading-normal font-normal lg:text-sm">{description}</p>
    </div>
  </div>
);
