import clsx from 'clsx';

export type Variant = 'light' | 'medium' | 'dark';

interface BadgeProps {
  text: string;
  variant: Variant;
}

export const Badge = ({ text, variant }: BadgeProps) => (
  <p
    className={clsx(
      'font-primary rounded-sm px-[5px] py-[3px] text-[10px] leading-[1.1] font-bold whitespace-nowrap text-white uppercase',
      {
        'bg-dark-default': variant === 'dark',
        'bg-[#616161]': variant === 'medium',
        'bg-[#949494]': variant === 'light',
      },
    )}
  >
    {text}
  </p>
);
