import React from 'react';

import { RibbonBlock } from '@/blocks/RibbonBlock';
import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { RibbonType } from '@/strapi/types/collection/ribbon';
import EwcLogoIcon from '@/ui/assets/icons/logos/ewc.svg';
import EwcAcronymLogoIcon from '@/ui/assets/icons/logos/ewc-acronym.svg';
import { Marquee } from '@/ui/components/Marquee';

export const EwcRibbonBlock = ({ ribbon }: { ribbon?: RibbonType | null }) => {
  if (ribbon) {
    return <RibbonBlock __component="" id={0} ribbon={ribbon} section={null} translations={undefined} />;
  }
  return (
    <BlockSectionWrapper>
      <div
        className="mt-[107px] py-6 md:mt-0"
        style={{ backgroundImage: 'linear-gradient(90deg, #4E442D 0%, #987C4B 100%)' }}
      >
        <Marquee duration="20s" repeat={7}>
          <EwcLogoIcon className="text-white" height={30} width={151} />
          <EwcAcronymLogoIcon className="text-white" height={30} width={151} />
        </Marquee>
      </div>
    </BlockSectionWrapper>
  );
};
