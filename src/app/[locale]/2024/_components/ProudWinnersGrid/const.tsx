import { WinnerCard } from '@/strapi/api/single/previousTournament/types';

import {
  AllianceLogo,
  Alpha7Logo,
  BdsLogo,
  FalconsLogo,
  FazeLogo,
  FreecsLogo,
  GiantsLogo,
  GladiatorsLogo,
  KplLogo,
  KuaiLogo,
  LiquidLogo,
  LunaLogo,
  NatusLogo,
  OmegaLogo,
  RaccoonsLogo,
  RedlineLogo,
  SoniqsLogo,
  T1Logo,
  WolvesLogo,
  XsetLogo,
} from '../assets/clubs';
import { DefaultGameImage } from '../assets/games/images';
import {
  ApexLegendsLogo,
  CodMwLogo,
  CodWzLogo,
  Cs2Logo,
  Dota2Logo,
  Fc24Logo,
  FortniteLogo,
  FreefireLogo,
  HokLogo,
  LolLogo,
  MlbbLogo,
  MlbbwLogo,
  OverwatchLogo,
  PubgLogo,
  PubgMobileLogo,
  RainbowSixLogo,
  RennsportLogo,
  RocketLeagueLogo,
  StarcraftLogo,
  StreetfighterLogo,
  TekkenLogo,
  TftLogo,
} from '../assets/games/logos';
import {
  ApexLegendsThumbnail,
  CodMwThumbnail,
  CodWzThumbnail,
  Cs2Thumbnail,
  Dota2Thumbnail,
  Fc24Thumbnail,
  FortniteThumbnail,
  FreefireThumbnail,
  HokThumbnail,
  LolThumbnail,
  MlbbThumbnail,
  MlbbwThumbnail,
  OverwatchThumbnail,
  PubgMobileThumbnail,
  PubgThumbnail,
  RainbowSixThumbnail,
  RennsportThumbnail,
  RocketLeagueThumbnail,
  StarcraftThumbnail,
  StreetfighterThumbnail,
  TekkenThumbnail,
  TftThumbnail,
} from '../assets/games/thumbnails';

const WINNERS_DATA = [
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <ApexLegendsThumbnail />,
    gameLogo: <ApexLegendsLogo />,
    winnerLogo: AllianceLogo,
    game: 'Apex Legends',
    winnerName: 'Alliance',
    prizePool: '$600,000',
    participatingTeams: '40',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <CodMwThumbnail />,
    gameLogo: <CodMwLogo />,
    winnerLogo: FazeLogo,
    game: 'Call of Duty: Modern Warfare 3',
    winnerName: 'Atlanta Faze',
    prizePool: '$600,000',
    participatingTeams: '16',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <CodWzThumbnail />,
    gameLogo: <CodWzLogo />,
    winnerLogo: FalconsLogo,
    game: 'Call of Duty: Warzone',
    winnerName: 'Team Falcons',
    prizePool: '$200,000',
    participatingTeams: '21',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <Cs2Thumbnail />,
    gameLogo: <Cs2Logo />,
    winnerLogo: NatusLogo,
    game: 'Counter Strike 2',
    winnerName: 'Natus Vincere',
    prizePool: '$400,000',
    participatingTeams: '15',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <Dota2Thumbnail />,
    gameLogo: <Dota2Logo />,
    winnerLogo: GladiatorsLogo,
    game: 'Dota 2',
    winnerName: 'Gaimin Gladiators',
    prizePool: '$1,150,000',
    participatingTeams: '20',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <Fc24Thumbnail />,
    gameLogo: <Fc24Logo />,
    winnerLogo: LunaLogo,
    game: 'FC24',
    prizePool: '$300,000',
    winnerName: 'Jafonso',
    participatingTeams: '16',
    isPvp: true,
    // winnerName: 'Luna Galaxy',
    // participatingTeams: '13',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <FortniteThumbnail />,
    gameLogo: <FortniteLogo />,
    winnerLogo: XsetLogo,
    game: 'Fortnite',
    winnerName: 'XSET',
    prizePool: '$400,000',
    participatingTeams: '16',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <FreefireThumbnail />,
    gameLogo: <FreefireLogo />,
    winnerLogo: FalconsLogo,
    game: 'Free Fire',
    winnerName: 'Team Falcons',
    prizePool: '$300,000',
    participatingTeams: '18',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <HokThumbnail />,
    gameLogo: <HokLogo />,
    winnerLogo: KplLogo,
    game: 'Honor of Kings',
    winnerName: 'KPL Dream Team',
    prizePool: '$1,000,000',
    participatingTeams: '12',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <LolThumbnail />,
    gameLogo: <LolLogo />,
    winnerLogo: T1Logo,
    game: 'League of Legends',
    winnerName: 'T1',
    prizePool: '$300,000',
    participatingTeams: '8',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <MlbbThumbnail />,
    gameLogo: <MlbbLogo />,
    winnerLogo: GiantsLogo,
    game: 'MLBB',
    winnerName: 'Selangor Red Giants',
    prizePool: '$1,000,000',
    participatingTeams: '23',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <MlbbwThumbnail />,
    gameLogo: <MlbbwLogo />,
    winnerLogo: OmegaLogo,
    game: 'MLBB Women',
    winnerName: 'Smart Omega Express',
    prizePool: '$180,000',
    participatingTeams: '12',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <OverwatchThumbnail />,
    gameLogo: <OverwatchLogo />,
    winnerLogo: RaccoonsLogo,
    game: 'Overwatch 2',
    winnerName: 'Crazy Raccoon',
    prizePool: '$400,000',
    participatingTeams: '16',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <PubgThumbnail />,
    gameLogo: <PubgLogo />,
    winnerLogo: SoniqsLogo,
    game: 'PUBG Battlegrounds',
    winnerName: 'Soniqs',
    prizePool: '$700,000',
    participatingTeams: '24',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <PubgMobileThumbnail />,
    gameLogo: <PubgMobileLogo />,
    winnerLogo: Alpha7Logo,
    game: 'PUBG Mobile',
    winnerName: 'Alpha 7 Esports',
    prizePool: '$487,000',
    participatingTeams: '28',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <RainbowSixThumbnail />,
    gameLogo: <RainbowSixLogo />,
    winnerLogo: BdsLogo,
    game: 'Rainbow Six Siege',
    winnerName: 'Team BDS',
    prizePool: '$750,000',
    participatingTeams: '16',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <RennsportThumbnail />,
    gameLogo: <RennsportLogo />,
    winnerLogo: RedlineLogo,
    game: 'Rennsport - Players',
    winnerName: 'Kevin Siggy',
    participatingTeams: '12',
    prizePool: '$25,000',
    isPvp: true,
    //   "winnerName": "Team Redline",
    //   "participatingTeams": "12",
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <RennsportThumbnail />,
    gameLogo: <RennsportLogo />,
    winnerLogo: RedlineLogo,
    game: 'Rennsport - Teams',
    winnerName: 'Team Redline',
    prizePool: '$140,000',
    participatingTeams: '12',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <RocketLeagueThumbnail />,
    gameLogo: <RocketLeagueLogo />,
    winnerLogo: BdsLogo,
    game: 'Rocket League',
    winnerName: 'Team BDS',
    prizePool: '$200,000',
    participatingTeams: '16',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <StarcraftThumbnail />,
    gameLogo: <StarcraftLogo />,
    winnerLogo: LiquidLogo,
    game: 'Starcraft 2',
    winnerName: 'Clem',
    participatingTeams: '18',
    prizePool: '$400,000',
    isPvp: true,
    //   "winnerName": "Team Liquid",
    //   "participatingTeams": "14",
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <StreetfighterThumbnail />,
    gameLogo: <StreetfighterLogo />,
    winnerLogo: KuaiLogo,
    game: 'Street Fighter 6',
    winnerName: 'Xiaohai',
    participatingTeams: '32',
    prizePool: '$300,000',
    isPvp: true,
    //   "winnerName": "Kuaishou Gaming",
    //   "participatingTeams": "26",
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <TftThumbnail />,
    gameLogo: <TftLogo />,
    winnerLogo: WolvesLogo,
    game: 'Teamfight Tactics',
    winnerName: 'Wolves Esports',
    prizePool: '$200,000',
    participatingTeams: '16',
  },
  {
    gameImage: DefaultGameImage,
    gameLogoThumbnail: <TekkenThumbnail />,
    gameLogo: <TekkenLogo />,
    winnerLogo: FreecsLogo,
    game: 'Tekken 8',
    winnerName: 'Ulsan',
    prizePool: '$300,000',
    participatingTeams: '32',
    isPvp: true,
    // winnerName: 'Kwangdong Freecs',
    // participatingTeams: '21',
  },
];

export function getWinnersDataWithContentImages(cards?: WinnerCard[] | null) {
  if (!cards) {
    return WINNERS_DATA;
  }

  const dataWithStrapiImages = WINNERS_DATA.map((d) => {
    const gameCard = cards.find((c) => c.gameId === d.game);
    if (gameCard?.thumbnailImage) {
      return { ...d, gameStrapiThumbnail: gameCard.thumbnailImage };
    }
    return d;
  });

  return dataWithStrapiImages;
}
