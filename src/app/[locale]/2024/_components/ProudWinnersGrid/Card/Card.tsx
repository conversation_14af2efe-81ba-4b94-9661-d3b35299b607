import { StaticImageData } from 'next/image';
import Image from 'next/image';

import { JsonFieldType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';

import { CardHeader } from './CardHeader';

export interface CardProps {
  gameImage: StaticImageData;
  gameLogo: React.ReactElement;
  gameLogoThumbnail: React.ReactElement;
  gameStrapiThumbnail?: MediaType;
  winnerLogo: StaticImageData;
  winnerName: string;
  prizePool: string;
  participatingTeams: string;
  isPvp?: boolean;
  translations?: JsonFieldType | null;
}

export const Card = ({
  gameLogo,
  winnerLogo,
  winnerName,
  prizePool,
  participatingTeams,
  isPvp,
  translations,
  ...rest
}: CardProps) => (
  <div className="flex flex-col rounded-3xl shadow-[0px_12px_16px_0px_#0000000F]">
    <CardHeader {...rest} winnerLogo={winnerLogo} />
    <div className="flex flex-col items-center gap-1 px-4 py-2">
      <div className="text-dark-default w-[174px] overflow-visible">{gameLogo}</div>
      <div className="flex w-full flex-col divide-y-[1px] divide-[#E4E4E4]">
        <CardSection
          logo={winnerLogo}
          text={winnerName}
          title={isPvp ? (translations?.['winner'] ?? 'Winner') : (translations?.['winners'] ?? 'Winners')}
        />
        <CardSection text={prizePool} title={translations?.['prizePool'] ?? 'Prize Pool'} />
        <CardSection
          text={participatingTeams}
          title={
            isPvp
              ? (translations?.['participatingPlayers'] ?? 'Participating Players')
              : (translations?.['participatingClubs'] ?? 'Participating Clubs')
          }
        />
      </div>
    </div>
  </div>
);

interface CardSectionProps {
  title: string;
  text: string;
  logo?: StaticImageData;
}

const CardSection = ({ title, text, logo }: CardSectionProps) => (
  <div className="flex items-center justify-between gap-2 py-2.5">
    <p className="text-dark-default text-sm leading-[1] font-medium capitalize">{title}</p>
    <div className="flex items-center gap-1 overflow-x-hidden">
      {logo && (
        <div className="size-6">
          <Image alt="" className="size-full object-contain" quality={25} src={logo} />
        </div>
      )}
      <p className="font-primary text-dark-default truncate text-end text-sm leading-[1] font-bold">{text}</p>
    </div>
  </div>
);
