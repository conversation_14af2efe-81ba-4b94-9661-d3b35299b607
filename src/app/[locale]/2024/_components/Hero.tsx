export const Hero = () => (
  <div className="bg-dark-default text-white-dirty relative h-[400px]">
    <video autoPlay className="absolute inset-0 size-full object-cover" loop muted playsInline>
      <source src="/assets/video/promo.webm" type="video/webm" />
      <source src="/assets/video/promo.mp4" type="video/mp4" />
      Your browser does not support HTML video.{' '}
      <a className="text-gold-primary cursor-pointer underline" href="/promo.mp4" target="__blank">
        Download the video from the link instead
      </a>
      .
    </video>
    <div className="pointer-events-none absolute inset-0 bg-gradient-to-b from-[#15151500] to-[#151515]" />
  </div>
);
