import { NewsCardsBlock } from '@/blocks/NewsCardsBlock';
import { Locale } from '@/hooks/i18n/const';
import { fetchNewsArticles } from '@/strapi/api/collection/news';
import { PreviousTournamentSection } from '@/strapi/api/single/previousTournament/types';
import { BlockSectionWrapperType } from '@/strapi/types/helper';

type Props = Partial<PreviousTournamentSection> & { locale: Locale };

export default async function NewsCards2024Block({ locale, title, subtitle, link }: Props) {
  const data = await fetchNewsArticles(locale, { pagination: { start: 0, limit: 4 } });
  if (!data) {
    return null;
  }

  return (
    <NewsCardsBlock
      __component=""
      featuredArticles={data.articles}
      id={1}
      section={
        {
          id: 0,
          isSectionDividerVisible: true,
          link: link ?? { text: 'see all news', url: '/news', id: 0, image: null },
          subtitle: subtitle ?? 'EWC Unlocked: Everything You Need to Know!',
          title: title ?? 'Latest from EWC',
          styleConfig: null,
          className: 'mx-auto px-4 py-[50px] lg:py-[90px] xl:px-8 max-w-6xl',
          headerImage: null,
          isBlockVisible: true,
        } as BlockSectionWrapperType
      }
      translations={undefined}
    />
  );
}
