import { medalsMap, MedalType } from './const';

export const MedalsSection = () => (
  <div className="flex justify-evenly py-4 md:justify-around md:max-lg:px-20 lg:justify-center">
    <MedalWithTotal points={2} type="bronze" />
    <MedalWithTotal points={4} type="silver" />
    <MedalWithTotal points={4} type="gold" />
  </div>
);

interface Props {
  type: MedalType;
  points: number;
}

const MedalWithTotal = ({ type, points }: Props) => {
  const MedalComponent = medalsMap[type];
  return (
    <div className="flex items-center gap-2 px-5">
      <MedalComponent className="size-8" />
      <p className="font-primary text-dark-default text-[21px] leading-[1] font-bold">{points}</p>
    </div>
  );
};
