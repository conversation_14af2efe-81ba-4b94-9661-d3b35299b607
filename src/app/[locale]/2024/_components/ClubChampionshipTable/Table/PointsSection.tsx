import { GAME_POINTS_DATA, medalsMap, MedalType } from './const';
import { TableProps } from './Table';

export const PointsSection = ({ translations }: TableProps) => (
  <div className="px-4 pt-3 pb-[30px] md:px-6">
    <div className="divide-gray-easy flex flex-col justify-between divide-y-[1px]">
      {GAME_POINTS_DATA.map((d) => (
        <GameWithPoints
          gameLogo={d.gameLogo}
          key={d.game}
          medal={d.medal as MedalType}
          points={d.points}
          pointsLabel={translations?.['ccPoints']}
        />
      ))}
    </div>
  </div>
);

interface Props {
  gameLogo: React.ReactElement;
  medal: MedalType;
  points: string;
  pointsLabel?: string | null;
}

const GameWithPoints = ({ gameLogo, medal, points, pointsLabel }: Props) => {
  const MedalComponent = medalsMap[medal];
  return (
    <div className="flex items-center justify-between gap-2 py-3">
      <div className="w-[106px]">{gameLogo}</div>
      <div className="flex items-center gap-4">
        <div className="font-primary flex items-center gap-4 leading-[1] font-bold whitespace-nowrap uppercase">
          <p className="text-gray-dark text-[10px]">{pointsLabel ?? 'cc points'}</p>
          <p className="text-dark-default text-[20px]">{points}</p>
        </div>
        <MedalComponent className="size-6" />
      </div>
    </div>
  );
};
