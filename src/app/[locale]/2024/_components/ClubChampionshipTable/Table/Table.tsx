import { JsonFieldType } from '@/strapi/types/helper';

import { MedalsSection } from './MedalsSection';
import { OverviewSection } from './OverviewSection';
import { PointsSection } from './PointsSection';

export interface TableProps {
  translations?: JsonFieldType | null;
}

export const Table = ({ translations }: TableProps) => {
  return (
    <div className="rounded-lg bg-white md:rounded-2xl lg:rounded-4xl">
      <div className="flex flex-col divide-y-[1px] divide-[#D9D9D9]">
        <OverviewSection translations={translations} />
        <MedalsSection />
        <PointsSection translations={translations} />
      </div>
    </div>
  );
};
