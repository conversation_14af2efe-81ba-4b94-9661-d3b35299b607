import BronzeMedalIcon from '@/ui/assets/icons/bronze-medal.svg';
import GoldMedalIcon from '@/ui/assets/icons/gold-medal.svg';
import SilverMedalIcon from '@/ui/assets/icons/silver-medal.svg';

import {
  ApexLegendsLogo,
  CodWzLogo,
  Dota2Logo,
  Fc24Logo,
  FreefireLogo,
  MlbbLogo,
  MlbbwLogo,
  OverwatchLogo,
  RocketLeagueLogo,
  TekkenLogo,
} from '../../assets/games/logos';

export type MedalType = 'gold' | 'silver' | 'bronze';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const medalsMap: Record<MedalType, any> = {
  gold: BronzeMedalIcon,
  silver: SilverMedalIcon,
  bronze: GoldMedalIcon,
};

export const GAME_POINTS_DATA = [
  {
    game: 'CoD Warzone',
    gameLogo: <CodWzLogo />,
    points: '1,000',
    medal: 'gold',
  },
  {
    game: 'Free Fire',
    gameLogo: <FreefireLogo />,
    points: '1,000',
    medal: 'gold',
  },
  {
    game: 'Apex Legends',
    gameLogo: <ApexLegendsLogo />,
    points: '600',
    medal: 'silver',
  },
  {
    game: 'MLBB',
    gameLogo: <MlbbLogo />,
    points: '600',
    medal: 'silver',
  },
  {
    game: 'Tekken 8',
    gameLogo: <TekkenLogo />,
    points: '600',
    medal: 'silver',
  },
  {
    game: 'Rocket League',
    gameLogo: <RocketLeagueLogo />,
    points: '600',
    medal: 'silver',
  },
  {
    game: 'Dota 2',
    gameLogo: <Dota2Logo />,
    points: '350',
    medal: 'bronze',
  },
  {
    game: 'EA FC 24',
    gameLogo: <Fc24Logo />,
    points: '275',
    medal: 'bronze',
  },
  {
    game: 'MLBB Women',
    gameLogo: <MlbbwLogo />,
    points: '275',
    medal: 'bronze',
  },
  {
    game: 'Overwatch 2',
    gameLogo: <OverwatchLogo />,
    points: '275',
    medal: 'bronze',
  },
];
