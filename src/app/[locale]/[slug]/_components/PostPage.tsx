import { Post<PERSON>lockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { PostPageHeader } from '@/blocks/shared/Post/PostPageHeader';
import { PageType } from '@/strapi/types/collection/page';

export const PostPage = ({ header, blocks }: PageType) => {
  return (
    <div className="flex flex-col items-center gap-8 py-30 lg:gap-12">
      {header && (
        <div className="flex px-4 lg:px-8 xl:px-35 2xl:justify-center">
          <div className="w-full xl:max-w-[1158px]">
            <PostPageHeader {...header} />
          </div>
        </div>
      )}
      {blocks.map((b) => (
        <PostBlockRenderer {...b} key={`${b.__component}-${b.id}`} />
      ))}
    </div>
  );
};
