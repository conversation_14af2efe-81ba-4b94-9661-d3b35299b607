'use client';
import { formatDate } from 'date-fns';

import { BlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import TopNavigation from '@/components/TopNavigation';
import { useCurrentLocale } from '@/hooks/i18n';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { PageType } from '@/strapi/types/collection/page';
import { TopNavigationType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import { PostPageHeaderType } from '@/strapi/types/shared';
import { Button } from '@/ui/components/Button';
import { isVideoMedia } from '@/utils/media';

export const ImpactPage = ({ background, header, blocks, disableBgGradient }: PageType) => {
  const topNavigationItems = blocks
    .map((b) => b.section?.navigation)
    .filter((item): item is TopNavigationType => item !== null && item !== undefined);

  return (
    <div className="relative">
      {background && <Background background={background} disableBgGradient={disableBgGradient} />}
      <TopNavigation navigationItems={topNavigationItems} />
      <div className="relative flex flex-col pt-[140px] lg:pt-[174px]">
        {header && <Header {...header} />}
        {blocks.map((b) => (
          <BlockRenderer {...b} key={`${b.__component}-${b.id}`} />
        ))}
      </div>
    </div>
  );
};

const Background = ({ background, disableBgGradient }: { background: MediaType; disableBgGradient: boolean }) => {
  return (
    <div className="bg-dark-default absolute top-0 h-100 w-full">
      {isVideoMedia(background.url) ? (
        <StrapiVideo autoPlay className="absolute inset-0 h-full w-full object-cover" loop muted video={background} />
      ) : (
        <StrapiImage className="absolute inset-0 h-full w-full object-cover" image={background} />
      )}
      {!disableBgGradient && (
        <div className="from-dark-default/0 to-dark-default absolute inset-0 bg-gradient-to-b to-55%" />
      )}
    </div>
  );
};

const Header = ({ cover, logo, date, dateTo, title, intro, buttons }: PostPageHeaderType) => {
  const locale = useDateLocale();
  const currentLocale = useCurrentLocale();
  const isChineseLocale = currentLocale === 'zh';

  return (
    <section className="relative mx-auto w-full max-w-6xl rounded-4xl px-4 xl:px-8">
      {cover && (
        <div className="absolute inset-0 h-2/5 w-full px-4 md:h-2/3 xl:px-8">
          <StrapiImage className="h-full w-full rounded-t-4xl object-cover" image={cover} />
        </div>
      )}
      {logo && (
        <div className="relative flex w-full justify-center py-8 md:py-[72px] lg:py-[88px] 2xl:py-[110px]">
          <StrapiImage className="aspect-[5] w-3/4" image={logo} />
        </div>
      )}
      <div className="relative flex flex-col items-center gap-5 rounded-4xl bg-white p-10">
        <div className="flex flex-col items-center gap-2">
          {date && (
            <p className="text-button-big text-black">
              {dateTo
                ? // If both dates exist, show range
                  isChineseLocale
                  ? `${formatDate(date, 'MMM do', { locale })} - ${formatDate(dateTo, 'MMM do', { locale })}`
                  : `${formatDate(date, 'MMM d', { locale })} - ${formatDate(dateTo, 'MMM d yyyy', { locale })}`
                : // If only start date exists, show single date
                  isChineseLocale
                  ? formatDate(date, 'MMM do yyyy', { locale })
                  : formatDate(date, 'MMM d yyyy', { locale })}
            </p>
          )}
          {title && <h1 className="text-h1 text-dark-default text-center">{title}</h1>}
        </div>

        {intro && <p className="text-paragraph text-dark-default text-center">{intro}</p>}
        {buttons && (
          <div className="flex gap-4">
            {buttons.map((b) => (
              <Button
                {...b}
                brazeEventProperties={{
                  button_name: `Button (${b.text})`,
                  location: `Page (${title}) - Header`,
                }}
                key={b.id}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};
