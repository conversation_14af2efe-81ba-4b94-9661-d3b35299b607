import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getPageData, getPageSeo } from '@/strapi/api/collection/page';
import { PageLayoutType } from '@/strapi/types/collection/page';

import { ImpactPage, PostPage } from './_components';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await getPageSeo(locale, slug);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function Page({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;

  const pageData = await getPageData(locale, slug);

  if (!pageData) {
    return notFound();
  }

  if (pageData.type === PageLayoutType.IMPACT) {
    return <ImpactPage {...pageData} />;
  }
  return <PostPage {...pageData} />;
}
