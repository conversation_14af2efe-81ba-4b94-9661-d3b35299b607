import { addDays, formatISO, getWeek, parseISO } from 'date-fns';

import { IndicatorStatusType } from '@/app/[locale]/schedule/_components/GridContent/shared/GameEventStatusIndicator';

function generateDatesBetween(startDate: string, endDate: string) {
  const dates = [];

  const currentDate = new Date(startDate);
  const end = new Date(endDate);

  while (currentDate <= end) {
    const dateString = formatISO(currentDate, { representation: 'date' });
    dates.push(dateString);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

export interface WeekDates {
  week: number;
  dates: string[];
}

function mapDatesByWeek(dates: string[], numberOfWeeks?: number): WeekDates[] {
  const firstDate = dates[0];
  const firstWeekAbsolute = getWeek(parseISO(firstDate), { weekStartsOn: 1 });

  const datesByWeek: WeekDates[] = [];
  for (const date of dates) {
    const dateWeek = getOrdinalWeek(firstWeekAbsolute, getWeek(parseISO(date), { weekStartsOn: 1 }), numberOfWeeks);
    const weekMap = datesByWeek.find((dw) => dw.week === dateWeek);

    if (weekMap) {
      weekMap.dates.push(date);
      continue;
    }

    datesByWeek.push({ week: dateWeek, dates: [date] });
  }

  return datesByWeek;
}

const getOrdinalWeek = (firstWeekAbsolute: number, week: number, numberOfWeeks?: number) => {
  const diff = week - firstWeekAbsolute + 1;
  return numberOfWeeks !== undefined ? Math.min(diff, numberOfWeeks) : diff;
};

export function generateWeekDatesBetween(startDate: string, endDate: string, numberOfWeeks?: number) {
  const dates = generateDatesBetween(startDate, endDate);
  const weekDates = mapDatesByWeek(dates, numberOfWeeks);

  return weekDates;
}

export function getEventStatus(startDate: string | null, endDate: string | null): IndicatorStatusType | null {
  if (!startDate || !endDate) return null;

  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (start > now) return 'upcoming';
  if (end < now) return 'completed';
  if (start <= now && end >= now) return 'live';

  return null;
}

export function isDateInRange(date: Date, rangeStart: string, rangeEnd: string) {
  const start = new Date(rangeStart);
  const end = new Date(rangeEnd);

  return date >= start && date <= end;
}

export function getGridColumnsForEvent(
  start: string,
  end: string,
): {
  gridColumnStart: string;
  gridColumnEnd: string;
} {
  const startDate = formatISO(new Date(start), { representation: 'date' });
  const endDate = formatISO(addDays(new Date(end), 1), { representation: 'date' });

  return {
    gridColumnStart: `D${startDate}`,
    gridColumnEnd: `D${endDate}`,
  };
}

export function getScheduleGridColumnWidth(strapiWidth: number | null): number {
  if (strapiWidth === null || strapiWidth < 100) {
    return 150;
  }
  return strapiWidth;
}

export function filterEventsByDateRange<
  T extends {
    startDate?: string | null;
    endDate?: string | null;
    startDateTime?: string | null;
    endDateTime?: string | null;
  },
>(events: T[], tournamentStartDate: string, tournamentEndDate: string): T[] {
  return events.filter((event) => {
    const startDate = event.startDateTime ?? event.startDate;
    const endDate = event.endDateTime ?? event.endDate;
    return (
      startDate &&
      endDate &&
      new Date(startDate) <= new Date(tournamentEndDate) &&
      new Date(endDate) >= new Date(tournamentStartDate)
    );
  });
}
