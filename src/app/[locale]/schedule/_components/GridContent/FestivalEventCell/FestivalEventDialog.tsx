import { FestivalType } from '@/strapi/types/collection/festival';
import { JsonFieldType } from '@/strapi/types/helper';

import { BaseEventDialog } from '../shared/BaseEventDialog';

type Props = {
  festival: FestivalType;
  onClose: () => void;
  translations: JsonFieldType | null;
};

export const FestivalEventDialog = ({ festival, onClose, translations }: Props) => {
  const { startDateTime, endDateTime, startDate, endDate, posterImage, description, ticketsUrl, icon } = festival;

  return (
    <BaseEventDialog
      content={description}
      endDate={endDateTime ?? endDate}
      isGameEvent={false}
      keyArt={posterImage}
      logo={icon}
      startDate={startDateTime ?? startDate}
      ticketsUrl={ticketsUrl}
      title={festival.title}
      translations={translations}
      onClose={onClose}
    />
  );
};
