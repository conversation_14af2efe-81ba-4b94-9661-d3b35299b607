import { MdPinDrop } from 'react-icons/md';

import { JsonFieldType } from '@/strapi/types/helper';

import { ScheduleGridSingleItem } from './ScheduleGridSingleItem';

export const ScheduleGridLegend = ({ translations }: { translations: JsonFieldType | null }) => {
  return (
    <div className="flex flex-wrap gap-2 pt-3">
      <ScheduleGridSingleItem
        color="bg-gray-dark"
        label={translations?.['lastChanceQualifiers'] ?? 'Last Chance Qualifiers'}
      />
      <ScheduleGridSingleItem
        color="bg-dark-default"
        label={translations?.['scheduleFilter.festivals'] ?? 'Festivals'}
      />
      <ScheduleGridSingleItem color="bg-red-accent" label={translations?.['liveEvents'] ?? 'Live Events'} />
      <ScheduleGridSingleItem color="bg-gold-primary" label={translations?.['finals'] ?? 'Finals'} />
      <ScheduleGridSingleItem icon={<MdPinDrop />} label={translations?.['eventVenue'] ?? 'Event Venue'} />
    </div>
  );
};
