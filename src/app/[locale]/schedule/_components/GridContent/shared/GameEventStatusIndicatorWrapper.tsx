import { EventType } from '@/strapi/types/helper/eventType';

import { GameEventStatusIndicator, IndicatorStatusType } from './GameEventStatusIndicator';

type GameEventStatusIndicatorWrapperProps = {
  cardType: EventType | 'Festival';
  eventStatus: IndicatorStatusType | null;
  label?: string;
  className?: string;
};

const STATUS_STYLES: Record<EventType | 'Festival', Partial<Record<IndicatorStatusType, string>>> = {
  Standard: {
    completed: 'bg-gray text-black',
    upcoming: 'bg-black text-white',
    live: 'bg-red-accent text-white',
  },
  'Last Chance Qualifier': {
    completed: 'bg-white text-black',
    upcoming: 'bg-black text-white',
    live: 'bg-red-accent text-white',
  },
  Final: {
    completed: 'bg-white text-gold-primary',
    upcoming: 'bg-white text-[#987C4B] shadow-sm',
    live: 'bg-red-accent text-white',
  },
  Festival: {
    completed: 'bg-gray-darker text-white',
    upcoming: 'bg-white text-black',
    live: 'bg-red-accent text-white',
  },
};

export function GameEventStatusIndicatorWrapper({
  cardType,
  eventStatus,
  label,
  className = '',
  ...rest
}: GameEventStatusIndicatorWrapperProps) {
  if (!eventStatus || !label) return null;

  const style = STATUS_STYLES[cardType]?.[eventStatus];
  if (!style) return null;

  return <GameEventStatusIndicator className={`${className} ${style}`} status={eventStatus} {...rest} label={label} />;
}
