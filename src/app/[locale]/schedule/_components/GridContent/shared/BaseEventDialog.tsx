import clsx from 'clsx';
import { formatDate } from 'date-fns';
import { isSameDay } from 'date-fns/isSameDay';
import { MdClose } from 'react-icons/md';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { StrapiImage } from '@/components/StrapiImage';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { logButtonClickedEvent } from '@/services/braze';
import { JsonFieldType, RichTextContentType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import { Button } from '@/ui/components/Button';

export type BaseEventDialogProps = {
  title: string | null;
  isGameEvent: boolean;
  startDate: string | null;
  endDate: string | null;
  content: RichTextContentType | null;
  logo: MediaType | null;
  keyArt: MediaType | null;
  ticketsUrl: string | null;
  onClose: () => void;
  translations: JsonFieldType | null;
  actions?: {
    primary?: {
      text: string;
      link: string;
      variant?: 'gold' | 'glassy';
    };
    secondary?: {
      text: string;
      link: string;
      variant?: 'gold' | 'glassy';
    };
  };
};

export const BaseEventDialog = ({
  title,
  isGameEvent,
  startDate,
  endDate,
  content,
  logo,
  keyArt,
  ticketsUrl,
  onClose,
  translations,
  actions,
}: BaseEventDialogProps) => {
  const locale = useDateLocale();

  const formatEventDate = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    const startTime = startDate.toLocaleTimeString(locale.code, {
      hour: '2-digit',
      minute: '2-digit',
    });
    const endTime = endDate.toLocaleTimeString(locale.code, {
      hour: '2-digit',
      minute: '2-digit',
    });

    if (isSameDay(startDate, endDate)) {
      return `${formatDate(startDate, 'MMM dd, yyyy')} ${startTime} - ${endTime}`;
    }

    return `${formatDate(startDate, 'MMM dd, yyyy')} ${startTime} - ${formatDate(endDate, 'MMM dd, yyyy')} ${endTime}`;
  };

  const buttonLocation = `Schedule Grid - ${isGameEvent ? 'Game' : 'Festival'} Event Dialog (${title})`;
  return (
    <div className={clsx('p-8 md:w-[395px]', 'cursor-default shadow-[0px_16px_64px_0px_#0000000A]')}>
      {keyArt && <BackgroundImage image={keyArt} />}
      <div className="absolute inset-0 h-full w-full overflow-hidden rounded-lg bg-[linear-gradient(197.48deg,_rgba(21,_21,_21,_0)_3.12%,_rgba(21,_21,_21,_0.9)_31.96%,_#151515_72.27%)] md:rounded-[20px]" />
      <div className="relative flex flex-col gap-8">
        <div className="flex flex-col gap-10">
          <div className="flex flex-col gap-1">
            <div className="flex items-center justify-between gap-4">
              {startDate && endDate && (
                <p className="font-primary text-base leading-[1] font-bold text-white">
                  {formatEventDate(startDate, endDate)}
                </p>
              )}
              <button
                className="ms-auto cursor-pointer rounded-lg bg-white p-1.5"
                onClick={() => {
                  onClose();
                  logButtonClickedEvent({ location: buttonLocation, button_name: `Close Button` });
                }}
              >
                <MdClose className="size-7" />
              </button>
            </div>
            {logo && (
              <div className="h-10 w-fit max-w-[129px]">
                <StrapiImage className="block h-full w-full object-contain" image={logo} />
              </div>
            )}
          </div>
          {content && (
            <div className="max-h-[220px] overflow-y-auto md:max-h-[400px]">
              <RichTextContent content={content} paragraphClassName="text-s text-white" />
            </div>
          )}
        </div>
        <ActionButtons
          actions={actions}
          location={buttonLocation}
          ticketsUrl={ticketsUrl}
          translations={translations}
        />
      </div>
    </div>
  );
};

type ActionButtonsProps = Pick<BaseEventDialogProps, 'actions' | 'ticketsUrl' | 'translations'> & { location: string };

const ActionButtons = ({ location, actions, ticketsUrl, translations }: ActionButtonsProps) => {
  if (!actions?.primary && !actions?.secondary && !ticketsUrl) {
    return null;
  }

  return (
    <div className="flex gap-3">
      {actions?.secondary && (
        <Button
          brazeEventProperties={{ button_name: `Secondary Button`, location }}
          isFullWidth
          link={actions.secondary.link}
          text={actions.secondary.text}
          variant={actions.secondary.variant ?? 'glassy'}
        />
      )}
      {(actions?.primary || ticketsUrl) && (
        <Button
          brazeEventProperties={{ button_name: `Primary Button`, location }}
          isFullWidth
          link={actions?.primary?.link ?? ticketsUrl ?? ''}
          text={actions?.primary?.text ?? translations?.buyTickets ?? 'buy tickets'}
          variant={actions?.primary?.variant ?? 'gold'}
        />
      )}
    </div>
  );
};

const BackgroundImage = ({ image }: { image: MediaType }) => {
  return (
    <>
      <StrapiImage className="absolute inset-0 size-full rounded-lg object-cover md:rounded-[20px]" image={image} />
      <div
        className="absolute inset-0 size-full rounded-lg md:rounded-[20px]"
        style={{
          background: 'linear-gradient(to top, rgba(21,21,21,1) 0%, rgba(21,21,21,0.9) 50%,  rgba(21,21,21,0) 85%',
        }}
      />
    </>
  );
};
