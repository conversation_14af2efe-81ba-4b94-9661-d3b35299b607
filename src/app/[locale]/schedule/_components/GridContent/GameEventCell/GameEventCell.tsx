import { GameType } from '@/strapi/types/collection/game';
import { GameEventType } from '@/strapi/types/collection/gameEvent';
import { JsonFieldType } from '@/strapi/types/helper';

import { BaseGameEventCell, MobileDialogDataType } from './BaseGameEventCell';

export interface Props {
  gameEvent: GameEventType;
  game: GameType | null;
  translations: JsonFieldType | null;
  openMobileDialog: (mobileDialogData: MobileDialogDataType) => void;
}
export function GameEventCell(props: Props) {
  const eventType = props.gameEvent.eventType;

  switch (eventType) {
    case 'Final':
      return (
        <BaseGameEventCell
          {...props}
          backgroundStyle="radial-gradient(100.83% 199.44% at 100% 0%, #F2C575 0%, #987C4B 55.84%, #4E442D 100%)"
          cardClassName="rounded-t-sm"
          containerHeight="91px"
          iconBgClass="bg-white"
          textColorClass="text-white"
          useIconFinals
        />
      );
    case 'Last Chance Qualifier':
      return (
        <BaseGameEventCell
          {...props}
          backgroundStyle="#d9d9d9"
          iconBgClass="bg-dark-default"
          textColorClass="text-black"
          topBorder={<div className="h-1 shrink-0 rounded-t-sm bg-[#d9d9d9]" />}
        />
      );
    case 'Standard':
    default:
      return (
        <BaseGameEventCell
          {...props}
          backgroundStyle="white"
          game={props?.game}
          gameEvent={props?.gameEvent}
          iconBgClass="bg-dark-default"
          textColorClass="text-black"
          topBorder={
            <div
              className="h-1 shrink-0 rounded-t-sm"
              style={{ background: props?.gameEvent?.game?.gradientHex ?? 'white' }}
            />
          }
        />
      );
  }
}
