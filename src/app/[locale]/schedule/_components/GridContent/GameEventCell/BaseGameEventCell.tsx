'use client';

import clsx from 'clsx';
import { RefObject, useRef } from 'react';
import { MdPinDrop } from 'react-icons/md';
import { useOnClickOutside, useToggle } from 'usehooks-ts';

import { GameEventStatusIndicatorWrapper } from '@/app/[locale]/schedule/_components/GridContent/shared/GameEventStatusIndicatorWrapper';
import { getEventStatus } from '@/app/[locale]/schedule/_components/utils';
import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { StrapiImage } from '../../../../../../components/StrapiImage';
import { FestivalType } from '../../../../../../strapi/types/collection/festival';
import { GameEventType } from '../../../../../../strapi/types/collection/gameEvent';
import { GameEventDialog } from './GameEventDialog';

export type MobileDialogDataType = {
  game: GameType | null;
  gameEvent: GameEventType | null;
  festival: FestivalType | null;
  translations: JsonFieldType | null;
};

interface BaseGameEventCellProps {
  gameEvent: GameEventType;
  game: GameType | null;
  translations: JsonFieldType | null;
  openMobileDialog: (data: MobileDialogDataType) => void;
  topBorder?: React.ReactNode;
  backgroundStyle: string;
  textColorClass: string;
  iconBgClass: string;
  useIconFinals?: boolean;
  containerHeight?: string;
  cardClassName?: string;
}

export function BaseGameEventCell({
  game,
  gameEvent,
  translations,
  openMobileDialog,
  topBorder,
  backgroundStyle,
  textColorClass,
  iconBgClass,
  useIconFinals = false,
  containerHeight = '87px',
  cardClassName,
}: BaseGameEventCellProps) {
  const [isDialogOpen, toggleIsDialogOpen, setIsDialogOpen] = useToggle();
  const { isSm } = useScreenType();
  const eventStatus = getEventStatus(
    gameEvent.startDateTime ?? gameEvent.startDate,
    gameEvent.endDateTime ?? gameEvent.endDate,
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const icon = useIconFinals ? game?.iconFinals : game?.icon;

  useOnClickOutside(containerRef as RefObject<HTMLDivElement>, () => setIsDialogOpen(false));

  const toggleIsDialogOpenHandler = () => {
    if (isSm) {
      openMobileDialog({ game, festival: null, gameEvent, translations });
    } else {
      toggleIsDialogOpen();
    }
  };

  return (
    <div className="relative cursor-pointer" ref={containerRef}>
      <div className="flex h-full cursor-pointer flex-col overflow-visible pe-3" onClick={toggleIsDialogOpenHandler}>
        {topBorder}
        <div
          className={clsx('grow rounded-b-sm p-[7px] pt-[5px]', cardClassName)}
          style={{ background: backgroundStyle, height: containerHeight }}
        >
          <div className="relative h-full w-full">
            {!game?.hideIcon && icon && (
              <div className={clsx('absolute end-1 top-1 size-6 rounded-sm', iconBgClass)}>
                <StrapiImage alt="" className="w-auto p-[3px]" image={icon} />
              </div>
            )}

            <div
              className={clsx(
                'flex h-full flex-col justify-between gap-1 leading-[1]',
                !gameEvent.venue && 'justify-center',
              )}
            >
              {gameEvent.venue && (
                <div className="flex items-center gap-0.5 text-[10px]">
                  <MdPinDrop className={clsx('align-middle !text-xs', textColorClass)} />
                  <span className={clsx('font-base font-bold', textColorClass)}>{gameEvent.venue}</span>
                </div>
              )}

              {gameEvent.title && (
                <p className={clsx('mt-auto mb-1 line-clamp-3 text-[13px] font-extrabold', textColorClass)}>
                  {gameEvent.title}
                </p>
              )}

              <div className="flex items-center justify-between">
                {!gameEvent.hideGameName && game?.title && (
                  <p className={clsx('text-[10px] font-bold', textColorClass)}>{game.title}</p>
                )}

                {eventStatus && (
                  <div className="ms-auto">
                    <GameEventStatusIndicatorWrapper
                      cardType={gameEvent.eventType}
                      eventStatus={eventStatus}
                      label={translations?.[eventStatus] ?? eventStatus}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className={clsx(
          'fixed end-8 bottom-24 z-10 transition-opacity lg:bottom-8',
          isDialogOpen ? 'opacity-100' : 'opacity-0',
        )}
      >
        {game && isDialogOpen && !isSm && (
          <GameEventDialog
            game={game}
            gameEvent={gameEvent}
            translations={translations}
            onClose={() => setIsDialogOpen(false)}
          />
        )}
      </div>
    </div>
  );
}
