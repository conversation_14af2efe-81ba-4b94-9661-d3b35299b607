import { WeekDates } from './utils';

interface Props {
  gridWidth: string;
  weekDates: WeekDates[];
  targetedWeek: number;
}

export const WeekTimeline = ({ gridWidth, weekDates, targetedWeek }: Props) => {
  const { startingWidth, targetedWidth, endingWidth } = getColoredWidthsForTargetedWeek(weekDates, targetedWeek);
  return (
    <div className="flex" style={{ width: gridWidth }}>
      <div className="h-2 bg-[#DFDFDF]" style={{ width: `${startingWidth}%` }} />
      <div className="bg-dark-default h-2" style={{ width: `${targetedWidth}%` }} />
      <div className="h-2 bg-[#DFDFDF]" style={{ width: `${endingWidth}%` }} />
    </div>
  );
};

function getColoredWidthsForTargetedWeek(weekDates: WeekDates[], targetedWeek: number) {
  const percentageByDay = 100 / weekDates.flatMap((wd) => wd.dates).length;

  let startingWidth = 0;
  let targetedWidth = 0;
  let endingWidth = 0;

  for (const wd of weekDates) {
    if (wd.week < targetedWeek) {
      startingWidth += percentageByDay * wd.dates.length;
    } else if (wd.week === targetedWeek) {
      targetedWidth += percentageByDay * wd.dates.length;
    } else {
      endingWidth += percentageByDay * wd.dates.length;
    }
  }

  return { startingWidth, targetedWidth, endingWidth };
}
