'use client';

import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

import { ScheduleGridLegend } from '@/app/[locale]/schedule/_components/GridContent/ScheduleGridLegend/ScheduleGridLegend';
import { useDragToScroll } from '@/components/ScrollWithFadeWrapper/useDragToScroll';
import { GameEventType } from '@/strapi/types/collection/gameEvent';
import { JsonFieldType } from '@/strapi/types/helper';
import { WeekTicketsLinkType } from '@/strapi/types/helper/weekTickets';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { ToggleGroup } from '../../../../components/ToggleGroup';
import { FestivalType } from '../../../../strapi/types/collection/festival';
import { GridContent } from './GridContent';
import { FestivalEventDialog } from './GridContent/FestivalEventCell/FestivalEventDialog';
import { MobileDialogDataType } from './GridContent/GameEventCell/BaseGameEventCell';
import { GameEventDialog } from './GridContent/GameEventCell/GameEventDialog';
import { generateWeekDatesBetween, getScheduleGridColumnWidth } from './utils';
import { WeekLabels } from './WeekLabels';

export type ScheduleFilter = 'all' | 'games' | 'festival';

interface Props {
  tournamentStartDate: string;
  tournamentEndDate: string;
  gameEvents: GameEventType[];
  festivals: FestivalType[];
  weekTickets: WeekTicketsLinkType[];
  translations: JsonFieldType | null;
  title: string | null;
  subtitle: string | null;
  timezoneInfo: string | null;
  gridColumnWidth: number | null;
}

export const ScheduleGrid = ({
  tournamentStartDate,
  tournamentEndDate,
  gameEvents,
  weekTickets,
  translations,
  title,
  subtitle,
  timezoneInfo,
  festivals,
  gridColumnWidth,
}: Props) => {
  const [filter, setFilter] = useState<ScheduleFilter>('all');
  const dayColumnWidth = getScheduleGridColumnWidth(gridColumnWidth);

  //todo set selected week based on today
  const [isMobileDialogOpen, setIsMobileDialogOpen] = useState(false);
  const [selectedMobileData, setSelectedMobileData] = useState<MobileDialogDataType | null>(null);
  const { isSm } = useScreenType();

  const scrollContainerRef = useRef<HTMLDivElement>(null);
  useDragToScroll(scrollContainerRef);

  const weekDates = generateWeekDatesBetween(tournamentStartDate, tournamentEndDate, weekTickets.length);
  const openMobileDialog = (mobileData: MobileDialogDataType) => {
    setSelectedMobileData(mobileData);
    setIsMobileDialogOpen(true);
  };

  const closeMobileDialog = () => {
    setSelectedMobileData(null);
    setIsMobileDialogOpen(false);
  };

  useEffect(() => {
    const html = document.documentElement;
    if (isMobileDialogOpen && isSm) {
      document.body.classList.add('overflow-hidden');
      html.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
      html.classList.remove('overflow-hidden');
    }
  }, [isMobileDialogOpen]);

  return (
    <>
      <div
        className={clsx(
          'flex grow flex-col gap-4 overflow-hidden py-15 lg:gap-8',
          isMobileDialogOpen && isSm && 'opacity-30',
        )}
      >
        <div className="flex justify-between gap-4 px-4 max-lg:flex-col lg:px-8">
          <div className="text-dark-default flex flex-col gap-1">
            {title && <p className="text-h1">{title}</p>}
            {subtitle && <p className="text-paragraph">{subtitle}</p>}
            {timezoneInfo && (
              <p className="font-primary text-gray-dark text-[10px] font-bold uppercase">{timezoneInfo}</p>
            )}
            <ScheduleGridLegend translations={translations} />
          </div>

          <div className="mt-auto">
            <ToggleGroup
              filters={[
                { value: 'all', label: translations?.['scheduleFilter.showAll'] ?? 'show all' },
                { value: 'games', label: translations?.['scheduleFilter.games'] ?? 'games' },
                { value: 'festival', label: translations?.['scheduleFilter.festivals'] ?? 'festivals' },
              ]}
              selectedFilter={filter}
              onFilterSelect={(f) => setFilter(f as ScheduleFilter)}
            />
          </div>
        </div>
        <div className="flex grow flex-col">
          <div
            className="hide-scrollbar flex grow flex-col gap-5 overflow-x-auto overflow-y-visible px-4 lg:px-8"
            ref={scrollContainerRef}
          >
            <div className="relative w-max grow">
              <WeekLabels
                dayColumnWidth={dayColumnWidth}
                translations={translations}
                weekDates={weekDates}
                weekTickets={weekTickets}
              />

              <GridContent
                dayColumnWidth={dayColumnWidth}
                festivals={festivals}
                filter={filter}
                gameEvents={gameEvents}
                openMobileDialog={openMobileDialog}
                translations={translations}
                weekDates={weekDates}
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        {selectedMobileData && isMobileDialogOpen && isSm && (
          <div className="fixed bottom-0 z-10 mx-4 flex h-full items-center">
            <div className="relative">
              {selectedMobileData.game && selectedMobileData.gameEvent && (
                <GameEventDialog
                  game={selectedMobileData.game}
                  gameEvent={selectedMobileData.gameEvent}
                  translations={selectedMobileData.translations}
                  onClose={closeMobileDialog}
                />
              )}
              {selectedMobileData.festival && (
                <FestivalEventDialog
                  festival={selectedMobileData.festival}
                  translations={selectedMobileData.translations}
                  onClose={closeMobileDialog}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};
