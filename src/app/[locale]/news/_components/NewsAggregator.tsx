'use client';

import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import { useTransition } from 'react';

import { NewsThumbnailCard } from '@/blocks/NewsThumbnailCard';
import { SearchInput } from '@/components/SearchInput';
import { ToggleGroup } from '@/components/ToggleGroup';
import { useCurrentLocale } from '@/hooks/i18n';
import { NewsArticleType } from '@/strapi/types/collection/news';
import { Paging } from '@/ui/components/Paging/Paging';

import { JsonFieldType } from '../../../../strapi/types/helper';

// !TODO: add filters to enum/const
export type NewsFilter = 'all' | 'latest' | 'archived';

interface Props {
  searchValue: string;
  articles: NewsArticleType[];
  currentPage: number;
  pages: number;
  translations?: JsonFieldType | null;
  filter: NewsFilter;
}

export const NewsAggregator = ({ searchValue, articles, currentPage, pages, translations, filter }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const locale = useCurrentLocale();

  const updateUrl = (newFilter: NewsFilter, search?: string) => {
    startTransition(() => {
      const params = new URLSearchParams();
      if (search) params.set('search', search);
      if (newFilter !== 'all') params.set('filter', newFilter);
      router.push(params.toString() ? `?${params.toString()}` : '/news');
    });
  };

  return (
    <div className="flex flex-col gap-5 md:gap-16">
      <div className="flex justify-between gap-5 max-lg:flex-col md:gap-8">
        <ToggleGroup
          filters={[
            { value: 'all', label: translations?.showAll ?? 'show all' },
            { value: 'latest', label: translations?.latest2025 ?? 'latest (2025)' },
            { value: 'archived', label: translations?.archived2024 ?? 'archived (2024)' },
          ]}
          selectedFilter={filter}
          onFilterSelect={(f) => {
            const newFilter = f as NewsFilter;
            updateUrl(newFilter, searchValue);
          }}
        />
        <div className="lg:w-1/2">
          <SearchInput
            analyticsLocation="News Aggregator - Search"
            initialValue={searchValue}
            onSearch={(s) => updateUrl(filter, s)}
          />
        </div>
      </div>
      <div
        className={clsx('grid grid-cols-1 gap-x-5 gap-y-8 md:grid-cols-3 lg:grid-cols-4', { 'opacity-50': isPending })}
      >
        {articles.map((c, i) => (
          <NewsThumbnailCard {...c} key={i} />
        ))}
      </div>
      <Paging
        currentPage={currentPage}
        mode="url"
        pages={pages}
        pathname={`/${locale}/news`}
        queryParams={{
          ...(searchValue ? { search: searchValue } : {}),
          ...(filter !== 'all' ? { filter } : {}),
        }}
      />
    </div>
  );
};
