'use client';
import { format } from 'date-fns';

import { StrapiImage } from '@/components/StrapiImage';
import { useCurrentLocale } from '@/hooks/i18n';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { GameType } from '@/strapi/types/collection/game';
import { Button } from '@/ui/components/Button';

export function CompetitionCard({ game, siteConfig }: { game: GameType; siteConfig: SiteConfigData | null }) {
  const translations = siteConfig?.translations ?? {};
  const locale = useDateLocale();
  const currentLocale = useCurrentLocale();
  const isChineseLocale = currentLocale === 'zh';

  return (
    <div className="flex w-full flex-col items-center justify-between overflow-hidden rounded-sm bg-white shadow-md md:rounded-2xl lg:rounded-3xl">
      <div className="flex flex-col items-center">
        {game.keyArt && <StrapiImage className="h-20 object-cover" image={game.keyArt} />}
        {game.gradientHex && <div className="h-1 w-full shrink-0" style={{ background: game.gradientHex }} />}
        <div className="mt-4 flex h-[50px] max-w-[200px] justify-center px-6">
          {game.logoDark && <StrapiImage className="h-full w-full object-contain" image={game.logoDark} />}
        </div>
        <div className="flex w-full flex-col p-6 text-center">
          {game.tournamentStart && game.tournamentEnd && (
            <div className="border-b-gray flex flex-col items-center justify-between py-1 md:flex-row md:py-2 md:not-last:border-b">
              <span className="text-dark-default text-sm font-normal">{translations['startEnd'] ?? 'Start - End'}</span>
              <span className="text-dark-default text-sm font-bold">
                {isChineseLocale
                  ? `${format(new Date(game.tournamentStart), 'MMM do', { locale })} - ${format(new Date(game.tournamentEnd), 'MMM do', { locale })}`
                  : `${format(new Date(game.tournamentStart), 'MMM dd', { locale })} - ${format(new Date(game.tournamentEnd), 'dd, yyyy', { locale })}`}
              </span>
            </div>
          )}
          {game.prizePool && (
            <div className="border-b-gray flex flex-col items-center justify-between py-1 md:flex-row md:py-2 md:not-last:border-b">
              <span className="text-dark-default text-sm font-normal">{translations['prizePool'] ?? 'Prize Pool'}</span>
              <span className="text-dark-default text-sm font-bold">{game.prizePool}</span>
            </div>
          )}
          {game.competingTeams && (
            <div className="border-b-gray flex flex-col items-center justify-between py-1 md:flex-row md:py-2 md:not-last:border-b">
              <span className="text-dark-default text-sm font-normal">
                {translations['participatingClubs'] ?? 'Participating Clubs'}
              </span>
              <span className="text-dark-default text-sm font-bold">{game.competingTeams}</span>
            </div>
          )}
          {game.competingPlayers && (
            <div className="border-b-gray flex flex-col items-center justify-between py-1 md:flex-row md:py-2 md:not-last:border-b">
              <span className="text-dark-default text-sm font-normal">
                {translations['participatingPlayers'] ?? 'Participating Players'}
              </span>
              <span className="text-dark-default text-sm font-bold">{game.competingPlayers}</span>
            </div>
          )}
          {game.numQualifiers && (
            <div className="border-b-gray flex flex-col items-center justify-between py-1 md:flex-row md:py-2 md:not-last:border-b">
              <span className="text-dark-default text-sm font-normal">
                {translations['qualifiers'] ?? 'Qualifiers'}
              </span>
              <span className="text-dark-default text-sm font-bold">{game.numQualifiers}</span>
            </div>
          )}
        </div>
      </div>
      <div className="flex w-full flex-col gap-2 px-1.5 pb-1.5 md:px-6 md:pb-6">
        {game.isLinkingToGamePageEnabled && game.slug && (
          <Button
            brazeEventProperties={{
              button_name: `Primary Button (Visit Game Page)`,
              location: `Competitions Aggregator - Card (${game.title})`,
            }}
            isFullWidth
            link={`/competitions/${game.slug}`}
            text={translations['visitGamePage'] ?? 'Visit game page'}
            variant="primary"
          />
        )}
        {game.ticketsUrl && (
          <Button
            brazeEventProperties={{
              button_name: `Secondary Button (Buy Tickets)`,
              location: `Competitions Aggregator - Card (${game.title})`,
            }}
            isFullWidth
            link={game.ticketsUrl}
            text={translations['buyTickets'] ?? 'Buy tickets'}
            variant="secondary"
          />
        )}
      </div>
    </div>
  );
}
