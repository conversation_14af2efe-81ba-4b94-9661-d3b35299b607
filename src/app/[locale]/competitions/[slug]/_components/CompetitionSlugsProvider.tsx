'use client';

import { createContext, useContext } from 'react';

import { CompetitionSlug } from '@/strapi/types/collection/game';

const CompetitionSlugsContext = createContext<CompetitionSlug[] | undefined>(undefined);

export const CompetitionSlugsProvider = ({
  competitionSlugs,
  children,
}: React.PropsWithChildren<{ competitionSlugs: CompetitionSlug[] }>) => {
  return <CompetitionSlugsContext.Provider value={competitionSlugs}>{children}</CompetitionSlugsContext.Provider>;
};

export function useCompetitionSlugs() {
  const state = useContext(CompetitionSlugsContext);

  if (!state) {
    throw new Error('useCompetitionSlugs must be used within a CompetitionSlugsProvider!');
  }

  return state;
}
