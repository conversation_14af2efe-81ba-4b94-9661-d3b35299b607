import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { BlockRenderer } from '@/blocks/BlockRenderer/BlockRenderer';
import { GameHeader } from '@/blocks/game/GameHeader';
import TopNavigation from '@/components/TopNavigation';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getGamePageData, getGamePageSeo } from '@/strapi/api/collection/game';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import { TopNavigationType } from '@/strapi/types/helper';

import { LiveStateHero } from '../../../../blocks/hero/LiveStateHero';
import { CompetitionSlugsProvider } from './_components/CompetitionSlugsProvider';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale<{ slug: string }>): Promise<Metadata> {
  const { locale, slug } = await params;
  const seo = await getGamePageSeo(locale, slug);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup',
    description: seo?.metaDescription ?? 'This is Esports World Cup',
  };
}

export default async function GamePage({ params }: PageProps<{ slug: string }>) {
  const { locale, slug } = await params;
  const pageData = await getGamePageData(locale, slug);
  const siteConfig = await getSiteConfig(locale);

  if (!pageData) {
    return notFound();
  }

  const { hero, blocks, competitionSlugs } = pageData;
  const navigationItems = blocks
    .map((b) => b.section?.navigation)
    .filter((item): item is TopNavigationType => item !== null && item !== undefined);

  return (
    <div className="flex flex-col">
      <CompetitionSlugsProvider competitionSlugs={competitionSlugs}>
        <div className="relative flex pb-8 lg:pb-12">
          <TopNavigation navigationItems={navigationItems} />
          {hero ? <LiveStateHero {...hero} /> : <GameHeader {...pageData} translations={siteConfig?.translations} />}
        </div>
        {blocks.map((b) => (
          <BlockRenderer {...b} key={`${b.__component}-${b.id}`} translations={siteConfig?.translations} />
        ))}
      </CompetitionSlugsProvider>
    </div>
  );
}
