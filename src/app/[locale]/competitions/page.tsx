import { Metadata } from 'next';
import { notFound } from 'next/navigation';

import { BlockRenderer } from '@/blocks/BlockRenderer';
import { PageProps, ParamsWithLocale } from '@/config/types/page';
import { getCompetitionsPageData, getCompetitionsPageSeo } from '@/strapi/api/single/competitions';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';

import { CompetitionCard } from './_components/CompetitionCard';

export * from '@/config/page-cache';

export async function generateMetadata({ params }: ParamsWithLocale): Promise<Metadata> {
  const locale = (await params).locale;
  const seo = await getCompetitionsPageSeo(locale);

  return {
    title: seo?.metaTitle ?? 'Esports World Cup | Competitions',
    description: seo?.metaDescription ?? 'Esports World Cup | Competitions',
  };
}

export default async function CompetitionsPage({ params }: PageProps) {
  const locale = (await params).locale;
  const competitionsPageData = await getCompetitionsPageData(locale);
  const siteConfig = await getSiteConfig(locale);

  if (!competitionsPageData) {
    return notFound();
  }

  const { pageData, gamesData } = competitionsPageData;
  const { title, subtitle, blocks } = pageData;

  const isHeaderVisible = title || subtitle;
  return (
    <div className="px-4 py-15 lg:px-8">
      <div className="relative mx-auto flex max-w-[1400px] flex-col gap-5 md:gap-8">
        {isHeaderVisible && (
          <section className="text-dark-default flex flex-col gap-1">
            {title && <h2 className="text-h3">{title}</h2>}
            {subtitle && <p className="font-base text-sm leading-[1.6] md:text-[18px]">{subtitle}</p>}
          </section>
        )}
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 xl:grid-cols-4">
          {gamesData &&
            gamesData.games &&
            gamesData.games.map((game) => {
              return <CompetitionCard game={game} key={game.id} siteConfig={siteConfig} />;
            })}
        </div>
        {blocks.map((b) => (
          <BlockRenderer {...b} key={`${b.__component}-${b.id}`} />
        ))}
      </div>
    </div>
  );
}
