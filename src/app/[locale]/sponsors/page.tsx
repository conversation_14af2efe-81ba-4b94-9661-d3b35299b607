import clsx from 'clsx';
import { notFound } from 'next/navigation';
import { RxArrowTopRight } from 'react-icons/rx';

import { getPartnersPageData } from '@/strapi/api/single/partners';

import { RichTextContent } from '../../../blocks/RichTextBlock/RichTextContent';
import { LocalizedLink } from '../../../components/LocalizedLink';
import { StrapiImage } from '../../../components/StrapiImage';
import { PageProps } from '../../../config/types/page';
import { getSiteConfig } from '../../../strapi/api/single/siteConfig';
import { PartnerType } from '../../../strapi/types/collection/partner';
import { JsonFieldType } from '../../../strapi/types/helper';

export default async function SponsorsPage({ params }: PageProps) {
  const locale = (await params).locale;
  const partnersPageData = await getPartnersPageData(locale);
  const siteConfig = await getSiteConfig(locale);

  const { tiers, title, upperLabel, subtitle } = partnersPageData;

  if (!partnersPageData) {
    return notFound();
  }

  return (
    <div className="mx-auto mt-20 mb-[165px] flex max-w-[1400px] flex-col gap-20 px-4 lg:px-8">
      <div className="flex flex-col gap-3">
        {upperLabel && <h4 className="text-h4 text-center">{upperLabel}</h4>}
        {title && (
          <p className="font-primary text-center text-[42px] leading-[90%] font-bold md:text-[64px] xl:text-[90px]">
            {title}
          </p>
        )}
        {subtitle && <RichTextContent content={subtitle} paragraphClassName="text-paragraph text-center" />}
      </div>
      {tiers && (
        <div className="flex flex-col gap-16">
          {tiers.map((tier) => (
            <div className="flex flex-col gap-8 text-center" key={tier.id}>
              <div className="flex flex-col gap-1">
                <h4
                  className={clsx('text-h4', {
                    'text-gold-primary': tier.color === 'gold',
                    'text-dark-default': tier.color === 'standard',
                    'text-gray-dark': tier.color === 'gray',
                  })}
                >
                  {tier.title}
                </h4>
                <p className="text-paragraph text-dark-default">{tier.subtitle}</p>
              </div>
              {tier.partners && (
                <div className="flex flex-wrap justify-center gap-4">
                  {tier.partners.map((sponsor) => (
                    <SponsorCard key={sponsor.id} sponsor={sponsor} translations={siteConfig?.translations} />
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// TODO: consider extracting this as it will probably be used in multiple places
const SponsorCard = ({
  sponsor,
  translations,
}: {
  sponsor: Pick<PartnerType, 'id' | 'logo' | 'slug' | 'link'>;
  translations?: JsonFieldType | null;
}) => {
  const { link, logo, slug } = sponsor;

  const href = slug ? `/sponsors/${slug}` : link;
  const showExternalLinkArrow = link?.startsWith('http') && !slug;

  const cardContent = (
    <>
      {logo && (
        <div className="bg-white-dirty flex aspect-[1.63] w-full items-center justify-center overflow-hidden group-hover:bg-white">
          <StrapiImage className="h-auto max-h-full w-auto max-w-full object-contain" image={logo} />
        </div>
      )}
      {href && (
        <div className="flex items-center gap-1">
          <span className="font-primary text-dark-default w-fit shrink-0 text-[11px] leading-[110%] font-bold">
            {translations?.findOutMore ?? 'FIND OUT MORE'}
          </span>
          {showExternalLinkArrow && <RxArrowTopRight className="size-2.5" />}
        </div>
      )}
    </>
  );

  const cardClassName =
    'flex lg:h-[156px] lg:w-[212px] transition-colors group h-[116px] w-[147px] flex-col justify-between gap-2 rounded-sm bg-white p-2';

  if (!href) {
    return <div className={cardClassName}>{cardContent}</div>;
  }

  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Sponsors Grid', button_name: `Sponsor Card Link (${link})` }}
      className={cardClassName}
      href={href}
      target={showExternalLinkArrow ? '_blank' : undefined}
    >
      {cardContent}
    </LocalizedLink>
  );
};
