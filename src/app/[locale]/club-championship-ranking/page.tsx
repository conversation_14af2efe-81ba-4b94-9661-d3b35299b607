import { notFound } from 'next/navigation';

import EwcLogoIcon from '@/ui/assets/icons/logos/ewc.svg';

import { BlockRenderer } from '../../../blocks/BlockRenderer/BlockRenderer';
import { RichTextContent } from '../../../blocks/RichTextBlock/RichTextContent';
import { StrapiImage } from '../../../components/StrapiImage';
import { StrapiVideo } from '../../../components/StrapiVideo';
import { isProductionEnvironment } from '../../../config/env/server';
import { PageProps } from '../../../config/types/page';
import { getGameCompetitions } from '../../../strapi/api/collection/game';
import { getClubChampionshipRankingPageData } from '../../../strapi/api/single/clubChampionshipRanking';
import { getSiteConfig } from '../../../strapi/api/single/siteConfig';
import { isVideoMedia } from '../../../utils/media';
import { ClubChampionshipRankingsBlock } from './_components/ClubChampionshipRankingsBlock';

export default async function ClubChampionshipRankingPage({ params }: PageProps) {
  if (isProductionEnvironment) return notFound();

  const { locale } = await params;
  const pageData = await getClubChampionshipRankingPageData(locale);
  const siteConfig = await getSiteConfig(locale);
  const gameCompetitions = await getGameCompetitions(locale);

  if (!pageData) {
    return notFound();
  }

  const {
    blocks,
    title,
    subtitle,
    bgMedia,
    topExplainerText,
    leaderboardSubtitle,
    leaderboardTitle,
    middleExplainerText,
    bottomExplainerText,
    tournamentStart,
    tournamentEnd,
  } = pageData;

  return (
    <div>
      <div className="relative z-0 flex flex-col">
        {bgMedia && (
          <div className="absolute start-0 top-0 -z-10 size-full before:absolute before:inset-0 before:bg-gradient-to-b before:from-transparent before:from-[35.93%] before:to-black before:to-[79.47%]">
            {isVideoMedia(bgMedia.url) ? (
              <StrapiVideo autoPlay className="size-full object-cover" loop muted video={bgMedia} />
            ) : (
              <StrapiImage className="size-full object-cover" image={bgMedia} />
            )}
          </div>
        )}

        <div className="/min-h-[721px] flex flex-col items-center justify-center pt-[67px] pb-[178px] md:pt-[103px] md:pb-[226px] lg:pt-[142px] lg:pb-[247px] xl:pt-[202px] xl:pb-[294px] 2xl:pt-[178px] 2xl:pb-[195px]">
          <div className="flex flex-col items-center gap-4 md:gap-8">
            <EwcLogoIcon
              className="text-gold-primary max-w-[156px] md:max-w-[191px] lg:max-w-[327px]"
              height={64}
              width={327}
            />
            {title && (
              <span className="font-primary text-center text-[36px] leading-[85%] font-bold text-white md:text-[48px] lg:text-[74px] xl:text-[90px] 2xl:text-[110px]">
                {title}
              </span>
            )}
            {subtitle && (
              <RichTextContent
                content={subtitle}
                paragraphClassName="text-paragraph text-center font-base text-white"
              />
            )}
          </div>
        </div>
      </div>
      <ClubChampionshipRankingsBlock
        bottomExplainerText={bottomExplainerText}
        gameCompetitions={gameCompetitions}
        leaderboardSubtitle={leaderboardSubtitle}
        leaderboardTitle={leaderboardTitle}
        middleExplainerText={middleExplainerText}
        siteConfig={siteConfig}
        topExplainerText={topExplainerText}
        tournamentEnd={tournamentEnd ?? '2025-08-24'}
        tournamentStart={tournamentStart ?? '2025-07-07'}
      />
      {blocks.map((b) => (
        <BlockRenderer {...b} key={`${b.__component}-${b.id}`} />
      ))}
    </div>
  );
}
