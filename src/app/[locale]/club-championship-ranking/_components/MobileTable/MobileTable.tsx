import { JsonFieldType } from '../../../../../strapi/types/helper';
import { ClubData } from '../../_utils/shape-api-data';
import { MobileTableRow } from './MobileTableRow';

export const MobileTable = ({
  rankings,
  translations,
}: {
  rankings: ClubData[];
  translations?: JsonFieldType | null;
}) => {
  return (
    <div>
      <div className="bg-dark-default font-primary flex gap-1.5 py-2 text-[10px] font-bold text-white uppercase">
        <span className="w-[44px] text-center">{translations?.rank ?? 'rank'}</span>
        <span className="grow">{translations?.club ?? 'club'}</span>
        <span className="w-[100px] pe-4 text-right">{translations?.ccPoints ?? 'CC POINTS'}</span>
      </div>
      <div className="divide-gray-easy divide-y">
        {rankings.map((club) => (
          <MobileTableRow club={club} key={club.club.id} translations={translations} />
        ))}
      </div>
    </div>
  );
};
