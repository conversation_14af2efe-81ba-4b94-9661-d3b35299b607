import clsx from 'clsx';
import Image from 'next/image';
import { FaChevronDown } from 'react-icons/fa';
import { useToggle } from 'usehooks-ts';

import EwcAcronymLogoIcon from '@/ui/assets/icons/logos/ewc-acronym.svg';

import { constructImageUrl } from '../../../../../services/graphql/utils';
import { JsonFieldType } from '../../../../../strapi/types/helper';
import { ClubData } from '../../_utils/shape-api-data';
import { CompetitionRankings } from '../CompetitionRankings';
import { ClubChampionshipEligibleIcon } from '../HelperComponents/ClubChampionshipEligibleIcon';
import { MedalPill } from '../HelperComponents/MedalPill';
import { RankDiffPill } from '../HelperComponents/RankDiffPill';

export const DesktopTableRow = ({ club, translations }: { club: ClubData; translations?: JsonFieldType | null }) => {
  const [isOpen, toggleIsOpen] = useToggle(false);

  const clubLogo = constructImageUrl(club.images, 'logo_transparent_whitebg');
  return (
    <>
      <div
        className={clsx(
          'font-primary before:bg-dark-default relative flex cursor-pointer justify-between bg-white p-4 ps-6 font-bold before:absolute before:start-0 before:top-0 before:h-0 before:w-[6px] before:transition-all before:duration-75',
          {
            'before:h-full': isOpen,
          },
        )}
        onClick={() => toggleIsOpen()}
      >
        <div className="flex items-center gap-4">
          <div className="flex min-w-[44px] items-center justify-center gap-0.5">
            <span className="text-[16px] leading-[110%]">#</span>
            <span className="text-[21px] leading-[110%]">{club.rank}</span>
          </div>
          <RankDiffPill diff={club.rankDiff} />
          <div className="flex items-center gap-5">
            {clubLogo ? (
              <Image alt={club.club.name ?? ''} className="size-8" height={32} src={clubLogo} width={32} />
            ) : (
              <EwcAcronymLogoIcon className="size-8 text-black" />
            )}
            <span className="text-[18px] leading-[110%]">{club.club.name}</span>
          </div>
        </div>
        <div className="flex items-center gap-6">
          {!club.isChampionshipEligible && <ClubChampionshipEligibleIcon backgroundColor="#ededed" />}
          <span className="min-w-[110px] text-center text-[21px] leading-[110%]">{club.prizes.XTS}</span>
          <div className="flex items-center gap-2">
            <MedalPill count={club.medals.gold} type="gold" />
            <MedalPill count={club.medals.silver} type="silver" />
            <MedalPill count={club.medals.bronze} type="bronze" />
          </div>
          <div className="flex size-8 items-center px-2">
            <FaChevronDown className={clsx('size-full transition-all', { 'rotate-180 opacity-20': isOpen })} />
          </div>
        </div>
      </div>
      {isOpen && <CompetitionRankings competitions={club.competitions} translations={translations} />}
    </>
  );
};
