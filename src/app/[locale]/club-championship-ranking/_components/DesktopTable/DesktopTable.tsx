import { JsonFieldType } from '../../../../../strapi/types/helper';
import { ClubData } from '../../_utils/shape-api-data';
import { DesktopTableRow } from './DesktopTableRow';

export const DesktopTable = ({
  rankings,
  translations,
}: {
  rankings: ClubData[];
  translations?: JsonFieldType | null;
}) => {
  return (
    <div>
      <div className="bg-dark-default font-primary flex gap-1.5 py-2 ps-6 pe-16 text-[12px] leading-[100%] font-bold text-white uppercase">
        <span className="w-[44px] text-center">{translations?.rank ?? 'rank'}</span>
        <span className="w-[44px] text-center">{translations?.rankDiff ?? '+/-'}</span>
        <span className="h-0 w-[39px] opacity-0">hidden element</span>
        <span className="min-w-[205px] grow">{translations?.club ?? 'club'}</span>
        <span className="w-[110px] text-center">{translations?.ccPoints ?? 'CC POINTS'}</span>
        <span className="w-[166px] text-center">{translations?.medals ?? 'MEDALS'}</span>
      </div>
      <div className="divide-gray-easy divide-y">
        {rankings.map((club) => (
          <DesktopTableRow club={club} key={club.club.id} translations={translations} />
        ))}
      </div>
    </div>
  );
};
