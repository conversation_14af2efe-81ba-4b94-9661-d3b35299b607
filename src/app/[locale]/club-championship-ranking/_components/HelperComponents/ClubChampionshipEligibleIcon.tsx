import { IoClose } from 'react-icons/io5';
import { LiaCrownSolid } from 'react-icons/lia';

export const ClubChampionshipEligibleIcon = ({ backgroundColor = 'white' }: { backgroundColor?: string }) => {
  return (
    <div className="relative flex size-[27px] items-center justify-center rounded-sm" style={{ backgroundColor }}>
      <LiaCrownSolid className="text-gold-primary h-[18.5px] w-[17px]" />
      <IoClose className="text-red-accent absolute end-1 bottom-1 size-[12px]" />
    </div>
  );
};
