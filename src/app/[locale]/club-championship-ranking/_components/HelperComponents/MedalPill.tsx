import clsx from 'clsx';

import BlankMedalIcon from '@/ui/assets/icons/blank-medal.svg';

export const MedalPill = ({ count, type }: { count: number; type: 'gold' | 'silver' | 'bronze' }) => {
  return (
    <div
      className={clsx('flex items-center gap-px rounded-xs p-0.5 text-white md:gap-[3px] md:rounded-[3px] md:p-1', {
        'bg-[#907C4B] md:border md:border-[#907C4B] md:bg-[#907C4B33] md:text-[#907C4B]': type === 'gold',
        'bg-[#727272] md:border md:border-[#727272] md:bg-[#72727233] md:text-[#727272]': type === 'silver',
        'bg-[#683C13] md:border md:border-[#683C13] md:bg-[#683C1333] md:text-[#683C13]': type === 'bronze',
      })}
    >
      <BlankMedalIcon className={clsx('size-2.5 max-md:text-white md:h-4 md:w-5')} />
      <span className="text-[10px] leading-[100%] md:text-[14px]">{count}</span>
    </div>
  );
};
