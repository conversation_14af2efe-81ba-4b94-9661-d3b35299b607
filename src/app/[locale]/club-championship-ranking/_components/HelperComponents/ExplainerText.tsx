import { BlocksContent } from '@strapi/blocks-react-renderer';
import { ReactNode } from 'react';

import { RichTextContent } from '../../../../../blocks/RichTextBlock/RichTextContent';

export const ExplainerText = ({ content, icon }: { content: BlocksContent; icon?: ReactNode }) => {
  return (
    <div className="border-gold-primary/50 bg-gold-primary/5 flex gap-2 rounded-xl border px-4 py-3">
      {icon}
      <RichTextContent
        content={content}
        paragraphClassName="font-base text-gold-primary font-bold text-[14px] md:text-[16px] leading-[160%]"
      />
    </div>
  );
};
