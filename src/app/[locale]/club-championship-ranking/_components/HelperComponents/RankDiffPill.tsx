import clsx from 'clsx';
import { GoTriangleDown } from 'react-icons/go';

export const RankDiffPill = ({ diff }: { diff?: number }) => {
  if (diff === undefined || diff === null) return null;
  return (
    <div
      className={clsx('flex items-center gap-0.5 rounded-xs px-1 py-0.5 text-white', {
        'bg-[#F40F30] [box-shadow:1px_2px_12px_0px_#F40F3066]': diff < 0,
        'bg-[#09C350] [box-shadow:1px_2px_12px_0px_#09C35066]': diff > 0,
        'bg-[#F4890F] [box-shadow:1px_2px_12px_0px_#F4890F66]': diff === 0,
      })}
    >
      {diff === 0 ? (
        <div className="size-1.5 rounded-full bg-white" />
      ) : (
        <GoTriangleDown className={clsx('size-2.5', { 'rotate-180': diff > 0 })} />
      )}
      <span className="text-[10px] leading-[100%]">{Math.abs(diff)}</span>
    </div>
  );
};
