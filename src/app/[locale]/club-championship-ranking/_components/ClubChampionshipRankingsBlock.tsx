'use client';

import clsx from 'clsx';
import { MdOutlineWarningAmber } from 'react-icons/md';

import { useTournamentsData } from '@/services/graphql/hooks';

import { SiteConfigData } from '../../../../strapi/api/single/siteConfig';
import { GameType } from '../../../../strapi/types/collection/game';
import { RichTextContentType } from '../../../../strapi/types/helper';
import { Button } from '../../../../ui/components/Button';
import { generateHistoricalRankings } from '../_utils/shape-api-data';
import { ClubCard } from './ClubCard';
import { ClubChampionshipEligibleIcon } from './HelperComponents/ClubChampionshipEligibleIcon';
import { ExplainerText } from './HelperComponents/ExplainerText';
import { LeaderboardSection } from './LeaderboardSection';
import { MissingDataPlaceholder } from './MissingDataPlaceholder';

interface ClubChampionshipRankingsBlockProps {
  leaderboardTitle: string | null;
  leaderboardSubtitle: string | null;
  tournamentEnd: string;
  tournamentStart: string;
  topExplainerText: RichTextContentType | null;
  middleExplainerText: RichTextContentType | null;
  bottomExplainerText: RichTextContentType | null;
  siteConfig: SiteConfigData | null;
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo'>[];
}

export const ClubChampionshipRankingsBlock = ({
  topExplainerText,
  leaderboardTitle,
  leaderboardSubtitle,
  siteConfig,
  middleExplainerText,
  tournamentEnd = '2025-08-24',
  tournamentStart = '2025-07-07',
  bottomExplainerText,
  gameCompetitions,
}: ClubChampionshipRankingsBlockProps) => {
  const flatCompetitionSlugs = gameCompetitions.flatMap((game) =>
    game.competitionSlugs.map((competition) => competition.competitionId),
  );
  const { data, loading, error } = useTournamentsData(flatCompetitionSlugs);

  if (loading) return <div>loading...</div>;
  if (error) return <MissingDataPlaceholder>Something went wrong</MissingDataPlaceholder>;
  if (!data || !data.tournaments.result) return <MissingDataPlaceholder>No data found</MissingDataPlaceholder>;

  const historicalRankings = generateHistoricalRankings(
    data.tournaments.result,
    gameCompetitions,
    new Date(tournamentStart),
    new Date(tournamentEnd),
  );

  const highlightedClubs = historicalRankings[tournamentEnd].slice(0, 3);

  return (
    <div
      className={clsx('relative z-10 mx-auto flex max-w-[1396px] flex-col items-center gap-20 px-4 lg:px-8', {
        'mt-[-118px] md:mt-[-137.5px] lg:mt-[-118px]': highlightedClubs.length > 0,
      })}
    >
      <div className="flex w-full flex-col gap-4 md:gap-8">
        {highlightedClubs.length > 0 && (
          <div className="flex w-full flex-col gap-2 md:flex-row md:gap-4 2xl:gap-8">
            {highlightedClubs.map((club) => (
              <ClubCard club={club} key={club.club.id} translations={siteConfig?.translations} />
            ))}
          </div>
        )}
        {topExplainerText && (
          <ExplainerText
            content={topExplainerText}
            icon={<MdOutlineWarningAmber className="text-gold-primary size-[22px]" />}
          />
        )}
      </div>

      <div className="flex w-full flex-col gap-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-end [&>button]:max-md:w-full">
          <div className="flex grow flex-col gap-1">
            <h3 className="text-h3 text-dark-default font-primary">{leaderboardTitle}</h3>
            <p className="text-paragraph text-dark-default font-base">{leaderboardSubtitle}</p>
          </div>
          <Button
            brazeEventProperties={{ button_name: 'endgame simulator', location: 'club championship ranking' }}
            // TODO: pull text from cms
            text="endgame simulator"
            variant="primary"
          />
        </div>

        <LeaderboardSection
          historicalRankings={historicalRankings}
          middleExplainerText={middleExplainerText}
          tournamentEnd={tournamentEnd}
          tournamentStart={tournamentStart}
          translations={siteConfig?.translations}
        />
        {bottomExplainerText && <ExplainerText content={bottomExplainerText} icon={<ClubChampionshipEligibleIcon />} />}
      </div>
    </div>
  );
};
