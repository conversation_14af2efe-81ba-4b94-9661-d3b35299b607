import clsx from 'clsx';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';

import { LocalizedLink } from '@/components/LocalizedLink';
import BlankMedalIcon from '@/ui/assets/icons/blank-medal.svg';
import BronzeMedalIcon from '@/ui/assets/icons/bronze-medal.svg';
import GoldMedalIcon from '@/ui/assets/icons/gold-medal.svg';
import SilverMedalIcon from '@/ui/assets/icons/silver-medal.svg';
import AbstractBgImage from '@/ui/assets/images/abstract-background.png';

import { StrapiImage } from '../../../../components/StrapiImage';
import { JsonFieldType } from '../../../../strapi/types/helper';
import { MediaType } from '../../../../strapi/types/media';
import { ClubCompetition } from '../_utils/shape-api-data';

export const CompetitionRankings = ({
  competitions,
  translations,
}: {
  competitions: Record<string, ClubCompetition>;
  translations?: JsonFieldType | null;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className="relative flex gap-2 p-2 md:p-4"
      initial={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Image alt="" className="absolute inset-0 -z-10 size-full object-cover" quality={65} src={AbstractBgImage} />
      <AnimatePresence>
        {Object.entries(competitions).map(([competitionName, value], index) => (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            initial={{ opacity: 0, y: 20 }}
            key={`${competitionName}-${value.rank}-${index}`}
            transition={{ duration: 0.2, delay: 0.1 + index * 0.05 }}
          >
            <CompetitionCard
              competitionName={competitionName}
              logoDark={value.logoDark}
              logoLight={value.logoLight}
              prizes={value.prizes}
              rank={value.rank}
              slug={value.slug}
              translations={translations}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </motion.div>
  );
};

export const CompetitionCard = ({
  competitionName,
  logoDark,
  logoLight,
  rank,
  prizes,
  slug,
  translations,
}: {
  competitionName: string;
  rank: number | null;
  prizes: Record<string, number>;
  logoDark: MediaType | null;
  logoLight: MediaType | null;
  slug: string | null;
  translations?: JsonFieldType | null;
}) => {
  const getMedalIcon = () => {
    switch (rank) {
      case 1:
        return <GoldMedalIcon className="h-[41px] w-[48px]" />;
      case 2:
        return <SilverMedalIcon className="h-[41px] w-[48px]" />;
      case 3:
        return <BronzeMedalIcon className="h-[41px] w-[48px]" />;
      case null:
        return <BlankMedalIcon className="h-[31px] w-[36px] text-[#FFFFFF1A]" />;
      default:
        return <BlankMedalIcon className="h-[31px] w-[36px] text-[#1515151A]" />;
    }
  };

  const content = (
    <motion.div
      className={clsx('flex min-h-[147px] w-[84px] flex-col gap-2 rounded-sm px-0.5 py-2', {
        'bg-[radial-gradient(50%_100%_at_50%_0%,_#AD9761_0%,_#907C4B_100%)]': rank === 1,
        'bg-[radial-gradient(50%_100%_at_50%_0%,_#A6A6A6_0%,_#727272_100%)]': rank === 2,
        'bg-[radial-gradient(50%_100%_at_50%_0%,_#93551B_0%,_#683C13_100%)]': rank === 3,
        'bg-white': rank !== null && rank > 3,
        'bg-dark-default': rank === null,
      })}
      transition={{ duration: 0.2 }}
      whileHover={{ scale: 1.05 }}
    >
      <div
        className={clsx('font-primary text-center text-[10px] leading-[110%] font-bold capitalize', {
          'text-black': rank !== null && rank > 3,
          'text-white': rank === null || rank <= 3,
        })}
      >
        {rank ? (
          <>
            {translations?.position ?? 'position'} #{rank}
          </>
        ) : (
          <>{translations?.upcoming ?? 'upcoming'}</>
        )}
      </div>
      <div
        className={clsx('relative flex size-20 items-center justify-center rounded-sm', {
          //   '': rank === 1,
          //   '': rank === 2,
          'bg-[#151515]': rank !== null && rank <= 3,
          'bg-[#1515150D]': rank !== null && rank > 3,
          'bg-[#FFFFFF12]': rank === null,
        })}
      >
        {getMedalIcon()}
        {rank !== null && (
          <div
            className={clsx(
              'font-primary absolute inset-x-0 -bottom-1 mx-auto w-fit rounded-xs bg-white px-1 py-0.5 text-[10px] leading-[110%] font-bold capitalize',
              {
                'text-[#8F7B4A]': rank === 1,
                'text-[#727272]': rank === 2 || rank > 3,
                'text-[#683C13]': rank === 3,
              },
            )}
          >
            {prizes.XTS} {translations?.points ?? 'points'}
          </div>
        )}
      </div>

      {rank !== null && logoDark && rank > 3 && <StrapiImage image={logoDark} />}
      {rank !== null && logoLight && rank <= 3 && <StrapiImage image={logoLight} />}
      {rank === null && logoLight && <StrapiImage image={logoLight} />}
    </motion.div>
  );

  if (slug) {
    return (
      <LocalizedLink
        brazeEventProperties={{
          button_name: `Competition Card - ${competitionName} Link`,
          location: 'Club Championship Ranking table',
        }}
        href={`/competitions/${slug}`}
      >
        {content}
      </LocalizedLink>
    );
  }

  return content;
};
