'use client';
import { BlocksContent } from '@strapi/blocks-react-renderer';
import { useState } from 'react';

import { SearchInput } from '../../../../components/SearchInput';
import { JsonFieldType } from '../../../../strapi/types/helper';
import { generateWeekDatesBetween } from '../../schedule/_components/utils';
import { HistoricalRankings } from '../_utils/shape-api-data';
import { ExplainerText } from './HelperComponents/ExplainerText';
import { LeaderboardTable } from './LeaderboardTable';
import { WeekPicker } from './WeekPicker';

export const LeaderboardSection = ({
  middleExplainerText,
  historicalRankings,
  tournamentStart,
  tournamentEnd,
  translations,
}: {
  middleExplainerText: BlocksContent | null;
  historicalRankings: HistoricalRankings;
  tournamentStart: string;
  tournamentEnd: string;
  translations?: JsonFieldType | null;
}) => {
  const [searchValue, setSearchValue] = useState('');

  // TODO: default active day to today
  const [activeDay, setActiveDay] = useState<string>(tournamentStart);

  const weekDates = generateWeekDatesBetween(tournamentStart, tournamentEnd);

  const activeRankings = historicalRankings[activeDay];

  return (
    <>
      <SearchInput
        analyticsLocation="Club Championship Ranking - Search"
        placeholder="Search clubs..."
        onSearch={setSearchValue}
      />
      <WeekPicker activeDay={activeDay} setActiveDay={setActiveDay} translations={translations} weekDates={weekDates} />
      {middleExplainerText && <ExplainerText content={middleExplainerText} />}

      <LeaderboardTable rankings={activeRankings} searchValue={searchValue} translations={translations} />
    </>
  );
};
