'use client';

import { useState } from 'react';

import { JsonFieldType } from '../../../../strapi/types/helper';
import { Only } from '../../../../ui/components/Only';
import { Paging } from '../../../../ui/components/Paging/Paging';
import { ClubData } from '../_utils/shape-api-data';
import { DesktopTable } from './DesktopTable/DesktopTable';
import { MissingDataPlaceholder } from './MissingDataPlaceholder';
import { MobileTable } from './MobileTable/MobileTable';

const ITEMS_PER_PAGE = 10;

export const LeaderboardTable = ({
  searchValue,
  rankings,
  translations,
}: {
  searchValue: string;
  rankings: ClubData[];
  translations?: JsonFieldType | null;
}) => {
  const [currentPage, setCurrentPage] = useState(1);

  const filteredRankings = rankings.filter((ranking) =>
    ranking.club.name?.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const totalPages = Math.ceil(filteredRankings.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const paginatedRankings = filteredRankings.slice(startIndex, startIndex + ITEMS_PER_PAGE);

  if (paginatedRankings.length === 0) {
    return (
      <MissingDataPlaceholder>
        {translations?.searchTooStrongMessage ?? 'No data found for given search term.'}
      </MissingDataPlaceholder>
    );
  }

  return (
    <div className="flex flex-col gap-4 overflow-hidden rounded-lg">
      <Only for="mdAndAbove">
        <DesktopTable rankings={paginatedRankings} translations={translations} />
      </Only>
      <Only for="sm">
        <MobileTable rankings={paginatedRankings} translations={translations} />
      </Only>

      <Paging currentPage={currentPage} mode="client" pages={totalPages} onPageChange={setCurrentPage} />
    </div>
  );
};
