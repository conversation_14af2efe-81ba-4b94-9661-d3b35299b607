import { TeamImage } from '@/services/graphql/types/shared';
import { GameType } from '@/strapi/types/collection/game';
import { MediaType } from '@/strapi/types/media';

import { Prize, Tournament, TournamentContestant } from '../../../../services/graphql/types/tournament';

interface Competition {
  prizePool: Prize[];
  contestants: Record<string, ContestantWithTimestamp>;
}

interface ContestantWithTimestamp extends TournamentContestant {
  timestamp: Date;
  tournamentId: string;
}

export interface ClubCompetition {
  rank: number | null;
  prizes: {
    [key: string]: number;
  };
  logoDark: MediaType | null;
  logoLight: MediaType | null;
  slug: string | null;
}

export interface ClubData {
  club: {
    id: string;
    name: string | null;
  };
  competitions: Record<string, ClubCompetition>;
  prizes: {
    [key: string]: number;
  };
  medals: {
    gold: number;
    silver: number;
    bronze: number;
  };
  top8Placements: number;
  isChampionshipEligible: boolean;
  images: TeamImage[];
  tournamentParticipated: Set<string>;
  tournamentCount?: number;
  rank?: number;
  rankDiff?: number;
}

export type HistoricalRankings = Record<string, ClubData[]>;

// Function to generate rankings for a specific date
export function generateRankingForDate(
  tournaments: Tournament[],
  competitionsSlugs: Record<string, string[]>,
  targetDate: Date,
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo'>[],
) {
  // Compute competition ranking based on multiple tournaments
  const competitions: Record<string, Competition> = {};
  Object.entries(competitionsSlugs).forEach(([slug, ids]) => {
    competitions[slug] = {
      prizePool: [],
      contestants: {},
    };

    tournaments
      .filter((tournament) => ids.includes(tournament.id))
      .forEach((tournament) => {
        if (tournament.prizePool) {
          competitions[slug].prizePool = competitions[slug].prizePool.concat(tournament.prizePool);
        }

        tournament.contestants?.forEach((contestant) => {
          if (contestant.team?.club.id) {
            competitions[slug].contestants[contestant.team.club.id] = {
              timestamp: new Date(tournament.endTime || ''),
              tournamentId: tournament.id,
              ...contestant,
            };
          }
        });
      });
  });

  // Compute club championship ranking based on rankings from all competitions
  const clubChampionship: Record<string, ClubData> = {};
  Object.entries(competitions).forEach(([slug, competition]) => {
    const gameCompetition = gameCompetitions.find((gc) => gc.slug === slug);
    const worstPossibleRank = Object.values(competition.contestants).filter(
      (c) => c.timestamp > targetDate || !c.rank,
    ).length;

    Object.entries(competition.contestants).forEach(([clubId, contestant]) => {
      if (!clubChampionship[clubId] && contestant.team) {
        clubChampionship[clubId] = {
          club: {
            id: contestant.team.club.id,
            name: contestant.team.club.name,
          },
          competitions: {},
          prizes: {},
          medals: { gold: 0, silver: 0, bronze: 0 },
          top8Placements: 0,
          isChampionshipEligible: false,
          images: contestant.team.images || [],
          tournamentParticipated: new Set(),
        };
      }

      if (clubChampionship[clubId]) {
        clubChampionship[clubId].tournamentParticipated.add(contestant.tournamentId);

        const actualRank = contestant.timestamp <= targetDate ? contestant.rank : null;
        const calculatedRank = actualRank || worstPossibleRank;

        clubChampionship[clubId].competitions[slug] = {
          rank: actualRank,
          prizes: {},
          logoDark: gameCompetition?.logoDark || null,
          logoLight: gameCompetition?.schedulePopupLogo || null,
          slug: gameCompetition?.slug || null,
        };

        if (actualRank === 1) {
          clubChampionship[clubId].medals.gold++;
        } else if (actualRank === 2) {
          clubChampionship[clubId].medals.silver++;
        } else if (actualRank === 3) {
          clubChampionship[clubId].medals.bronze++;
        }

        if (actualRank !== null && actualRank <= 8) {
          clubChampionship[clubId].top8Placements++;
        }

        competition.prizePool
          .filter((p) => p.rank === calculatedRank)
          .forEach((prize) => {
            if (prize.currency) {
              if (!clubChampionship[clubId].prizes[prize.currency]) {
                clubChampionship[clubId].prizes[prize.currency] = 0;
              }
              if (!clubChampionship[clubId].competitions[slug].prizes[prize.currency]) {
                clubChampionship[clubId].competitions[slug].prizes[prize.currency] = 0;
              }

              clubChampionship[clubId].prizes[prize.currency] += prize.amount || 0;
              clubChampionship[clubId].competitions[slug].prizes[prize.currency] += prize.amount || 0;
            }
          });
      }
    });
  });

  // Update championship eligibility for all clubs
  Object.values(clubChampionship).forEach((club) => {
    club.isChampionshipEligible = club.top8Placements >= 2;
  });

  // Sort clubs by their total points decending
  const clubChampionshipRanking = Object.values(clubChampionship).sort((a, b) => {
    const pointsDiff = (b.prizes['XTS'] || 0) - (a.prizes['XTS'] || 0);
    if (pointsDiff !== 0) return pointsDiff;

    const goldDiff = b.medals.gold - a.medals.gold;
    if (goldDiff !== 0) return goldDiff;

    const silverDiff = b.medals.silver - a.medals.silver;
    if (silverDiff !== 0) return silverDiff;

    return b.medals.bronze - a.medals.bronze;
  });

  // Convert Sets to counts and add tournament count
  clubChampionshipRanking.forEach((club) => {
    club.tournamentCount = club.tournamentParticipated.size;
  });

  // Compute rank based on total points with gaps in case of same points as previous club
  let currentRank = 1;
  clubChampionshipRanking.forEach((club, index) => {
    const currentPoints = club.prizes['XTS'] || 0;
    const previousPoints = clubChampionshipRanking[index - 1]?.prizes['XTS'] || 0;
    if (currentPoints !== previousPoints) {
      currentRank = index + 1;
    }
    club.rank = currentRank;
  });

  return clubChampionshipRanking;
}

// Function to generate historical rankings between two dates
export function generateHistoricalRankings(
  tournaments: Tournament[],
  gameCompetitions: Pick<GameType, 'slug' | 'competitionSlugs' | 'logoDark' | 'schedulePopupLogo'>[],
  startDate: Date,
  endDate: Date,
): HistoricalRankings {
  const competitionSlugs = gameCompetitions.reduce<Record<string, string[]>>((acc, game) => {
    if (!game?.slug) return acc;

    acc[game.slug] = game.competitionSlugs.map((competition) => competition.competitionId);
    return acc;
  }, {});

  // Process tournaments to strip time component from timestamps
  tournaments = tournaments.map((tournament) => ({
    ...tournament,
    endTime: tournament.endTime ? new Date(tournament.endTime).toISOString().split('T')[0] : null,
  }));

  // Generate rankings for each day
  const historicalRankings: HistoricalRankings = {};
  const currentDate = startDate;
  const endDateObj = endDate;

  // Keep track of previous day's rankings
  let previousDayRankings: Record<string, { rank: number }> = {};

  while (currentDate <= endDateObj) {
    const rankings = generateRankingForDate(tournaments, competitionSlugs, currentDate, gameCompetitions);

    // Calculate rank differences compared to previous day
    rankings.forEach((club) => {
      const previousRank = previousDayRankings[club.club.id]?.rank;
      club.rankDiff = previousRank ? previousRank - club.rank! : 0;
    });

    // Store current rankings for next day's comparison
    previousDayRankings = rankings.reduce<Record<string, { rank: number }>>((acc, club) => {
      acc[club.club.id] = {
        rank: club.rank!,
      };
      return acc;
    }, {});

    // Use YYYY-MM-DD as the key
    const dateStr = currentDate.toISOString().split('T')[0];
    historicalRankings[dateStr] = rankings;

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return historicalRankings;
}

// Example competition slugs structure for reference
export const EXAMPLE_COMPETITION_SLUGS = {
  dota2: [
    '3f534358-c416-403e-bbcf-371213aebacc',
    '63a2dfb9-2b64-401a-9848-f5ba826fd64e',
    'c1712f2a-c661-4bf9-96f8-7cdca4eb6f35',
  ],
  pubg: ['9ba946ae-39d6-46cf-8d9b-5c65444b65f0', '22b8e286-9f2e-44fd-80ae-58e371b5e980'],
};
