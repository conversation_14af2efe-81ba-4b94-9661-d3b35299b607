/* eslint-disable @next/next/no-page-custom-font */

import '@/ui/styles/globals.css';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

import clsx from 'clsx';
import { notFound } from 'next/navigation';

import { CookieConsentModal } from '@/components/CookieConsentModal';
import { GoogleAnalyticsProvider } from '@/components/GoogleAnalyticsProvider';
import { isProductionEnvironment } from '@/config/env/server';
import { LayoutProps } from '@/config/types/page';
import { DEFAULT_LOCALE } from '@/hooks/i18n/const';
import { InitializeBraze } from '@/services/braze/InitializeBraze';
import { getRedirects } from '@/strapi/api/collection/redirect';
import { getCookieConsentModalData } from '@/strapi/api/single/cookieConsentModal';
import { getFooterData } from '@/strapi/api/single/footer';
import { getNavigationData } from '@/strapi/api/single/navigation';
import { getSiteConfig } from '@/strapi/api/single/siteConfig';
import { Toaster } from '@/ui/components/Toast/Toaster';
import { ApolloClientProvider } from '@/ui/providers/ApolloClientProvider';
import QueryClientProvider from '@/ui/providers/QueryClientProvider';
import { ScreenTypeProvider } from '@/ui/providers/ScreenTypeProvider';
import { fontStyles } from '@/ui/styles/fonts';

import i18nConfig from '../../../i18nConfig';
import { Footer } from './_components/Footer';
import { IconsMeta } from './_components/IconsMeta';
import { Navigation } from './_components/Navigation';
import { RedirectHandler } from './_components/RedirectHandler';

export default async function RootLayout({ children, params }: LayoutProps) {
  const locale = (await params).locale;

  if (!i18nConfig.locales.includes(locale)) {
    notFound();
  }

  const navigationData = await getNavigationData(locale);
  const footerData = await getFooterData(locale);
  const cookieConsentModalData = await getCookieConsentModalData(locale);
  const siteConfig = await getSiteConfig(locale);
  const redirects = await getRedirects(locale);

  if (locale !== DEFAULT_LOCALE && siteConfig?.featureFlags?.disabledLocales?.includes(locale)) {
    notFound();
  }

  return (
    <html
      className={clsx(fontStyles, locale === 'ar' && 'font-arabic')}
      dir={locale === 'ar' ? 'rtl' : 'ltr'}
      lang={locale}
    >
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0&display=block"
          rel="stylesheet"
        />
        <IconsMeta />
      </head>
      <body className={clsx('flex min-h-screen flex-col antialiased', fontStyles)}>
        <InitializeBraze isDisabled={false} />
        <ApolloClientProvider>
          <QueryClientProvider>
            <ScreenTypeProvider>
              <CookieConsentModal {...{ ...cookieConsentModalData, siteConfig }} />
              <Navigation
                data={navigationData}
                disabledLocales={siteConfig?.featureFlags?.disabledLocales}
                siteConfig={siteConfig}
              />
              <main
                className={clsx(
                  'bg-white-dirty flex h-full flex-1 flex-col',
                  navigationData && 'lg:ps-[var(--spacing-sidebar-width)]',
                )}
              >
                <RedirectHandler locale={locale} redirects={redirects}>
                  {children}
                </RedirectHandler>
              </main>
              {footerData && (
                <div className={clsx('mt-auto', navigationData && 'lg:ps-[var(--spacing-sidebar-width)]')}>
                  <Footer {...footerData} disabledLocales={siteConfig?.featureFlags?.disabledLocales} />
                </div>
              )}
              <Toaster isRtl={locale === 'ar'} />
            </ScreenTypeProvider>
          </QueryClientProvider>
        </ApolloClientProvider>
        <GoogleAnalyticsProvider isProductionEnvironment={isProductionEnvironment} />
      </body>
    </html>
  );
}
