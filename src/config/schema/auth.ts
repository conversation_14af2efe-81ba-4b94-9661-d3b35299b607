import * as yup from 'yup';

import { JsonFieldType } from '@/strapi/types/helper';

export function getPasswordSchema(translations?: JsonFieldType | null) {
  return yup
    .string()
    .required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.')
    .min(
      8,
      translations?.['passwordFieldMinCharactersValidationMessage'] ?? 'Password must contain at least 8 characters.',
    )
    .matches(
      /[a-z]/,
      translations?.['passwordFieldLowercaseValidationMessage'] ??
        'Password must contain at least one lowercase letter.',
    )
    .matches(
      /[A-Z]/,
      translations?.['passwordFieldUppercaseValidationMessage'] ??
        'Password must contain at least one uppercase letter.',
    )
    .matches(
      /\d/,
      translations?.['passwordFieldNumberValidationMessage'] ?? 'Password must contain at least one number.',
    )
    .matches(
      /[@$!%*?&#]/,
      translations?.['passwordFieldSpecialCharacterValidationMessage'] ??
        'Password must contain at least one special character.',
    );
}

export function getPasswordConfirmSchema(translations?: JsonFieldType | null) {
  return yup
    .string()
    .required()
    .test({
      name: 'is-password-confirmed',
      message: translations?.['passwordConfirmFieldInvalidValidationMessage'] ?? 'Password incorrect. Try again.',
      test: (value, context) => {
        return value === context.parent.password;
      },
    });
}

export function getEmailSchema(translations?: JsonFieldType | null) {
  return yup
    .string()
    .required(translations?.['fieldRequiredValidationMessage'] ?? 'This is a required field.')
    .email(translations?.['emailFieldInvalidValidationMessage'] ?? 'Please enter a valid email address.');
}
