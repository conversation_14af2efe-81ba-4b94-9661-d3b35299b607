type EnvType = 'local' | 'qa' | 'staging' | 'production';
const ENV = process.env.ENV as EnvType;

if (!ENV) {
  throw new Error('Missing ENV env variable!');
}

export const isLocalEnvironment = ENV === 'local';
export const isStagingEnvironment = ENV === 'staging';
export const isProductionEnvironment = ENV === 'production';

export const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN as string;
if (!STRAPI_API_TOKEN) {
  throw new Error('Missing STRAPI_API_TOKEN env variable!');
}

export const AWS_REGION = process.env.AWS_REGION as string;
export const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID as string;
export const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY as string;

if (!AWS_REGION || !AWS_ACCESS_KEY_ID || !AWS_SECRET_ACCESS_KEY) {
  throw new Error('Missing AWS CLIENT env variables!');
}
