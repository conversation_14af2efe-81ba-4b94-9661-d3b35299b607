import { Locale } from '@/hooks/i18n/const';

export type ParamsWithLocale<T extends object | undefined = undefined> = {
  params: Promise<{ locale: Locale } & (T extends object ? T : object)>;
};

export type PageProps<T extends object | undefined = undefined> = ParamsWithLocale<T> & {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export type LayoutProps<T extends object | undefined = undefined> = React.PropsWithChildren<ParamsWithLocale<T>>;
