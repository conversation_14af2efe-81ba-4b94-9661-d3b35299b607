// exporting `let revalidate` is a hack because next 15 requires the value to be statically analyzable
// https://nextjs.org/docs/app/api-reference/file-conventions/route-segment-config#revalidate
// https://github.com/vercel/next.js/issues/72365#issuecomment-2459094362

// eslint-disable-next-line prefer-const
export let revalidate = +(process.env.REVALIDATE_TIME_IN_SECONDS ?? 60);
export const dynamic = 'force-static';
