export const TTL_1_HOUR = 3600;
export const TTL_1_DAY = 86400;
export const TTL_1_YEAR = 31536000;

export const ASSETS_CACHE_CONTROL_VALUE = `public, max-age=${TTL_1_HOUR}, stale-while-revalidate=${TTL_1_YEAR}, stale-while-revalidate=${TTL_1_YEAR}`;

const metadataFilesCacheHeaders = [
  '/android-chrome-192x192.png',
  '/android-chrome-512x512.png',
  '/apple-touch-icon.png',
  '/browserconfig.xml',
  '/favicon-16x16.png',
  '/favicon-32x32.png',
  '/favicon.ico',
  '/mstile-150x150.png',
  '/robots.txt',
  '/safari-pinned-tab.svg',
  '/site.webmanifest',
  '/twitter-image.png',
].map((source) => ({
  source,
  headers: [{ key: 'Cache-Control', value: ASSETS_CACHE_CONTROL_VALUE }],
}));

export const ASSETS_CACHE_HEADERS = [
  {
    source: '/assets/(.*)',
    headers: [{ key: 'Cache-Control', value: ASSETS_CACHE_CONTROL_VALUE }],
  },
  ...metadataFilesCacheHeaders,
];
