import { Fustat } from 'next/font/google';
import localFont from 'next/font/local';

const fustat = Fustat({
  variable: '--font-fustat',
  subsets: ['arabic', 'latin'],
});

const riforma = localFont({
  src: [
    { path: '../../../public/fonts/Riforma-LL/RiformaLLWeb-Bold.woff2', weight: '500' },
    { path: '../../../public/fonts/Riforma-LL/RiformaLLWeb-Bold.woff', weight: '500' },
    { path: '../../../public/fonts/Riforma-LL/RiformaLLSub-Bold.woff', weight: '500' },
    { path: '../../../public/fonts/Riforma-LL/RiformaLLSub-Bold.woff2', weight: '500' },
  ],
  variable: '--font-riforma',
});

const tajawal = localFont({
  src: [
    { path: '../../../public/fonts/Tajawal/Tajawal-Black.ttf', weight: '900' },
    { path: '../../../public/fonts/Tajawal/Tajawal-Bold.ttf', weight: '700' },
    { path: '../../../public/fonts/Tajawal/Tajawal-ExtraBold.ttf', weight: '800' },
    { path: '../../../public/fonts/Tajawal/Tajawal-Light.ttf', weight: '300' },
    { path: '../../../public/fonts/Tajawal/Tajawal-Medium.ttf', weight: '500' },
    { path: '../../../public/fonts/Tajawal/Tajawal-Regular.ttf', weight: '400' },
  ],
  variable: '--font-tajawal',
});

export const fontStyles = `${fustat.variable} ${riforma.variable} ${tajawal.variable}`;

export const fonts = {
  fustat,
  riforma,
  tajawal,
};
