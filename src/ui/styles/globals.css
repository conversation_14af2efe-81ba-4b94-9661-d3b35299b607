@import 'tailwindcss';
@import './colors.css';
@import './shadows.css';
@import './keyframes.css';
@import './space.css';
@import './typography.css';
@import './components.css';
@import './utilities.css';

@theme inline {
  --font-fustat: var(--font-fustat);
  --font-riforma: var(--font-riforma);
  --font-tajawal: var(--font-tajawal);
}

@layer base {
  html {
    --font-base: var(--font-fustat);
    --font-primary: var(--font-riforma);
    font-family: var(--font-base), Arial, sans-serif;
    overflow-x: hidden;
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .font-arabic {
    --font-base: var(--font-tajawal);
    --font-primary: var(--font-tajawal);
  }

  .font-primary {
    font-family: var(--font-primary);
  }

  .font-base {
    font-family: var(--font-base);
  }
}
