@theme {
  --animate-scroll-for-more-drop: scroll-for-more-drop 4s cubic-bezier(1, 0, 0, 1) infinite;
  @keyframes scroll-for-more-drop {
    0% {
      transform: translateY(0px);
    }
    10% {
      transform: translateY(30px);
    }
    100% {
      transform: translateY(30px);
    }
  }

  --animate-marquee-scroll: marquee-scroll 10s linear infinite;
  @keyframes marquee-scroll {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--marquee-gap)));
    }
  }
}
