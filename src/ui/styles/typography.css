@layer components {
  .text-h0 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 44px;
    line-height: 0.9;
    text-transform: uppercase;
    @variant md {
      font-size: 64px;
    }
    @variant lg {
      font-size: 90px;
    }
    @variant xl {
      font-size: 120px;
    }
  }

  .text-h1 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 32px;
    line-height: 1.1;
    @variant md {
      font-size: 44px;
    }
    @variant lg {
      font-size: 50px;
    }
    @variant xl {
      font-size: 54px;
    }
  }

  .text-h2 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 28px;
    line-height: 1.1;
    @variant md {
      font-size: 38px;
    }
    @variant lg {
      font-size: 44px;
    }
    @variant xl {
      font-size: 48px;
    }
  }

  .text-h3 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 26px;
    line-height: 1.15;

    @variant md {
      font-size: 32px;
    }
    @variant lg {
      font-size: 38px;
    }
    @variant xl {
      font-size: 42px;
    }
  }

  .text-h4 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 24px;
    line-height: 1.15;
    @variant md {
      font-size: 28px;
    }
    @variant lg {
      font-size: 30px;
    }
    @variant xl {
      font-size: 34px;
    }
  }

  .text-h5 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 21px;
    line-height: 1.1;
    @variant xl {
      font-size: 24px;
    }
  }

  .text-h6 {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 14px;
    line-height: 1.3;
  }

  .text-subtitle {
    font-family: var(--font-base);
    font-weight: 800;
    font-size: 16px;
    line-height: 1.2;
    text-transform: uppercase;
    @variant md {
      font-size: 18px;
    }
    @variant xl {
      font-size: 21px;
    }
  }

  .text-paragraph {
    font-family: var(--font-base);
    font-weight: 400;
    font-size: 14px;
    line-height: 1.6;
    @variant md {
      font-size: 18px;
    }
    @variant xl {
      font-size: 20px;
    }
  }

  .text-tag {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 10px;
    line-height: 1;
    text-transform: uppercase;
  }

  .text-button-default {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 13px;
    line-height: 1;
    text-transform: uppercase;
  }

  .text-button-big {
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 14px;
    line-height: 1;
    text-transform: uppercase;
    @variant md {
      font-size: 15px;
    }
    @variant lg {
      font-size: 16px;
    }
  }

  .text-quote {
    font-family: var(--font-base);
    font-weight: 400;
    font-size: 18px;
    line-height: 1.4;
    text-indent: 32px;
    @variant md {
      font-size: 21px;
    }
    @variant lg {
      font-size: 28px;
    }
  }
}
