'use client';

import React from 'react';
import Select from 'react-select';
import { useIsClient } from 'usehooks-ts';

import { useCurrentLocale } from '@/hooks/i18n';

import { getCommonProps } from '../props';
import { SelectLayout } from '../SelectLayout';
import { SelectOption, SelectProps } from '../types';
import { SingleSelectPlaceholder } from './SingleSelectPlaceholder';

export interface SingleSelectProps extends SelectProps {
  value: string;
  onSelect: (v: string) => void;
}

export const SingleSelect = ({
  options,
  value,
  placeholder = '',
  label,
  error,
  onSelect,
  ...rest
}: SingleSelectProps) => {
  const isClient = useIsClient();
  const locale = useCurrentLocale();

  const selectedOption = options.find((o) => o.value === value) ?? null;

  let SelectComponent = <SingleSelectPlaceholder placeholder={placeholder} value={selectedOption?.value ?? ''} />;
  if (isClient) {
    SelectComponent = (
      <Select<SelectOption, false>
        isMulti={false}
        {...getCommonProps(locale)}
        options={options}
        placeholder={placeholder}
        value={selectedOption}
        onChange={(o) => onSelect(o?.value ?? '')}
        {...rest}
      />
    );
  }

  return (
    <SelectLayout error={error} label={label}>
      {SelectComponent}
    </SelectLayout>
  );
};
