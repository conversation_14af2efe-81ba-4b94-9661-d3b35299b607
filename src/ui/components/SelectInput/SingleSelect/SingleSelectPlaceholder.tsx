import clsx from 'clsx';
import { GoTriangleDown } from 'react-icons/go';

import { SingleSelectProps } from './SingleSelect';

// react-select causes hydration error because there is a differing value between server & client for `aria-activedescendant` on input component
// providing id with useId() doesn't seem to resolve the issue completely
// see https://github.com/JedWatson/react-select/issues/2629

type Props = Pick<SingleSelectProps, 'value' | 'placeholder'>;

export const SingleSelectPlaceholder = ({ value, placeholder }: Props) => {
  return (
    <div
      className={clsx(
        'flex cursor-pointer items-center justify-between',
        'bg-white-dirty border-gray rounded-lg border px-3 py-[11px] lg:rounded-[14px] lg:px-5 lg:py-[14.5px]',
      )}
    >
      <p
        className={clsx(
          'font-base text-sm leading-normal lg:text-lg',
          value ? 'text-dark-default font-bold' : 'text-gray-dark font-normal',
        )}
      >
        {value || placeholder}
      </p>
      <GoTriangleDown className="text-dark-default me-[-3px] size-6 transition-transform lg:me-[-7px]" />
    </div>
  );
};
