import clsx from 'clsx';
import React from 'react';
import { GoTriangleDown } from 'react-icons/go';
import {
  ClassNamesConfig,
  components as selectComponents,
  DropdownIndicatorProps,
  GroupBase,
  OptionProps,
  SelectComponentsConfig,
} from 'react-select';

import { Locale } from '@/hooks/i18n/const';

import { SelectOption } from './types';

const DropdownIndicator = ({ ...props }: DropdownIndicatorProps<SelectOption>) => {
  return (
    <selectComponents.DropdownIndicator {...props} className="me-[-3px] lg:me-[-7px]">
      <GoTriangleDown
        className={clsx('text-dark-default size-6 transition-transform', props.selectProps.menuIsOpen && 'rotate-180')}
      />
    </selectComponents.DropdownIndicator>
  );
};

const Option = ({ children, ...props }: OptionProps<SelectOption>) => {
  return (
    <selectComponents.Option
      {...props}
      className={clsx(
        'group !cursor-pointer px-3 py-2 transition-colors duration-50 lg:px-5 lg:py-[14.5px]',
        'border-gray not-last:border-b last:rounded-b-[14px]',
        props.isFocused && 'bg-white',
      )}
    >
      <span
        className={clsx(
          'inline-block text-sm leading-normal font-semibold transition-transform duration-50 lg:text-lg',
          props.isFocused && 'translate-x-1.5',
        )}
      >
        {children}
      </span>
    </selectComponents.Option>
  );
};

const components: SelectComponentsConfig<SelectOption, boolean, GroupBase<SelectOption>> = {
  MultiValue: () => null,
  ClearIndicator: () => null,
  DropdownIndicator,
  Option,
};

const classNames: ClassNamesConfig<SelectOption> = {
  container: () => 'font-base text-sm leading-normal font-normal lg:text-lg text-dark-default',
  control: (props) =>
    clsx(
      'bg-white-dirty border-gray rounded-t-lg border px-3 py-[11px] lg:rounded-t-[14px] lg:px-5 lg:py-[14.5px] !cursor-pointer',
      props.menuIsOpen ? 'rounded-b-none' : 'rounded-b-lg lg:rounded-b-[14px]',
    ),
  input: () => 'font-bold',
  placeholder: () => 'text-gray-dark',
  singleValue: () => 'font-bold',
  menu: () => 'bg-white-dirty border-gray flex flex-col rounded-b-lg border lg:rounded-b-[14px]',
  noOptionsMessage: () => 'px-3 py-2 font-semibold lg:px-5 lg:py-[14.5px]',
};

const noOptionsMessageMap: Record<Locale, string> = {
  en: 'No options',
  zh: '没有选项',
  ar: 'لا توجد خيارات',
};

export function getCommonProps(locale: Locale) {
  return {
    components,
    classNames,
    unstyled: true,
    noOptionsMessage: () => noOptionsMessageMap[locale],
  };
}
