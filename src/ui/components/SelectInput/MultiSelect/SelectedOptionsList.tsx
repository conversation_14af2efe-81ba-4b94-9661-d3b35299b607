import clsx from 'clsx';
import { IoCloseSharp } from 'react-icons/io5';

import { SelectOption } from '../types';

interface Props {
  selectedOptions: SelectOption[];
  onDiscard: (v: string) => void;
}

export const SelectedOptionsList = ({ selectedOptions, onDiscard }: Props) => {
  if (selectedOptions.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2">
      {selectedOptions.map((o) => (
        <SelectedValue key={o.value} text={o.label} onDiscard={() => onDiscard(o.value)} />
      ))}
    </div>
  );
};

const SelectedValue = ({ text, onDiscard }: { text: string; onDiscard: () => void }) => {
  return (
    <div
      className={clsx(
        'flex max-w-full items-center gap-[14px] px-5 py-4',
        'bg-white-dirty border-dark-default rounded-[10px] border shadow-md',
      )}
    >
      <p className="font-primary truncate text-sm leading-[1.1] font-bold whitespace-nowrap uppercase">{text}</p>
      <div className="bg-dark-default h-[15px] w-px" />
      <IoCloseSharp className="text-dark-default size-4 shrink-0 cursor-pointer" onClick={onDiscard} />
    </div>
  );
};
