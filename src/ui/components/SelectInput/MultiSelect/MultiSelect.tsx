'use client';

import React, { useState } from 'react';
import CreatableSelect from 'react-select/creatable';
import { useIsClient } from 'usehooks-ts';

import { useCurrentLocale } from '@/hooks/i18n';

import { getCommonProps } from '../props';
import { SelectLayout } from '../SelectLayout';
import { SelectOption, SelectProps } from '../types';
import { MultiSelectPlaceholder } from './MultiSelectPlaceholder';
import { SelectedOptionsList } from './SelectedOptionsList';

export interface MultiSelectProps extends SelectProps {
  values: string[];
  onSelect: (v: string[]) => void;
}

export const MultiSelect = ({
  options: initialOptions,
  values,
  placeholder = '',
  label,
  error,
  onSelect,
  ...rest
}: MultiSelectProps) => {
  const isClient = useIsClient();
  const locale = useCurrentLocale();

  const { options, setOptions, selectedOptions } = useOptions(values, initialOptions);

  let SelectComponent = <MultiSelectPlaceholder options={selectedOptions} placeholder={placeholder} />;
  if (isClient) {
    SelectComponent = (
      <>
        <CreatableSelect<SelectOption, true>
          {...getCommonProps(locale)}
          isMulti
          options={options}
          placeholder={placeholder}
          value={selectedOptions}
          onChange={(o) => {
            const selectedValues = o.map((o) => o.value);
            onSelect(selectedValues);
          }}
          onCreateOption={(v) => {
            const optionFromValue = { label: v, value: v };
            setOptions((prevOptions) => [...prevOptions, optionFromValue]);
            onSelect([...values, v]);
          }}
          {...rest}
        />
        <SelectedOptionsList
          selectedOptions={selectedOptions}
          onDiscard={(v) => {
            const updatedValues = values.filter((val) => val !== v);
            onSelect(updatedValues);
          }}
        />
      </>
    );
  }

  return (
    <SelectLayout error={error} label={label}>
      <div className="flex flex-col gap-2">{SelectComponent}</div>
    </SelectLayout>
  );
};

function useOptions(values: string[], initialOptions: SelectOption[]) {
  const optionsFromValues = values.map((v) => ({ label: v, value: v }));

  const [options, setOptions] = useState<SelectOption[]>([...optionsFromValues, ...initialOptions]);
  const selectedOptions = values.map((v) => options.find((o) => o.value === v) as SelectOption);

  return { options, setOptions, selectedOptions };
}
