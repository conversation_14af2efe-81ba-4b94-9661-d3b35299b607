import clsx from 'clsx';
import { GoTriangleDown } from 'react-icons/go';

import { MultiSelectProps } from './MultiSelect';
import { SelectedOptionsList } from './SelectedOptionsList';

// react-select causes hydration error because there is a differing value between server & client for `aria-activedescendant` on input component
// providing id with useId() doesn't seem to resolve the issue completely
// see https://github.com/JedWatson/react-select/issues/2629

type Props = Pick<MultiSelectProps, 'options' | 'placeholder'>;

export const MultiSelectPlaceholder = ({ options, placeholder }: Props) => {
  const areOptionsVisible = options.length !== 0;
  return (
    <>
      <div
        className={clsx(
          'flex cursor-pointer items-center justify-between',
          'bg-white-dirty border-gray rounded-lg border px-3 py-[11px] lg:rounded-[14px] lg:px-5 lg:py-[14.5px]',
        )}
      >
        <p className="font-base text-gray-dark text-sm leading-normal font-normal lg:text-lg">
          {areOptionsVisible ? '' : placeholder}
        </p>
        <GoTriangleDown className="text-dark-default me-[-3px] size-6 transition-transform lg:me-[-7px]" />
      </div>
      <SelectedOptionsList selectedOptions={options} onDiscard={() => undefined} />
    </>
  );
};
