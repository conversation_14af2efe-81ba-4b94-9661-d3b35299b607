import { ErrorLabel, InputLabel } from '../TextInput/InputLabel';

type Props = React.PropsWithChildren<{
  label?: string;
  error?: string;
}>;

export const SelectLayout = ({ label, error, children }: Props) => {
  return (
    <div className="flex w-full min-w-0 flex-col gap-1.5">
      {label && <InputLabel text={label} />}
      {children}
      {error && <ErrorLabel text={error} />}
    </div>
  );
};
