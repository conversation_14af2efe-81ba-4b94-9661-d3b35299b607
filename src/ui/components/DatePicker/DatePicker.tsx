'use client';

import './styles.css';

import { formatISO } from 'date-fns';
import { DayPicker } from 'react-day-picker';
import { ar, enUS, Locale as DPLocale, zhCN } from 'react-day-picker/locale';

import { useCurrentLocale } from '@/hooks/i18n';
import { Locale } from '@/hooks/i18n/const';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { Popover } from '../Popover';

export interface DatePickerProps {
  date: string;
  onDateSelect: (d: string) => void;
  trigger: React.ReactElement;
  areFutureDatesDisabled?: boolean;
}

export function DatePicker({ date, onDateSelect, trigger, areFutureDatesDisabled = false }: DatePickerProps) {
  const { isSm } = useScreenType();
  const locale = useCurrentLocale();

  return (
    <Popover align={isSm ? 'center' : 'start'} trigger={trigger}>
      <DayPicker
        animate
        captionLayout="dropdown-years"
        disabled={areFutureDatesDisabled ? { after: new Date() } : undefined}
        locale={localeMap[locale]}
        mode="single"
        selected={date ? new Date(date) : undefined}
        onSelect={(d) => (d ? onDateSelect(formatISO(d, { representation: 'date' })) : onDateSelect(''))}
      />
    </Popover>
  );
}

const localeMap: Record<Locale, DPLocale> = {
  en: enUS,
  ar: ar,
  zh: zhCN,
};
