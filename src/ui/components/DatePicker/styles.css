@import 'react-day-picker/style.css';

.rdp-root {
  --rdp-nav_button-width: 30px;
  --rdp-nav_button-height: 30px;
  .rdp-nav {
    gap: 6px;
  }
  .rdp-chevron {
    display: inline-block;
    fill: var(--color-gray-dark);
  }

  .rdp-month_caption {
    color: var(--color-dark-default);
    font-weight: 700;
    font-size: 21px;
    line-height: 1;
    padding-inline-start: 8px;
  }

  .rdp-dropdown_root {
    cursor: pointer;
  }

  .rdp-month_grid {
    margin-top: 12px;
  }

  .rdp-weekday {
    font-weight: 800;
    font-size: 16px;
    line-height: 1;
  }

  .rdp-week {
    color: var(--color-dark-default);
    font-weight: 400;
    font-size: 16px;
    line-height: 1;
  }

  --rdp-day-width: 43px;
  --rdp-day-height: 43px;
  --rdp-day_button-width: 43px;
  --rdp-day_button-height: 43px;
  --rdp-today-color: var(--color-gold-primary);
  --rdp-selected-border: 0px;

  .rdp-day {
    @apply rounded-full transition-colors duration-50;
    &:hover:not(.rdp-selected) {
      background-color: white;
    }
  }
  .rdp-selected {
    background-color: var(--color-gold-primary);
    color: white;
    font-size: 16px;
  }

  .rdp-today:not(.rdp-outside) {
    font-weight: 800;
  }
}
