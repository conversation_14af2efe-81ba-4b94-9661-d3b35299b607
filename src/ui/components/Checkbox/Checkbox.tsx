import clsx from 'clsx';
import { FaCheck } from 'react-icons/fa';

import { SimpleRichTextContent } from '@/components/SimpleRichTextContent';
import { RichTextContentType } from '@/strapi/types/helper';

export interface CheckboxProps {
  value: boolean;
  onChange: (v: boolean) => void;
  label: string | RichTextContentType;
}

export const Checkbox = ({ value, onChange, label }: CheckboxProps) => {
  return (
    <label className="relative flex cursor-pointer items-start gap-2">
      <input checked={value} className="peer absolute size-0" type="checkbox" onChange={() => onChange(!value)} />
      <div
        className={clsx(
          'flex size-[23px] shrink-0 items-center justify-center rounded-sm transition-colors duration-75',
          value ? 'bg-dark-default' : 'bg-white-dirty border-gray border',
          'peer-focus-visible:ring-2 peer-focus-visible:ring-[#3b82f6]',
        )}
      >
        <FaCheck
          className={clsx(
            'size-[15px] shrink-0 text-white transition-opacity duration-75',
            value ? 'opacity-100' : 'opacity-0',
          )}
        />
      </div>
      <div className="text-dark-default font-base mt-[3px] text-sm font-medium">
        {typeof label === 'string' ? (
          <p>{label}</p>
        ) : (
          <SimpleRichTextContent content={label} linkClassname="underline text-gold-primary" />
        )}
      </div>
    </label>
  );
};
