interface BasePagingProps {
  pages: number;
  currentPage: number;
}

export interface ClientPagingProps extends BasePagingProps {
  mode: 'client';
  onPageChange: (page: number) => void;
}

export interface URLPagingProps extends BasePagingProps {
  mode: 'url';
  pathname: string;
  queryParams?: Record<string, string>;
}

export type PagingProps = ClientPagingProps | URLPagingProps;

export interface ButtonProps {
  isSelected?: boolean;
  children: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
}

export interface PageButtonProps {
  page: number;
  isSelected?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
  mode: 'client' | 'url';
  pathname?: string;
  queryParams?: Record<string, string>;
  disabled?: boolean;
}
