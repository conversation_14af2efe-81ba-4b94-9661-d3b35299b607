'use client';

import { MdChevronLeft, MdChevronRight } from 'react-icons/md';

import { PageButton } from './components';
import { PagingProps } from './types';
import { constructPagingArray } from './utils/constructPagingArray';

export const Paging = (props: PagingProps) => {
  let visiblePages: { page: number; text: string }[] = [];
  switch (true) {
    case props.pages <= 5:
      visiblePages = [...Array(props.pages)].map((_, i) => ({ page: i + 1, text: `${i + 1}` }));
      break;
    case props.currentPage <= 3:
      visiblePages = constructPagingArray('head', props.pages, props.currentPage);
      break;
    case props.currentPage >= props.pages - 2:
      visiblePages = constructPagingArray('tail', props.pages, props.currentPage);
      break;
    default:
      visiblePages = constructPagingArray('middle', props.pages, props.currentPage);
      break;
  }

  if (props.mode === 'client') {
    return (
      <div className="flex gap-2">
        <PageButton
          disabled={props.currentPage <= 1}
          mode="client"
          page={props.currentPage - 1}
          onClick={() => props.onPageChange(Math.max(1, props.currentPage - 1))}
        >
          <MdChevronLeft className="rtl:rotate-180" size={18} />
        </PageButton>

        {visiblePages.map((p) => (
          <PageButton
            isSelected={props.currentPage === p.page}
            key={p.page}
            mode="client"
            page={p.page}
            onClick={() => props.onPageChange(p.page)}
          >
            {p.text}
          </PageButton>
        ))}

        <PageButton
          disabled={props.currentPage >= props.pages}
          mode="client"
          page={props.currentPage + 1}
          onClick={() => props.onPageChange(Math.min(props.pages, props.currentPage + 1))}
        >
          <MdChevronRight className="rtl:rotate-180" size={18} />
        </PageButton>
      </div>
    );
  }

  return (
    <div className="flex gap-2">
      <PageButton
        disabled={props.currentPage <= 1}
        mode="url"
        page={props.currentPage - 1}
        pathname={props.pathname}
        queryParams={props.queryParams}
      >
        <MdChevronLeft className="rtl:rotate-180" size={18} />
      </PageButton>

      {visiblePages.map((p) => (
        <PageButton
          isSelected={props.currentPage === p.page}
          key={p.page}
          mode="url"
          page={p.page}
          pathname={props.pathname}
          queryParams={props.queryParams}
        >
          {p.text}
        </PageButton>
      ))}

      <PageButton
        disabled={props.currentPage >= props.pages}
        mode="url"
        page={props.currentPage + 1}
        pathname={props.pathname}
        queryParams={props.queryParams}
      >
        <MdChevronRight className="rtl:rotate-180" size={18} />
      </PageButton>
    </div>
  );
};
