export function constructPagingArray(type: 'head' | 'tail' | 'middle', pages: number, currentPage: number) {
  if (type === 'head') {
    return [
      { page: 1, text: '1' },
      { page: 2, text: '2' },
      { page: 3, text: '3' },
      { page: 4, text: '...' },
      { page: pages, text: `${pages}` },
    ];
  }

  if (type === 'tail') {
    return [
      { page: 1, text: '1' },
      { page: pages - 3, text: '...' },
      { page: pages - 2, text: `${pages - 2}` },
      { page: pages - 1, text: `${pages - 1}` },
      { page: pages, text: `${pages}` },
    ];
  }

  return [
    { page: 1, text: '1' },
    { page: currentPage - 1, text: '...' },
    { page: currentPage, text: `${currentPage}` },
    { page: currentPage + 1, text: `...` },
    { page: pages, text: `${pages}` },
  ];
}
