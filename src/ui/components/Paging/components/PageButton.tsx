'use client';

import { LocalizedLink } from '@/components/LocalizedLink';

import { PageButtonProps } from '../types';
import { Button } from './Button';

export const PageButton = ({
  page,
  isSelected,
  onClick,
  children,
  mode,
  pathname,
  queryParams,
  disabled,
}: PageButtonProps) => {
  if (mode === 'client') {
    return (
      <Button disabled={disabled} isSelected={isSelected} onClick={onClick}>
        {children}
      </Button>
    );
  }

  if (disabled) {
    return <Button disabled>{children}</Button>;
  }

  const query = { ...queryParams };
  if (page > 0) {
    query.page = page.toString();
  }

  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Paging', button_name: `Page ${page} Link` }}
      href={{ pathname, query }}
    >
      <Button isSelected={isSelected}>{children}</Button>
    </LocalizedLink>
  );
};
