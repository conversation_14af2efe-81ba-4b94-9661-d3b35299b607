'use client';

import clsx from 'clsx';

import { ButtonProps } from '../types';

export const Button = ({ isSelected, children, disabled, onClick }: ButtonProps) => (
  <button
    className={clsx(
      'flex size-[38px] items-center justify-center rounded-lg shadow-[4px_8px_16px_0px_#00000014]',
      'font-primary text-sm leading-normal font-bold uppercase',
      isSelected ? 'bg-gold-light text-white' : 'bg-white text-black',
      !isSelected && !disabled && 'cursor-pointer',
      disabled && 'cursor-not-allowed opacity-50',
    )}
    disabled={disabled}
    type="button"
    onClick={onClick}
  >
    {children}
  </button>
);
