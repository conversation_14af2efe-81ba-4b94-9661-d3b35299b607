'use client';

type ScreenType = 'sm' | 'md' | 'lg' | 'xl' | '2xl';
type MiddleScreenType = Exclude<ScreenType, 'sm' | '2xl'>;
type ExtendedScreenType = ScreenType | `${MiddleScreenType}AndAbove` | `${MiddleScreenType}AndBelow`;

interface Props {
  for: ExtendedScreenType;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const Only = (props: Props) => {
  switch (props.for) {
    case 'sm':
      return (
        <>
          <div className="hidden sm:max-md:contents">{props.children}</div>
          {props.fallback && <div className="hidden md:contents">{props.fallback}</div>}
        </>
      );
    case 'md':
      return (
        <>
          <div className="hidden md:max-lg:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-md:contents lg:contents">{props.fallback}</div>}
        </>
      );
    case 'lg':
      return (
        <>
          <div className="hidden lg:max-xl:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-lg:contents xl:contents">{props.fallback}</div>}
        </>
      );
    case 'xl':
      return (
        <>
          <div className="hidden xl:max-2xl:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-xl:contents 2xl:contents">{props.fallback}</div>}
        </>
      );
    case '2xl':
      return (
        <>
          <div className="hidden 2xl:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-2xl:contents">{props.fallback}</div>}
        </>
      );
    case 'mdAndBelow':
      return (
        <>
          <div className="hidden max-lg:contents">{props.children}</div>
          {props.fallback && <div className="hidden lg:contents">{props.fallback}</div>}
        </>
      );
    case 'mdAndAbove':
      return (
        <>
          <div className="hidden md:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-md:contents">{props.fallback}</div>}
        </>
      );
    case 'lgAndBelow':
      return (
        <>
          <div className="hidden max-xl:contents">{props.children}</div>
          {props.fallback && <div className="hidden xl:contents">{props.fallback}</div>}
        </>
      );
    case 'lgAndAbove':
      return (
        <>
          <div className="hidden lg:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-lg:contents">{props.fallback}</div>}
        </>
      );
    case 'xlAndBelow':
      return (
        <>
          <div className="hidden max-2xl:contents">{props.children}</div>
          {props.fallback && <div className="hidden 2xl:contents">{props.fallback}</div>}
        </>
      );
    case 'xlAndAbove':
      return (
        <>
          <div className="hidden xl:contents">{props.children}</div>
          {props.fallback && <div className="hidden max-xl:contents">{props.fallback}</div>}
        </>
      );
    default:
      return null;
  }
};
