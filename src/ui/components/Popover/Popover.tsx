import * as PopoverPrimitive from '@radix-ui/react-popover';
import * as React from 'react';

type PopoverPrimitiveProps = Pick<PopoverPrimitive.PopoverContentProps, 'align'>;

type Props = React.PropsWithChildren<
  PopoverPrimitiveProps & {
    trigger: React.ReactElement;
  }
>;

export const Popover = ({ trigger, align = 'start', children }: Props) => {
  return (
    <PopoverPrimitive.Root>
      <PopoverPrimitive.Trigger asChild>{trigger}</PopoverPrimitive.Trigger>
      <PopoverPrimitive.Portal>
        <PopoverPrimitive.Content
          align={align}
          className="bg-white-dirty border-gray rounded-[14px] border p-5 shadow-sm"
          sideOffset={4}
        >
          {children}
        </PopoverPrimitive.Content>
      </PopoverPrimitive.Portal>
    </PopoverPrimitive.Root>
  );
};
