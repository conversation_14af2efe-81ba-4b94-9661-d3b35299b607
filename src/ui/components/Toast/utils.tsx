import { IoIosCheckmarkCircleOutline, IoMdInformationCircleOutline } from 'react-icons/io';
import { MdErrorOutline, MdOutlineWarningAmber } from 'react-icons/md';
import { toast as sonnerToast } from 'sonner';

import { Toast, ToastProps } from './Toast';

type ShowToastProps = Omit<ToastProps, 'id'> & { duration?: number };

export function showToast({ duration, ...rest }: ShowToastProps) {
  return sonnerToast.custom((id) => <Toast {...rest} id={id} />, { duration });
}

type ShowVariantToastProps = Omit<ShowToastProps, 'variant' | 'IconLeft'>;

export function showSuccessToast(props: ShowVariantToastProps) {
  return showToast({ variant: 'success', IconLeft: IoIosCheckmarkCircleOutline, ...props });
}

export function showWarningToast(props: ShowVariantToastProps) {
  return showToast({ variant: 'warning', IconLeft: MdOutlineWarningAmber, ...props });
}

export function showInfoToast(props: ShowVariantToastProps) {
  return showToast({ variant: 'info', IconLeft: IoMdInformationCircleOutline, ...props });
}

export function showErrorToast(props: ShowVariantToastProps) {
  return showToast({ variant: 'error', IconLeft: MdErrorOutline, ...props });
}
