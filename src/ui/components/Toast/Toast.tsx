'use client';

import clsx from 'clsx';
import React from 'react';
import { IconType } from 'react-icons';
import { toast as sonnerToast } from 'sonner';

import { logButtonClickedEvent } from '@/services/braze';

import { buttonColorMap, shadowColorMap, textColorMap, Variant } from './styles';

export interface ToastProps {
  id: string | number;
  title: string;
  variant?: Variant;
  IconLeft?: IconType;
  description?: string;
  buttonText?: string;
  onClick?: () => void;
}

export const Toast = ({ title, description, variant = 'basic', IconLeft, buttonText, onClick, id }: ToastProps) => {
  return (
    <div
      className={clsx(
        'flex w-full gap-4 p-4 max-lg:flex-col lg:max-w-[777px] lg:items-center lg:gap-5 lg:p-5',
        'min-[600px]:max-lg:ms-[calc(var(--offset-left)*2)] min-[600px]:max-lg:max-w-[calc(100%-var(--offset-left)*2)]',
        'rounded-lg bg-white shadow-[0px_12px_16px_0px] lg:rounded-2xl',
        shadowColorMap[variant],
        textColorMap[variant],
      )}
    >
      <div className="flex grow gap-3">
        {IconLeft && (
          <div className="size-[22px] shrink-0 p-px">
            <IconLeft className="size-full" />
          </div>
        )}
        <div className={clsx('flex flex-col gap-2 lg:gap-2.5', IconLeft && 'mt-0.5')}>
          <p className="font-primary text-lg leading-none font-bold">{title}</p>
          {description && <p className="font-base text-base font-normal">{description}</p>}
        </div>
      </div>
      {buttonText && (
        <button
          className={clsx(
            'rounded-lg px-[28px] py-4 max-lg:w-full lg:rounded-[14px] lg:py-[18px]',
            'text-button-default cursor-pointer whitespace-nowrap uppercase',
            buttonColorMap[variant],
          )}
          onClick={() => {
            onClick?.();
            sonnerToast.dismiss(id);
            logButtonClickedEvent({
              location: `Toast Notification Component (${title})`,
              button_name: `Primary Button (${buttonText})`,
            });
          }}
        >
          {buttonText}
        </button>
      )}
    </div>
  );
};
