export type Variant = 'basic' | 'warning' | 'error' | 'success' | 'info';

export const textColorMap: Record<Variant, string> = {
  basic: 'text-dark-default',
  warning: 'text-[#987C4B]',
  error: 'text-[#AD3838]',
  success: 'text-[#38ADA3]',
  info: 'text-[#3879AD]',
};

export const buttonColorMap: Record<Variant, string> = {
  basic: 'bg-white-dirty',
  warning: 'bg-[#987C4B1A]',
  error: 'bg-[#AD38381A]',
  success: 'bg-[#38ADA31A]',
  info: 'bg-[#3879AD1A]',
};

export const shadowColorMap: Record<Variant, string> = {
  basic: 'shadow-[#0000000F]',
  warning: 'shadow-[#987C4B0F]',
  error: 'shadow-[#AD38380F]',
  success: 'shadow-[#38AD8C0F]',
  info: 'shadow-[#3879AD0F]',
};
