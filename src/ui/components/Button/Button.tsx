'use client';

import clsx from 'clsx';

import { LocalizedLink } from '@/components/LocalizedLink';
import { logButtonClickedEvent } from '@/services/braze';

import { ButtonSize, ButtonVariant, sizeMapper, variantMapper } from './variants';

type DefaultProps = Pick<React.ComponentPropsWithoutRef<'button'>, 'onClick' | 'type'>;

type Props = {
  text?: string | null;
  link?: string | null;
  variant?: ButtonVariant | null;
  size?: ButtonSize | null;
  openInNewTab?: boolean | null;
  isDisabled?: boolean | null;
  isFullWidth?: boolean;
  brazeEventProperties: { button_name: string; location: string };
} & DefaultProps;

export const Button = ({
  text,
  variant,
  size,
  link,
  openInNewTab,
  isDisabled,
  isFullWidth,
  onClick,
  brazeEventProperties,
  ...rest
}: Props) => {
  const defaultStyles = isFullWidth ? 'w-full' : 'w-fit';

  const ButtonComponent = (
    <button
      className={clsx(
        'disabled:bg-dark-default/15 h-fit transition-colors not-disabled:cursor-pointer disabled:text-white',
        defaultStyles,
        variantMapper[variant ?? 'primary'],
        sizeMapper[size ?? 'default'],
      )}
      disabled={!!isDisabled}
      onClick={(e) => {
        onClick?.(e);
        if (!link) {
          logButtonClickedEvent(brazeEventProperties);
        }
      }}
      {...rest}
    >
      <p className="leading-[130%]">{text ?? ''}</p>
    </button>
  );

  if (link) {
    return (
      <LocalizedLink
        brazeEventProperties={brazeEventProperties}
        className={clsx(defaultStyles, !!isDisabled && 'focus-visible:outline-0')}
        href={link}
        target={openInNewTab ? '_blank' : '_self'}
      >
        {ButtonComponent}
      </LocalizedLink>
    );
  }

  return ButtonComponent;
};
