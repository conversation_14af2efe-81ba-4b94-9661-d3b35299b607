export type ButtonVariant = keyof typeof variantMapper;
export type ButtonSize = keyof typeof sizeMapper;

export const variantMapper = {
  primary: 'bg-dark-default hover:bg-[#212121] text-white',
  secondary: 'text-dark-default bg-gray hover:bg-gray-easy',
  gold:
    'not-disabled:bg-[radial-gradient(122.78%_179%_at_50.21%_0%,_#F2C575_0%,_#987C4B_40.5%,_#4E442D_92.88%)] ' +
    'not-disabled:hover:bg-[radial-gradient(122.78%_179%_at_50.21%_0%,_#F4CE8A_0%,_#AB8B54_40.5%,_#615538_92.88%)] ' +
    'text-white',
  glassy: 'bg-white/20 hover:bg-white/30 text-white backdrop-blur-[32px]',
  red: 'bg-[#F40F30] text-white hover:bg-[#E60E2C]',
};

export const sizeMapper = {
  default: 'px-8 py-4 lg:py-[18px] rounded-lg lg:rounded-[14px] text-button-default',
  big: 'px-8 py-5 lg:px-9 lg:py-6 rounded-[18px] lg:rounded-3xl text-button-big',
  small: 'py-2.5 px-4 rounded-sm text-button-default',
};
