import React from 'react';

interface MarqueeProps {
  repeat?: number;
  duration?: string;
  gap?: string;
  pauseOnHover?: boolean;
  children: React.ReactNode;
}

export const Marquee = ({
  repeat = 10,
  duration = '10s',
  gap = '16px',
  pauseOnHover = false,
  children,
}: MarqueeProps) => {
  const childItems = [...Array(repeat)].map((_, i) => <React.Fragment key={i}>{children}</React.Fragment>);

  return (
    <div
      className="group/marquee flex w-full gap-[var(--marquee-gap)] overflow-x-hidden select-none rtl:flex-row-reverse"
      style={{ '--marquee-gap': gap } as React.CSSProperties}
    >
      <MarqueeList duration={duration} pauseOnHover={pauseOnHover}>
        {childItems}
      </MarqueeList>
      <MarqueeList duration={duration} isHidden pauseOnHover={pauseOnHover}>
        {childItems}
      </MarqueeList>
    </div>
  );
};

type MarqueeListProps = Pick<MarqueeProps, 'duration' | 'children' | 'pauseOnHover'> & { isHidden?: boolean };

const MarqueeList = ({ duration, isHidden, pauseOnHover, children }: MarqueeListProps) => (
  <ul
    aria-hidden={isHidden}
    className={`animate-marquee-scroll flex min-w-full shrink-0 justify-around gap-[var(--marquee-gap)] ${
      pauseOnHover ? 'group-hover/marquee:[animation-play-state:paused]' : ''
    }`}
    style={{ animationDuration: duration }}
  >
    {children}
  </ul>
);
