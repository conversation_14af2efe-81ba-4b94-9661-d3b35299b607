'use client';

import clsx from 'clsx';

import { logButtonClickedEvent } from '@/services/braze';

interface Props {
  text: string;
  type?: 'danger' | 'warning';
  iconLeft?: React.ReactElement;
  button?: { text: string; onClick: () => void };
}

export const Alert = ({ text, type = 'danger', iconLeft, button }: Props) => {
  return (
    <div
      className={clsx('flex justify-between gap-3 rounded-xl border px-5 py-3', {
        'border-[#AD38381A] bg-[#AD38380D] text-[#AD3838]': type === 'danger',
        'border-[#987C4B1A] bg-[#987C4B0D] text-[#987C4B]': type === 'warning',
      })}
    >
      <div className="flex gap-2">
        <div className="size-[22px] shrink-0">{iconLeft}</div>
        <p className={clsx('font-base text-sm leading-normal font-bold lg:text-base', !!iconLeft && 'max-lg:mt-0.5')}>
          {text}
        </p>
      </div>
      {button && (
        <button
          className={clsx('font-primary rounded-sm px-[14px] py-2 text-[10px] leading-none font-bold uppercase', {
            'bg-[#AD3838] text-white': type === 'danger',
          })}
          onClick={() => {
            button.onClick();
            logButtonClickedEvent({ location: 'Alert Component', button_name: `Primary Button (${text})` });
          }}
        >
          {button.text}
        </button>
      )}
    </div>
  );
};
