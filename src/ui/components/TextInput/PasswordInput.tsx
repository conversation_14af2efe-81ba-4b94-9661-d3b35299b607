'use client';

import { FaEye, FaEyeSlash } from 'react-icons/fa6';
import { useToggle } from 'usehooks-ts';

import { TextInput, TextInputProps } from './TextInput';

type Props = Omit<TextInputProps, 'iconRight' | 'onClickIconRight'>;

export const PasswordInput = ({ ...rest }: Props) => {
  const [isPasswordVisible, toggleIsPasswordVisible] = useToggle(false);

  return (
    <TextInput
      {...rest}
      iconRight={
        isPasswordVisible ? (
          <FaEyeSlash className="text-dark-default size-[17px] lg:size-[25px]" />
        ) : (
          <FaEye className="text-dark-default size-[18px] lg:size-6" />
        )
      }
      type={isPasswordVisible ? 'text' : 'password'}
      onClickIconRight={toggleIsPasswordVisible}
    />
  );
};
