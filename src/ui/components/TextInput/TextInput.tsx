import clsx from 'clsx';
import { forwardRef } from 'react';

import { ErrorLabel, InputLabel } from './InputLabel';

export interface TextInputProps {
  value: string;
  placeholder?: string;
  name?: string;
  label?: string;
  error?: string;
  isDisabled?: boolean;
  iconRight?: React.ReactElement;
  type?: 'text' | 'password';
  onChange?: (v: string) => void;
  onBlur?: () => void;
  onClickIconRight?: () => void;
}

export const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      value,
      placeholder,
      name,
      label,
      error,
      isDisabled,
      iconRight,
      type = 'text',
      onChange,
      onBlur,
      onClickIconRight,
      ...rest
    },
    ref,
  ) => {
    return (
      <div className="flex w-full min-w-0 flex-col gap-1.5">
        {label && <InputLabel text={label} />}
        <div
          className={clsx(
            'relative flex items-center justify-between gap-2.5',
            'bg-white-dirty border-gray rounded-lg border lg:rounded-[14px]',
            !!error && 'not-focus-within:border-[#AD3838]',
            'focus-within:ring-2 focus-within:ring-[#3b82f6]',
          )}
        >
          <input
            className={clsx(
              'min-w-0 flex-1 rounded-lg px-3 py-[11px] focus-visible:outline-0 lg:rounded-[14px] lg:px-5 lg:py-[14.5px]',
              'placeholder:font-base placeholder:text-gray-dark placeholder:font-normal',
              'font-base text-dark-default text-start text-sm leading-normal font-bold lg:text-lg',
            )}
            disabled={!!isDisabled}
            name={name}
            placeholder={placeholder}
            ref={ref}
            type={type}
            value={value}
            onBlur={onBlur}
            onChange={(e) => onChange?.(e.target.value)}
            {...rest}
          />
          {iconRight && (
            <button className="absolute end-3 cursor-pointer" type="button" onClick={onClickIconRight}>
              {iconRight}
            </button>
          )}
        </div>
        {error && <ErrorLabel text={error} />}
      </div>
    );
  },
);

TextInput.displayName = 'TextInput';
