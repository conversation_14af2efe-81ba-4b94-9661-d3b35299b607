import 'client-only';

import { useEffect, useLayoutEffect, useState } from 'react';

// breakpoints are defined in @/ui/styles/space.css
const MD_BREAKPOINT = 768;
const LG_BREAKPOINT = 1200;
const XL_BREAKPOINT = 1440;
const XL2_BREAKPOINT = 1920;

function getScreenType() {
  const isSmMedia = () => matchMedia(`(max-width: ${MD_BREAKPOINT - 1}px)`).matches;
  const isMdMedia = () => matchMedia(`(min-width: ${MD_BREAKPOINT}px) and (max-width: ${LG_BREAKPOINT - 1}px)`).matches;
  const isLgMedia = () => matchMedia(`(min-width: ${LG_BREAKPOINT}px) and (max-width: ${XL_BREAKPOINT - 1}px)`).matches;
  const isXlMedia = () =>
    matchMedia(`(min-width: ${XL_BREAKPOINT}px) and (max-width: ${XL2_BREAKPOINT - 1}px)`).matches;
  const is2xlMedia = () => matchMedia(`(min-width: ${XL2_BREAKPOINT}px)`).matches;

  return { isSm: isSmMedia(), isMd: isMdMedia(), isLg: isLgMedia(), isXl: isXlMedia(), is2xl: is2xlMedia() };
}

export function useResolveScreenType() {
  const [screenType, setScreenType] = useState({ isSm: true, isMd: false, isLg: false, isXl: false, is2xl: false });

  useLayoutEffect(() => setScreenType(getScreenType()), []);

  useEffect(() => {
    const documentResizeObserver = new ResizeObserver(() => {
      const updatedScreenType = getScreenType();
      if (JSON.stringify(updatedScreenType) !== JSON.stringify(screenType)) {
        setScreenType(updatedScreenType);
      }
    });
    documentResizeObserver.observe(document.body);

    return () => documentResizeObserver.unobserve(document.body);
  }, [screenType]);

  return screenType;
}
