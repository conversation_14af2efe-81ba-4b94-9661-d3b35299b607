'use client';

import { createContext, useContext } from 'react';

import { useResolveScreenType } from './hooks';

export interface ScreenTypeValue {
  isSm: boolean;
  isMd: boolean;
  isLg: boolean;
  isXl: boolean;
  is2xl: boolean;
}

const ScreenTypeContext = createContext<ScreenTypeValue | undefined>(undefined);

export const ScreenTypeProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useResolveScreenType();
  return <ScreenTypeContext value={value}>{children}</ScreenTypeContext>;
};

export function useScreenType() {
  const state = useContext(ScreenTypeContext);

  if (!state) {
    throw new Error('useScreenType must be used within a ScreenTypeProvider!');
  }

  return state;
}
