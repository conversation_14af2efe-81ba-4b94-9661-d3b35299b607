{"format": "apollo-persisted-query-manifest", "version": 1, "operations": [{"id": "5e92a7539daee3264a6bd417592afb2f69159a4bfce71db95237a6518dd949fc", "name": "MatchSeries", "type": "query", "body": "query MatchSeries($tournamentIds: [String!]!) {\n  matchSeries(\n    filters: [{tournamentIds: $tournamentIds}]\n    limit: 100\n    sorts: [{field: START_TIME, order: ORDER_ASC}]\n  ) {\n    items {\n      id\n      status\n      startTime\n      position\n      tournament {\n        id\n        variant\n      }\n      streams {\n        url\n        language\n        primary\n      }\n      metadata {\n        id\n        type\n        position\n        name\n      }\n      contestants {\n        score\n        result\n        rank\n        points\n        team {\n          id\n          name\n          images {\n            id\n            type\n          }\n        }\n        members {\n          player {\n            name\n            nationality\n          }\n          role {\n            id\n            name\n          }\n        }\n      }\n      links {\n        direction\n        result\n        linkedMatchSeries {\n          id\n        }\n      }\n      matches {\n        contestants {\n          points\n          rank\n          result\n          score\n          team {\n            id\n          }\n        }\n        sequence\n        status\n        gameMap {\n          id\n          name\n        }\n      }\n    }\n  }\n}"}, {"id": "5563921646b3c2db611321c37c004c20ebf73ca3944d733c48b670c2c9dcd417", "name": "Tournaments", "type": "query", "body": "query Tournaments($tournamentIds: [String!]!) {\n  tournaments(\n    filter: [{ids: $tournamentIds}]\n    limit: 100\n    sort: [{field: START_TIME, order: ORDER_ASC}]\n  ) {\n    result {\n      id\n      name\n      startTime\n      endTime\n      prizePool {\n        rank\n        amount\n        currency\n      }\n      streams {\n        url\n        language\n        primary\n      }\n      contestants {\n        rank\n        team {\n          id\n          name\n          club {\n            id\n            name\n          }\n          images {\n            id\n            type\n          }\n        }\n        members {\n          role {\n            name\n          }\n          player {\n            name\n            nationality\n          }\n        }\n      }\n    }\n  }\n}"}]}