import fs from 'fs';
import { parse, print } from 'graphql';

import manifest from './persisted-query-manifest.json' assert { type: 'json' };

function writeNormalizedQueriesFromManifest() {
  const writeStream = fs.createWriteStream('./src/ui/providers/ApolloClientProvider/persistedQueries/queries.txt', {
    flags: 'w',
  });

  for (const op of manifest.operations) {
    const normalized = print(parse(op.body));
    writeStream.write(`${op.id}\n`);
    writeStream.write(`${normalized}\n\n`);
  }

  writeStream.end();
}

writeNormalizedQueriesFromManifest();
