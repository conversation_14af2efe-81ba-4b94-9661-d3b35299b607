87be1e23826c7d88e02c16d15a33ed0b81448b84dda5d8d79a0678b85e1a4f52
query MatchSeries($tournamentIds: [String!]!) {
  matchSeries(
    filters: [{tournamentIds: $tournamentIds}]
    limit: 100
    sorts: [{field: START_TIME, order: ORDER_ASC}]
  ) {
    items {
      id
      status
      startTime
      position
      tournament {
        id
      }
      streams {
        url
        language
        primary
      }
      metadata {
        id
        type
        position
        name
      }
      contestants {
        score
        result
        rank
        points
        team {
          id
          name
          images {
            id
            type
          }
        }
      }
      links {
        direction
        result
        linkedMatchSeries {
          id
        }
      }
      matches {
        contestants {
          points
          rank
          result
          score
          team {
            id
          }
        }
        sequence
        status
        gameMap {
          id
          name
        }
      }
    }
  }
}

7ce72b9c7f1bada04984b92f9a17ad718d71edd6f47b744b8a3670b18d60c344
query Tournaments($tournamentIds: [String!]!) {
  tournaments(
    filter: [{ids: $tournamentIds}]
    limit: 100
    sort: [{field: START_TIME, order: ORDER_ASC}]
  ) {
    result {
      id
      name
      startTime
      endTime
      prizePool {
        rank
        amount
        currency
      }
      streams {
        url
        language
        primary
      }
      contestants {
        rank
        team {
          id
          name
          club {
            id
            name
          }
          images {
            id
            type
          }
        }
        members {
          role {
            name
          }
          player {
            name
            nationality
          }
        }
      }
    }
  }
} 