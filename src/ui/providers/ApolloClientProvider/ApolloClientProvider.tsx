'use client';

import { HttpLink } from '@apollo/client';
import { createPersistedQueryLink } from '@apollo/client/link/persisted-queries';
import { ApolloClient, ApolloNextAppProvider, InMemoryCache } from '@apollo/client-integration-nextjs';
import { generatePersistedQueryIdsFromManifest } from '@apollo/persisted-query-lists';

import { GRAPH_API_URL } from '@/config/env/client';

// const WS_URL = 'wss://your-api-endpoint/graphql';

//todo add WS link & persisted queries
// const splitLink = split(
//   ({ query }) => {
//     const definition = getMainDefinition(query);
//     return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
//   },
//   new GraphQLWsLink(createClient({ url: WS_URL })),
//   createPersistedQueryLink({ sha256 }).concat(new HttpLink({ uri: HTTP_URL })),
// );

const persistedQueryLink = createPersistedQueryLink(
  generatePersistedQueryIdsFromManifest({
    loadManifest: () => import('./persistedQueries/persisted-query-manifest.json'),
  }),
);
const httpLink = new HttpLink({
  uri: GRAPH_API_URL,
  useGETForQueries: true,
  headers: { 'Content-Type': 'text/plain' },
});

function makeClient() {
  return new ApolloClient({
    cache: new InMemoryCache(),
    link: persistedQueryLink.concat(httpLink),
    // link: splitLink,
  });
}

export function ApolloClientProvider({ children }: React.PropsWithChildren) {
  return <ApolloNextAppProvider makeClient={makeClient}>{children}</ApolloNextAppProvider>;
}
