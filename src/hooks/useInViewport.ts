import { useEffect, useState } from 'react';

export const useInViewport = (elementIds: string[]) => {
  const [activeId, setActiveId] = useState<string>('');

  useEffect(() => {
    const handleScroll = () => {
      let closestElement: HTMLElement | null = null;
      let closestDistance = Infinity;

      for (const id of elementIds) {
        const element = document.getElementById(id);
        if (element) {
          const rect = element.getBoundingClientRect();
          // Calculate distance from top of viewport, but only consider elements that are either
          // above the viewport center or currently visible
          if (rect.top < window.innerHeight / 2) {
            const distance = Math.abs(rect.top);
            if (distance < closestDistance) {
              closestDistance = distance;
              closestElement = element;
            }
          }
        }
      }

      if (closestElement) {
        const newActiveId = closestElement.id;
        if (newActiveId && newActiveId !== activeId) {
          setActiveId(newActiveId);
        }
      }
    };

    handleScroll();

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [elementIds, activeId]);

  return activeId;
};
