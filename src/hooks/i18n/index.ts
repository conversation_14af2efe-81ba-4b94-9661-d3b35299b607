'use client';

import { usePathname } from 'next/navigation';
import { useCurrentLocale as useI18nCurrentLocale } from 'next-i18n-router/client';
import { Config } from 'next-i18n-router/dist/types';

import i18nConfig from '../../../i18nConfig';
import { Locale } from './const';

export function useCurrentLocale() {
  const locale = useI18nCurrentLocale(i18nConfig as Config) as Locale;
  return locale;
}

export function useContentDirection() {
  const currentLocale = useCurrentLocale();

  const isRTL = currentLocale === 'ar';
  const isLTR = currentLocale !== 'ar';
  const contentDirection = isRTL ? 'rtl' : 'ltr';

  return { isRTL, isLTR, contentDirection };
}

export function useReplaceLocale() {
  const currentLocale = useCurrentLocale();
  const pathname = usePathname();

  function replaceLocale(newLocale: Locale) {
    const updatedPathname = pathname.replace(currentLocale, newLocale);
    // hard navigation ensures the server sets the locale cookie, which persists the locale for user
    return window.location.assign(updatedPathname);
  }

  return replaceLocale;
}
