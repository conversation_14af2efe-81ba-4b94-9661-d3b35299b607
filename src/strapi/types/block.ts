import { DynamicComponentBase } from './base';
import { ClubType } from './collection/club';
import { ContentNavigationItemType } from './collection/contentNavigationItems';
import { FaqType } from './collection/faq';
import { GameType } from './collection/game';
import { KeyStatisticType } from './collection/keyStatistic';
import { NewsArticleType } from './collection/news';
import { TeamGridItemType } from './collection/participatingTeams';
import { PartnerType } from './collection/partner';
import { QualifierType } from './collection/qualifier';
import { RibbonType } from './collection/ribbon';
import { BlockSectionWrapperType, CtaBannerType, JsonFieldType, RichTextColumn, RichTextContentType } from './helper';
import { StackableInfoItemType } from './helper/stackableInfo';
import { TicketCardType } from './helper/ticketCard';
import { TimelineItemType } from './helper/timelineItem';
import { MediaType } from './media';
import { ButtonType, ImageLinkType } from './shared';

export interface BlockBase extends DynamicComponentBase {
  section: BlockSectionWrapperType | null;
  // not in Strapi - provided from `BlockRenderer` for global translations
  translations: JsonFieldType | null | undefined;
}

export const SHOWCASE_GRID_BLOCK_KEY = 'block.showcase-grid';
export interface ShowcaseGridBlockType extends BlockBase {
  images: ImageLinkType[] | null;
}

export const NEWS_BLOCK_KEY = 'news.news-block';
export interface NewsBlockType extends BlockBase {
  featuredArticles: NewsArticleType[];
}

export const PULL_QUOTE_BLOCK_KEY = 'block.pull-qoute';
export interface PullQuoteBlockType extends BlockBase {
  text: string;
  authorName?: string | null;
  authorTitle?: string | null;
}

export const RICH_TEXT_BLOCK_KEY = 'block.rich-text';
export interface RichTextBlockType extends BlockBase {
  columns: RichTextColumn[];
}

export const MEDIA_BLOCK_KEY = 'block.media';
export interface MediaBlockType extends BlockBase {
  media: MediaType | null;
  autoplay: boolean;
  loop: boolean;
  muted: boolean;
  controls: boolean | null;
  mediaUrl: string | null;
}

export const RIBBON_BLOCK_KEY = 'block.ribbon';
export interface RibbonBlockType extends BlockBase {
  ribbon: RibbonType | null;
}

export const AUTOSCROLLING_SHOWCASE_KEY = 'block.auto-scrolling-showcase';
export interface AutoscrollingShowcaseType extends BlockBase {
  title: string;
  subtitle?: string | null;
  description?: string;
  button?: ButtonType | null;
  clubs?: Array<ClubType> | null;
}

export const PROMO_BLOCK_KEY = 'block.promo';
export interface PromoBlockType extends BlockBase {
  title: string;
  subtitle: string | null;
  repeatingText: string | null;
  backgroundImage: MediaType;
  media: MediaType;
}

export const FAQ_BLOCK_KEY = 'block.faq-block';
export interface FAQBlockType extends BlockBase {
  faq: FaqType | null;
}

export const CTA_BLOCK_KEY = 'block.cta';
export interface CtaBlockType extends BlockBase {
  ctas: CtaBannerType[];
}

export const ANNOUNCED_GAMES_GRID_BLOCK_KEY = 'block.announced-games-grid';
export interface AnnouncedGamesGridBlockType extends BlockBase {
  games: GameType[];
}

export const VISUAL_INFO_BLOCK_KEY = 'block.visual-info';
export interface VisualInfoBlockType extends BlockBase {
  title: string | null;
  subtitle: string | null;
  descriptionRichText: RichTextContentType | null;
  button: ButtonType | null;
  isMediaOnRight: boolean;
  media: MediaType;
}

export const STACKABLE_INFO_BLOCK_KEY = 'block.stackable-info';
export interface StackableInfoBlockType extends BlockBase {
  isNumbered: boolean;
  items: StackableInfoItemType[] | null;
}

export const TICKET_SALES_BLOCK_KEY = 'block.ticket-block';

export interface TicketSalesBlockType extends BlockBase {
  tickets: TicketCardType[];
}

export const PARTNERS_BLOCK_KEY = 'block.partners-block';
export interface PartnersBlockType extends BlockBase {
  partners?: PartnerType[];
}

export const KEY_STATISTICS_BLOCK_KEY = 'block.key-statistics';
export interface KeyStatisticsBlockType extends BlockBase {
  keyStatistics: KeyStatisticType[];
  hasBackground: boolean;
}

export const TIMELINE_BLOCK_KEY = 'block.timeline';
export interface TimelineBlockType extends BlockBase {
  items: TimelineItemType[];
}

export const QUALIFIERS_GRID_BLOCK_KEY = 'block.qualifiers-grid';
export interface QualifiersGridBlockType extends BlockBase {
  qualifiers: QualifierType[];
}

export const QUALIFIERS_LIST_BLOCK_KEY = 'block.qualifiers-list';
export interface QualifiersListBlockType extends BlockBase {
  qualifiers: QualifierType[];
}

export const CONTENT_NAVIGATION_GRID_BLOCK_KEY = 'block.content-navigation-grid';
export interface ContentNavigationGridBlockType extends BlockBase {
  contentNavigationItems: ContentNavigationItemType[];
}

export const PARTICIPATING_TEAMS_BLOCK_KEY = 'block.participating-teams-block';
export interface ParticipatingTeamsBlockType extends BlockBase {
  gridItems: TeamGridItemType[];
}

export const PARTNER_RIBBON_BLOCK_KEY = 'block.partner-ribbon';
export interface PartnerRibbonBlockType extends BlockBase {
  title: string | null;
  pauseOnHover: boolean;
  duration: number | null;
  partnerRibbon: { name: string | null; partners: PartnerType[] | null } | null;
}

export type DynamicZoneBlock =
  | AutoscrollingShowcaseType
  | ShowcaseGridBlockType
  | NewsBlockType
  | PullQuoteBlockType
  | RichTextBlockType
  | MediaBlockType
  | FAQBlockType
  | PromoBlockType
  | CtaBlockType
  | AnnouncedGamesGridBlockType
  | VisualInfoBlockType
  | StackableInfoBlockType
  | RibbonBlockType
  | TicketSalesBlockType
  | PartnersBlockType
  | KeyStatisticsBlockType
  | TimelineBlockType
  | QualifiersGridBlockType
  | QualifiersListBlockType
  | PartnerRibbonBlockType;
