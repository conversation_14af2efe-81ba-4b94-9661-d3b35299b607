import { StrapiBase } from '../base';
import { DynamicZoneBlock } from '../block';
import { RichTextContentType } from '../helper';
import { MediaType } from '../media';
import { SeoType } from '../shared/seo';
import { PartnerLabelType } from './partnerLabel';

export interface PartnerType extends StrapiBase {
  name: string | null;
  logo: MediaType | null;
  link: string | null;
  partnerLabel: PartnerLabelType | null;
  slug: string | null;
  summary: RichTextContentType | null;
  coverImage: MediaType | null;
  blocks: DynamicZoneBlock[];
  seo: SeoType | null;
}
