import { StrapiBase } from '../base';
import { RichTextContentType } from '../helper';
import { MediaType } from '../media';

export interface FestivalType extends StrapiBase {
  title: string | null;
  description: RichTextContentType | null;
  startDate: string | null;
  endDate: string | null;
  startDateTime: string | null;
  endDateTime: string | null;
  type: string; // or enum
  slug: string | null;
  shortSummary: string | null;
  posterImage: MediaType | null;
  location: string | null;
  isHighlight: boolean;
  isShownOnSchedule: boolean;
  icon: MediaType | null;
  ticketsUrl: string | null;
  venue: string | null;
}
