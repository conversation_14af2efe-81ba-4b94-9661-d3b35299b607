import { StrapiBase } from '../base';
import { DynamicZoneBlock } from '../block';
import { RichTextContentType } from '../helper';
import { MediaType } from '../media';
import { SeoType } from '../shared/seo';
import { TagType } from './tag';

export const NEWS_ARTICLES_COLLECTION_KEY = 'news-articles';
export interface NewsArticleType extends StrapiBase {
  title: string | null;
  slug: string | null;
  date: string | null;
  summary: RichTextContentType | null;
  cover: MediaType | null;
  tags: TagType[];
  author: string | null;
  isArchived: boolean | null;
  blocks: DynamicZoneBlock[];
  seo: SeoType | null;
}
