import { StrapiBase } from '../base';
import { MediaType } from '../media';

export interface TeamGridItemType extends StrapiBase {
  teamLogo: MediaType | null;
  teamName: string | null;
  subtitleText: string | null;
  qualifierLogo: MediaType | null;
  qualifierName: string | null;
  isCurrentChampion: boolean | null;
  playerName: string | null;
  playerPhoto: MediaType | null;
  isPlayer: boolean | null;
  isTBD: boolean | null;
}
