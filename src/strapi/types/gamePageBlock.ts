import { BlockBase } from './block';

export const STANDINGS_TABLE_BLOCK_KEY = 'game-page-blocks.standings-table';
export type StandingsTableBlockType = BlockBase;

export const GAME_SCHEDULE_BLOCK_KEY = 'game-page-blocks.game-schedule-block';
export interface GameScheduleBlockType extends BlockBase {
  competitions:
    | {
        competitionName: string;
        competitionId: string;
      }[]
    | null;
}

export type GamePageBlock = StandingsTableBlockType | GameScheduleBlockType;
