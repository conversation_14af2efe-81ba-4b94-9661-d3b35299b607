import { StrapiBase } from './base';

export type MediaType = StrapiBase &
  MediaFormat & {
    alternativeText: string | null;
    caption: string | null;
    formats: { thumbnail: MediaFormat; small: MediaFormat; medium: MediaFormat; large: MediaFormat } | null;
    previewUrl: string | null;
    provider: string;
    // provider_metadata: null;
  };

interface MediaFormat {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  path: string | null;
  width: number;
  height: number;
  size: number;
  sizeInBytes: number;
  url: string;
}
