import { DynamicComponentBase } from './base';
import { MediaType } from './media';

export interface HeroTournamentUpcomingBlockType extends DynamicComponentBase {
  dateHeading: string;
  subtitle: string;
  countdownTimerDate: string | null;
  scrollForMoreText: string | null;
  logo: MediaType;
  backgroundMedia: MediaType;
}

export interface HeroLiveStateType extends DynamicComponentBase {
  noStreamLabel: string | null;
}
