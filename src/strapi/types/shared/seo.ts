import { MediaType } from '../media';

export interface SeoType {
  id: number;
  metaTitle: string;
  metaDescription: string;
  keywords: string | null;
  metaRobots: string | null;
  metaViewport: string | null;
  canonicalURL: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  structuredData: Record<string, any>;
  openGraph: OpenGraph | null;
  metaImage: MediaType;
}

interface OpenGraph {
  id: number;
  ogTitle: string;
  ogDescription: string;
  ogUrl: string | null;
  ogType: string | null;
}
