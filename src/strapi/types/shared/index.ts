import { ButtonSize, ButtonVariant } from '@/ui/components/Button/variants';

import { MediaType } from '../media';

export interface ImageLinkType {
  id: number;
  image: MediaType;
  title: string | null;
  link: string | null;
}

export interface PostPageHeaderType {
  id: number;
  title: string | null;
  intro: string | null;
  date: string | null;
  dateTo: string | null;
  cover: MediaType | null;
  logo: MediaType | null;
  buttons: ButtonType[] | null;
}

export interface ButtonType {
  id: number;
  text: string | null;
  link: string | null;
  variant: ButtonVariant | null;
  size: ButtonSize | null;
  openInNewTab: boolean | null;
  isDisabled: boolean | null;
}
