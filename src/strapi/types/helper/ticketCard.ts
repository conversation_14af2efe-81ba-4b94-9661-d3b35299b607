import { GameType } from '../collection/game';
import { MediaType } from '../media';
import { ButtonType } from '../shared';

export type TicketType = 'weekly' | 'daily' | 'festival' | 'game-specific';
export interface TicketCardType {
  id: number;
  title: string | null;
  description: string | null;
  image: MediaType | null;
  dateRange: string | null;
  games: GameType[];
  button: ButtonType | null;
  type: TicketType;
}
