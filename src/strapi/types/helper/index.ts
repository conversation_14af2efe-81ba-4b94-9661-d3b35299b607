import { BlocksContent } from '@strapi/blocks-react-renderer';

import { Locale } from '@/hooks/i18n/const';

import { MediaType } from '../media';
import { ButtonType } from '../shared';

export interface BlockSectionWrapperType {
  id: number;
  title: string | null;
  subtitle: string | null;
  isSectionDividerVisible: boolean | null;
  styleConfig: React.CSSProperties | null;
  link: LinkType | null;
  headerImage: MediaType | null;
  isBlockVisible: boolean | null;
  titleAlignment?: 'left' | 'center' | 'right' | null;
  navigation?: TopNavigationType | null;
}

export interface TopNavigationType {
  navigationId: string;
  navigationLabel: string;
}

export interface LinkType {
  id: number;
  text: string;
  url: string;
  image: MediaType | null;
}

export type RichTextContentType = BlocksContent;

export interface RichTextColumn {
  id: number;
  content: RichTextContentType;
}

export interface CtaBannerType {
  id: number;
  title: string;
  description: string | null;
  button: ButtonType;
}

export type JsonFieldType = Record<string, string>;

export interface FeatureFlagsType {
  disabledLocales?: Locale[];
}
