import { MediaType } from '../media';
import { RichTextContentType } from '.';

export interface StackableInfoCardType {
  id: number;
  title: string | null;
  icon: string | null;
  description: string | null;
}

export interface StackableInfoItemType {
  id: number;
  upperLabel: string | null;
  title: string | null;
  subtitle: string | null;
  descriptionRichText: RichTextContentType | null;
  media: MediaType | null;
  cards: StackableInfoCardType[] | null;
  isLeftToRight: boolean;
}
