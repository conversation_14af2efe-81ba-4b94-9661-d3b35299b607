import {
  ANNOUNCED_GAMES_GRID_BLOCK_KEY,
  AUTOSCROLLING_SHOWCASE_KEY,
  CONTENT_NAVIGATION_GRID_BLOCK_KEY,
  CTA_BLOCK_KEY,
  FAQ_BLOCK_KEY,
  KEY_STATISTICS_BLOCK_KEY,
  MEDIA_BLOCK_KEY,
  NEWS_BLOCK_KEY,
  PARTICIPATING_TEAMS_BLOCK_KEY,
  PARTNER_RIBBON_BLOCK_KEY,
  PARTNERS_BLOCK_KEY,
  PROMO_BLOCK_KEY,
  PULL_QUOTE_BLOCK_KEY,
  QUALIFIERS_GRID_BLOCK_KEY,
  QUALIFIERS_LIST_BLOCK_KEY,
  RIBBON_BLOCK_KEY,
  RICH_TEXT_BLOCK_KEY,
  SHOWCASE_GRID_BLOCK_KEY,
  STACKABLE_INFO_BLOCK_KEY,
  TICKET_SALES_BLOCK_KEY,
  TIMELINE_BLOCK_KEY,
  VISUAL_INFO_BLOCK_KEY,
} from '../types/block';
import { GAME_SCHEDULE_BLOCK_KEY, STANDINGS_TABLE_BLOCK_KEY } from '../types/gamePageBlock';

const section = { populate: '*' };

interface Params {
  includeGamePageBlocks: boolean;
}

export function getDynamicZoneBlocks(params?: Params) {
  return { on: { ...blocksDynamicZone, ...(params?.includeGamePageBlocks ? gamePageBlocks : {}) } };
}

const blocksDynamicZone = {
  [SHOWCASE_GRID_BLOCK_KEY]: { populate: { section, images: { populate: '*' } } },
  [NEWS_BLOCK_KEY]: { populate: { section, featuredArticles: { populate: ['tags', 'cover'] } } },
  [MEDIA_BLOCK_KEY]: { populate: { section, media: { populate: true } } },
  [RIBBON_BLOCK_KEY]: { populate: { section, ribbon: { populate: '*' } } },
  [RICH_TEXT_BLOCK_KEY]: { populate: { section, columns: { populate: '*' } } },
  [FAQ_BLOCK_KEY]: {
    populate: { section, faq: { populate: { faqSections: { populate: '*' } } } },
  },
  [PULL_QUOTE_BLOCK_KEY]: { populate: { section } },
  [PROMO_BLOCK_KEY]: { populate: { section, backgroundImage: { populate: '*' }, media: { populate: '*' } } },
  [AUTOSCROLLING_SHOWCASE_KEY]: { populate: { section, button: { populate: '*' }, clubs: { populate: '*' } } },
  [CTA_BLOCK_KEY]: { populate: { section, ctas: { populate: '*' } } },
  [ANNOUNCED_GAMES_GRID_BLOCK_KEY]: { populate: { section, games: { populate: '*' } } },
  [VISUAL_INFO_BLOCK_KEY]: { populate: { section, button: true, media: true } },
  [STACKABLE_INFO_BLOCK_KEY]: {
    populate: { section, items: { populate: { media: true, cards: true } } },
  },
  [TICKET_SALES_BLOCK_KEY]: {
    populate: {
      section,
      tickets: {
        populate: {
          image: { fields: ['url', 'alternativeText', 'width', 'height'] },
          games: { populate: { game: { populate: '*' } } },
          button: { populate: '*' },
        },
      },
    },
  },
  [PARTNERS_BLOCK_KEY]: { populate: { section, partners: { populate: '*' } } },
  [KEY_STATISTICS_BLOCK_KEY]: { populate: { section, keyStatistics: { populate: '*' } } },
  [CONTENT_NAVIGATION_GRID_BLOCK_KEY]: { populate: { section, contentNavigationItems: { populate: '*' } } },
  [TIMELINE_BLOCK_KEY]: { populate: { section, items: { populate: '*' } } },
  [QUALIFIERS_GRID_BLOCK_KEY]: {
    populate: {
      section,
      qualifiers: {
        fields: ['title', 'startDate', 'endDate', 'region', 'teamsProgress'],
        populate: {
          logo: { fields: ['url', 'alternativeText', 'width', 'height'] },
          buttons: { populate: '*' },
          game: { fields: ['gradientHex'] },
        },
      },
    },
  },
  [QUALIFIERS_LIST_BLOCK_KEY]: {
    populate: {
      section,
      qualifiers: {
        fields: ['title', 'startDate', 'endDate', 'region', 'teamsProgress'],
        populate: { logo: { fields: ['url', 'alternativeText', 'width', 'height'] }, buttons: { populate: '*' } },
      },
    },
  },
  [PARTICIPATING_TEAMS_BLOCK_KEY]: {
    populate: {
      section,
      gridItems: {
        fields: ['teamName', 'subtitleText', 'qualifierName', 'playerName', 'isCurrentChampion', 'isPlayer', 'isTBD'],
        populate: {
          teamLogo: { fields: ['url', 'alternativeText', 'width', 'height'] },
          qualifierLogo: { fields: ['url', 'alternativeText', 'width', 'height'] },
          playerPhoto: { fields: ['url', 'alternativeText', 'width', 'height'] },
        },
      },
    },
  },
  [PARTNER_RIBBON_BLOCK_KEY]: { populate: { section, partnerRibbon: { populate: { partners: { populate: '*' } } } } },
  [GAME_SCHEDULE_BLOCK_KEY]: { populate: { section } },
};

const gamePageBlocks = {
  [STANDINGS_TABLE_BLOCK_KEY]: { populate: { section } },
};
