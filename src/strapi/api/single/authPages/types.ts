import { RichTextContentType } from '@/strapi/types/helper';
import { SeoType } from '@/strapi/types/shared/seo';

interface AuthPageBaseData {
  id: number;
  title: string | null;
  subtitle: string | null;
  footerText: RichTextContentType | null;
  seo: SeoType | null;
}

export interface SignupPageData extends AuthPageBaseData {
  separatorText: string | null;
  emailFieldPlaceholder: string | null;
  nameFieldPlaceholder: string | null;
  surnameFieldPlaceholder: string | null;
  usernameFieldPlaceholder: string | null;
  passwordFieldPlaceholder: string | null;
  passwordConfirmFieldPlaceholder: string | null;
  countryFieldPlaceholder: string | null;
  termsOfUseCheckboxText: RichTextContentType | null;
  newsCheckboxText: string | null;
  marketingCheckboxText: string | null;
  signupButtonText: string | null;
  loginText: string | null;
  termsOfUseAlertTitle: string | null;
  termsOfUseAlertBody: string | null;
  termsOfUseAlertButtonText: string | null;
}

export interface LoginPageData extends AuthPageBaseData {
  separatorText: string | null;
  emailFieldPlaceholder: string | null;
  passwordFieldPlaceholder: string | null;
  loginButtonText: string | null;
  signupButtonText: string | null;
  externalLoginMissingFieldsText: string | null;
}

export interface ForgotPasswordPageData extends AuthPageBaseData {
  emailFieldPlaceholder: string | null;
  sendEmailButtonText: string | null;
  confirmationCodeSentText: RichTextContentType | null;
}

export interface ResetPasswordPageData extends AuthPageBaseData {
  emailFieldPlaceholder: string | null;
  confirmationCodeFieldPlaceholder: string | null;
  passwordFieldPlaceholder: string | null;
  passwordConfirmFieldPlaceholder: string | null;
  resetButtonText: string | null;
}

export interface ChangePasswordPageData extends AuthPageBaseData {
  currentPasswordFieldPlaceholder: string | null;
  newPasswordFieldPlaceholder: string | null;
  passwordConfirmFieldPlaceholder: string | null;
  updatePasswordButtonText: string | null;
}

export interface ConfirmEmailPageData extends AuthPageBaseData {
  codeFieldPlaceholder: string | null;
  verifyButtonText: string | null;
  separatorText: string | null;
  separatorRequestAgainText: string | null;
}
