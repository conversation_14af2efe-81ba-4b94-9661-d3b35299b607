import { Locale } from '@/hooks/i18n/const';
import { StrapiBase } from '@/strapi/types/base';

import { getStrapiDefaultParams, strapiClient } from '../../client';
import {
  ChangePasswordPageData,
  ConfirmEmailPageData,
  ForgotPasswordPageData,
  LoginPageData,
  ResetPasswordPageData,
  SignupPageData,
} from './types';

interface AuthPageData extends StrapiBase {
  signup: SignupPageData | null;
  login: LoginPageData | null;
  forgotPassword: ForgotPasswordPageData | null;
  resetPassword: ResetPasswordPageData | null;
  changePassword: ChangePasswordPageData | null;
  confirmEmail: ConfirmEmailPageData | null;
}

export async function getSignupPageData(locale: Locale) {
  try {
    const authPagesData = await strapiClient.single('auth-page').find({
      populate: { signup: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = (authPagesData.data as unknown as AuthPageData).signup;
    return data;
  } catch (error) {
    console.error('Signup Page data not fetched!', error);
    throw new Error('Signup Page data not fetched!');
  }
}

export async function getLoginPageData(locale: Locale) {
  try {
    const authPagesData = await strapiClient.single('auth-page').find({
      populate: { login: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = (authPagesData.data as unknown as AuthPageData).login;
    return data;
  } catch (error) {
    console.error('Login Page data not fetched!', error);
    throw new Error('Login Page data not fetched!');
  }
}

export async function getForgotPasswordPageData(locale: Locale) {
  try {
    const authPagesData = await strapiClient.single('auth-page').find({
      populate: { forgotPassword: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = (authPagesData.data as unknown as AuthPageData).forgotPassword;
    return data;
  } catch (error) {
    console.error('Forgot Password Page data not fetched!', error);
    throw new Error('Forgot Password Page data not fetched!');
  }
}

export async function getResetPasswordPageData(locale: Locale) {
  try {
    const authPagesData = await strapiClient.single('auth-page').find({
      populate: { resetPassword: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = (authPagesData.data as unknown as AuthPageData).resetPassword;
    return data;
  } catch (error) {
    console.error('Reset Password Page data not fetched!', error);
    throw new Error('Reset Password Page data not fetched!');
  }
}

export async function getChangePasswordPageData(locale: Locale) {
  try {
    const authPagesData = await strapiClient.single('auth-page').find({
      populate: { changePassword: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = (authPagesData.data as unknown as AuthPageData).changePassword;
    return data;
  } catch (error) {
    console.error('Change Password Page data not fetched!', error);
    throw new Error('Change Password Page data not fetched!');
  }
}

export async function getConfirmEmailPageData(locale: Locale) {
  try {
    const authPagesData = await strapiClient.single('auth-page').find({
      populate: { confirmEmail: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    const data = (authPagesData.data as unknown as AuthPageData).confirmEmail;
    return data;
  } catch (error) {
    console.error('Confirm Email Page data not fetched!', error);
    throw new Error('Confirm Email Page data not fetched!');
  }
}
