import { Locale } from '@/hooks/i18n/const';
import { SeoType } from '@/strapi/types/shared/seo';

import { NewsFilter } from '../../../app/[locale]/news/_components/NewsAggregator';
import { StrapiBase } from '../../types/base';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { fetchNewsArticles } from '../collection/news';

interface NewsPageData extends StrapiBase {
  title: string | null;
  subtitle: string | null;
}

export async function getNewsPageData(locale: Locale, page: number = 1, search?: string, filter?: NewsFilter) {
  try {
    const newsPage = await strapiClient.single('news-page').find(getStrapiDefaultParams(locale));
    const articlesData = await fetchNewsArticles(locale, { pagination: { pageSize: 16, page }, search, filter });

    const pageData = newsPage.data as NewsPageData;
    return { pageData, articlesData };
  } catch (error) {
    console.error('News Page data not fetched!', error);
    throw new Error('News Page data not fetched!');
  }
}

export async function getNewsPageSeo(locale: Locale) {
  try {
    const newsPage = await strapiClient
      .single('news-page')
      .find({ populate: 'seo', ...getStrapiDefaultParams(locale) });
    return newsPage.data.seo as SeoType | null;
  } catch (error) {
    console.error('News Page SEO not fetched!', error);
    return null;
  }
}
