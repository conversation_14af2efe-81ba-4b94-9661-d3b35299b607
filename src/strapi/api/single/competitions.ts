import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock } from '@/strapi/types/block';
import { SeoType } from '@/strapi/types/shared/seo';

import { StrapiBase } from '../../types/base';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { fetchGames } from '../collection/game';
import { assembleBlocksWithAdditionalData } from '../utils';

interface CompetitionsPageData extends StrapiBase {
  title: string | null;
  subtitle: string | null;
  blocks: DynamicZoneBlock[];
}

export async function getCompetitionsPageData(locale: Locale) {
  try {
    const competitionsPage = await strapiClient.single('competitions-page').find({
      populate: { blocks: getDynamicZoneBlocks() },
      ...getStrapiDefaultParams(locale),
    });

    const gamesData = await fetchGames(locale);

    const blocks = await assembleBlocksWithAdditionalData(competitionsPage.data.blocks, locale);
    const pageData = { ...competitionsPage.data, blocks } as CompetitionsPageData;

    return { pageData, gamesData };
  } catch (error) {
    console.error('Competitions Page data not fetched!', error);
    throw new Error('Competitions Page data not fetched!');
  }
}

export async function getCompetitionsPageSeo(locale: Locale) {
  try {
    const competitionsPage = await strapiClient
      .single('competitions-page')
      .find({ populate: 'seo', ...getStrapiDefaultParams(locale) });
    return competitionsPage.data.seo as SeoType | null;
  } catch (error) {
    console.error('Competitions Page SEO not fetched!', error);
    return null;
  }
}
