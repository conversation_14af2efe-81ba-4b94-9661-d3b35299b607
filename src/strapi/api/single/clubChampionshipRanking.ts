import { Locale } from '../../../hooks/i18n/const';
import { StrapiBase } from '../../types/base';
import { DynamicZoneBlock } from '../../types/block';
import { RichTextContentType } from '../../types/helper';
import { MediaType } from '../../types/media';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils';

interface ClubChampionshipRankingPageData extends StrapiBase {
  title: string | null;
  subtitle: RichTextContentType | null;
  bgMedia: MediaType | null;
  topExplainerText: RichTextContentType | null;
  middleExplainerText: RichTextContentType | null;
  bottomExplainerText: RichTextContentType | null;
  leaderboardTitle: string | null;
  leaderboardSubtitle: string | null;
  blocks: DynamicZoneBlock[];
  tournamentStart: string | null;
  tournamentEnd: string | null;
}

export async function getClubChampionshipRankingPageData(locale: Locale) {
  const pageData = await strapiClient.single('club-championship-ranking-page').find({
    populate: { bgMedia: { populate: '*' }, blocks: getDynamicZoneBlocks() },
    ...getStrapiDefaultParams(locale),
  });

  const blocks = await assembleBlocksWithAdditionalData(pageData.data.blocks, locale);
  const data = { ...pageData.data, blocks } as ClubChampionshipRankingPageData;

  return data;
}
