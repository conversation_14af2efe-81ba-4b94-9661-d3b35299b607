import { Locale } from '@/hooks/i18n/const';
import { GameEventType } from '@/strapi/types/collection/gameEvent';
import { WeekTicketsLinkType } from '@/strapi/types/helper/weekTickets';

import { StrapiBase } from '../../types/base';
import { FestivalType } from '../../types/collection/festival';
import { SeoType } from '../../types/shared/seo';
import { getStrapiDefaultParams, strapiClient } from '../client';

interface SchedulePageData extends StrapiBase {
  title: string | null;
  subtitle: string | null;
  timezoneInfo: string | null;
  tournamentStartDate: string;
  tournamentEndDate: string;
  gameEvents: GameEventType[];
  festivals: FestivalType[];
  weekTickets: WeekTicketsLinkType[] | null;
  gridColumnWidth: number;
}

export async function getSchedulePageData(locale: Locale) {
  try {
    const scheduleData = await strapiClient.single('schedule-page').find({
      populate: {
        weekTickets: true,
        gameEvents: {
          populate: {
            game: { populate: { schedulePopupLogo: true, logoDark: true, keyArt: true, icon: true, iconFinals: true } },
          },
        },
        festivals: { populate: '*' },
      },
      ...getStrapiDefaultParams(locale),
    });

    return scheduleData.data as SchedulePageData;
  } catch (error) {
    console.error('Schedule Page data not fetched!', error);
    throw new Error('Schedule Page data not fetched!');
  }
}

export async function getSchedulePageSeo(locale: Locale) {
  try {
    const scheduleData = await strapiClient.single('schedule-page').find({
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return scheduleData.data.seo as SeoType;
  } catch (error) {
    console.error('Schedule Page SEO not fetched!', error);
    return null;
  }
}
