import { Locale } from '@/hooks/i18n/const';

import { StrapiBase } from '../../types/base';
import { DynamicZoneBlock } from '../../types/block';
import { HeroTournamentUpcomingBlockType } from '../../types/hero';
import { SeoType } from '../../types/shared/seo';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils';

interface HomepageData extends StrapiBase {
  hero: HeroTournamentUpcomingBlockType | null;
  blocks: DynamicZoneBlock[];
}

export async function getHomepageData(locale: Locale) {
  try {
    const homepageData = await strapiClient.single('homepage').find({
      populate: { hero: { populate: '*' }, blocks: getDynamicZoneBlocks() },
      ...getStrapiDefaultParams(locale),
    });

    const blocks = await assembleBlocksWithAdditionalData(homepageData.data.blocks, locale);
    const data = { ...homepageData.data, blocks, hero: homepageData.data.hero[0] } as HomepageData;

    return data;
  } catch (error) {
    console.error('Homepage data not fetched!', error);
    throw new Error('Homepage data not fetched!');
  }
}

export async function getHomepageSeo(locale: Locale) {
  try {
    const homepageData = await strapiClient.single('homepage').find({
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return homepageData.data.seo as SeoType;
  } catch (error) {
    console.error('Homepage SEO not fetched!', error);
    return null;
  }
}
