import { Locale } from '@/hooks/i18n/const';
import { RichTextContentType } from '@/strapi/types/helper';

import { getStrapiDefaultParams, strapiClient } from '../client';

export interface CookieConsentModalType {
  title?: string | null;
  description?: RichTextContentType | null;
}

export async function getCookieConsentModalData(locale: Locale) {
  try {
    const cookieConsentModal = await strapiClient.single('cookie-consent-modal').find({
      fields: ['title', 'description'],
      ...getStrapiDefaultParams(locale),
    });

    const data = cookieConsentModal.data as CookieConsentModalType;
    return data;
  } catch (error) {
    console.error('Cookie Consent Modal data not fetched!', error);
    return null;
  }
}
