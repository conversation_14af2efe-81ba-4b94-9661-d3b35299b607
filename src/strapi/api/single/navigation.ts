import { Locale } from '@/hooks/i18n/const';

import { StrapiBase } from '../../types/base';
import { NavigationItem } from '../../types/helper/navigationItem';
import { ButtonType, ImageLinkType } from '../../types/shared';
import { getStrapiDefaultParams, strapiClient } from '../client';

export interface NavigationData extends StrapiBase {
  logo?: ImageLinkType;
  items?: Array<NavigationItem>;
  buttons?: Array<ButtonType>;
}

export async function getNavigationData(locale: Locale): Promise<NavigationData | null> {
  try {
    const navigationData = await strapiClient.single('navigation').find({
      populate: {
        logo: { populate: '*' },
        items: { populate: '*' },
        buttons: { populate: '*' },
        populate: '*',
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = navigationData.data as NavigationData;

    return data;
  } catch (error) {
    console.error('Navigation data not fetched!', error);
    return null;
  }
}
