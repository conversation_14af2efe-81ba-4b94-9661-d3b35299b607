import { Locale } from '@/hooks/i18n/const';
import { LinkType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';

import { StrapiBase } from '../../types/base';
import { getStrapiDefaultParams, strapiClient } from '../client';

export interface FooterSection {
  label: string | null;
  links: LinkType[];
}

export interface FooterData extends StrapiBase {
  callCenterInfo: { label: string | null; telephone: string | null; workingHours: string | null };
  legalLinks: LinkType[];
  apps: FooterSection | null;
  socials: FooterSection | null;
  copyright: string | null;
  chooseLanguageLabel: string | null;
  logo: MediaType | null;
}

export async function getFooterData(locale: Locale) {
  try {
    const footer = await strapiClient.single('footer').find({
      populate: {
        callCenterInfo: { populate: '*' },
        legalLinks: { populate: '*' },
        apps: { populate: { links: { populate: { image: true } } } },
        socials: { populate: { links: { populate: { image: true } } } },
        logo: { populate: '*' },
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = footer.data as FooterData;
    return data;
  } catch (error) {
    console.error('Footer data not fetched!', error);
    return null;
  }
}
