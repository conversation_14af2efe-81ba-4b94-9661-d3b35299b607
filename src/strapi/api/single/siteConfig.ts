import { Locale } from '@/hooks/i18n/const';
import { FeatureFlagsType, JsonFieldType } from '@/strapi/types/helper';

import { StrapiBase } from '../../types/base';
import { getStrapiDefaultParams, strapiClient } from '../client';

export interface SiteConfigData extends StrapiBase {
  translations: JsonFieldType | null;
  featureFlags: FeatureFlagsType | null;
}

export async function getSiteConfig(locale: Locale) {
  try {
    const siteConfig = await strapiClient.single('site-config').find({
      fields: ['translations', 'featureFlags'],
      ...getStrapiDefaultParams(locale),
    });

    const data = siteConfig.data as SiteConfigData;
    return data;
  } catch (error) {
    console.error('Site Config data not fetched!', error);
    return null;
  }
}
