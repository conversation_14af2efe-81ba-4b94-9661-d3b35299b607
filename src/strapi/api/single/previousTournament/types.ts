import { LinkType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';

export interface PreviousTournamentHero {
  heading: string | null;
  description: string | null;
  pointsLabel: string | null;
  prizeLabel: string | null;
}

export interface PreviousTournamentWinners {
  title: string | null;
  subtitle: string | null;
  cards: WinnerCard[] | null;
}

export interface WinnerCard {
  gameId: string | null;
  thumbnailImage: MediaType | null;
}

export interface PreviousTournamentSection {
  title: string | null;
  subtitle: string | null;
  link: LinkType | null;
}
