import { Locale } from '@/hooks/i18n/const';
import { PartnerType } from '@/strapi/types/collection/partner';
import { SeoType } from '@/strapi/types/shared/seo';

import { StrapiBase } from '../../types/base';
import { RichTextContentType } from '../../types/helper';
import { getStrapiDefaultParams, strapiClient } from '../client';

export interface PartnerTier {
  id: number;
  title: string | null;
  subtitle: string | null;
  color: 'gold' | 'standard' | 'gray';
  partners: Pick<PartnerType, 'id' | 'logo' | 'slug' | 'link'>[] | null;
}

export interface PartnersPageData extends StrapiBase {
  title: string | null;
  upperLabel: string | null;
  subtitle: RichTextContentType | null;
  tiers: PartnerTier[];
}

export async function getPartnersPageData(locale: Locale) {
  try {
    const partnersData = await strapiClient.collection('partners-page').find({
      populate: {
        tiers: {
          populate: {
            partners: {
              fields: ['slug', 'link'],
              populate: { logo: { populate: '*' } },
            },
          },
        },
      },
      ...getStrapiDefaultParams(locale),
    });

    return partnersData.data as unknown as PartnersPageData;
  } catch (error) {
    console.error('Partners page data not fetched', error);
    throw new Error('Partners page data not fetched');
  }
}

export async function getPartnersPageSeo(locale: Locale) {
  try {
    const partnersData = await strapiClient.collection('partners-page').find({
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return partnersData.data[0]?.seo as SeoType | null;
  } catch (error) {
    console.error('Partners page SEO not fetched!', error);
    return null;
  }
}
