import { Locale } from '@/hooks/i18n/const';
import { RedirectType } from '@/strapi/types/collection/redirect';

import { getStrapiDefaultParams, strapiClient } from '../client';

export async function getRedirects(locale: Locale) {
  try {
    const redirectData = await strapiClient.collection('redirects').find({
      fields: ['fromPath', 'toPath', 'enabled'],
      ...getStrapiDefaultParams(locale),
    });

    const data = redirectData.data as RedirectType[];
    if (!data) {
      return [];
    }

    const enabledRedirects = data.filter((d) => d.enabled);
    return enabledRedirects;
  } catch (error) {
    console.error('Redirects not fetched!', error);
    throw new Error('Redirects not fetched!');
  }
}
