import { Locale } from '@/hooks/i18n/const';
import { ErrorPageType, ErrorType } from '@/strapi/types/collection/error-page';

import { getStrapiDefaultParams, strapiClient } from '../client';

export async function getErrorPageData(locale: Locale, type: ErrorType) {
  try {
    const errorPageData = await strapiClient.collection('error-pages').find({
      filters: { type: { $eq: type } },
      populate: { button: { populate: '*' } },
      ...getStrapiDefaultParams(locale),
    });

    if (!errorPageData) {
      return null;
    }

    return errorPageData.data[0] as ErrorPageType;
  } catch (error) {
    console.error('Error page data not fetched', error);
    throw new Error('Error page data not fetched');
  }
}
