import { Locale } from '@/hooks/i18n/const';
import { GAMES_COLLECTION_KEY, GameType } from '@/strapi/types/collection/game';
import { SeoType } from '@/strapi/types/shared/seo';

import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils';

export async function getGamePageSlugs(locale: Locale) {
  try {
    const gamePageData = await strapiClient.collection('games').find({
      fields: ['slug'],
      ...getStrapiDefaultParams(locale),
    });

    const data = gamePageData.data as GameType[];
    if (!data) {
      return [];
    }

    const slugs = data.filter((d) => d.slug).map((d) => d.slug);
    return slugs;
  } catch (error) {
    console.error('Game page slugs not fetched!', error);
    throw new Error('Game page slugs not fetched!');
  }
}

export async function getGameTitles(locale: Locale) {
  try {
    const gamePageData = await strapiClient.collection('games').find({
      fields: ['title'],
      ...getStrapiDefaultParams(locale),
    });

    const data = gamePageData.data as GameType[];
    if (!data) {
      return [];
    }

    const titles = data.filter((d) => d.title).map((d) => d.title);
    return titles as string[];
  } catch (error) {
    console.error('Game titles not fetched!', error);
    throw new Error('Game titles not fetched!');
  }
}

export async function getGamePageData(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('games').find({
      filters: { slug: { $eq: slug } },
      populate: {
        blocks: getDynamicZoneBlocks({ includeGamePageBlocks: true }),
        keyArt: { fields: ['url', 'alternativeText', 'width', 'height'] },
        logoDark: { fields: ['url', 'alternativeText', 'width', 'height'] },
        logoLight: { fields: ['url', 'alternativeText', 'width', 'height'] },
        competitionSlugs: { populate: '*' },
        hero: { populate: '*' },
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as GameType;

    if (!data) {
      return null;
    }

    data.blocks = await assembleBlocksWithAdditionalData(data.blocks, locale, data);
    data.hero = (data as any).hero?.[0];

    return data;
  } catch (error) {
    console.error('Game data not fetched', error);
    throw new Error('Game data not fetched');
  }
}

export async function getGamePageSeo(locale: Locale, slug: string) {
  try {
    const pageData = await strapiClient.collection('games').find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as GameType;
    if (!data) {
      return null;
    }
    return data.seo as SeoType;
  } catch (error) {
    console.error('Game Page SEO not fetched!', error);
    return null;
  }
}

export async function fetchGames(locale: Locale) {
  try {
    const games = await strapiClient.collection(GAMES_COLLECTION_KEY).find({
      sort: ['tournamentStart:asc'],
      populate: ['keyArt', 'logoDark'],
      ...getStrapiDefaultParams(locale),
    });
    const { data } = games;
    return { games: data as unknown as GameType[] };
  } catch (error) {
    console.error('Games not fetched!', error);
    throw new Error('Games not fetched!');
  }
}

export async function getGameCompetitions(locale: Locale) {
  try {
    const games = await strapiClient.collection(GAMES_COLLECTION_KEY).find({
      fields: ['slug'],
      populate: {
        logoDark: { populate: '*' },
        schedulePopupLogo: { fields: '*' },
        competitionSlugs: '*',
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = games.data as GameType[];
    if (!data) {
      return [];
    }

    return data
      .filter((game) => game.competitionSlugs.length > 0)
      .map((game) => ({
        slug: game.slug,
        logoDark: game.logoDark,
        schedulePopupLogo: game.schedulePopupLogo,
        competitionSlugs: game.competitionSlugs,
      }));
  } catch (error) {
    console.error('Game competitions not fetched!', error);
    throw new Error('Game competitions not fetched!');
  }
}
