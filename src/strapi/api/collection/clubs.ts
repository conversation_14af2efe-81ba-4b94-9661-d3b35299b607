import { Locale } from '@/hooks/i18n/const';
import { ClubType } from '@/strapi/types/collection/club';

import { getStrapiDefaultParams, strapiClient } from '../client';

export async function getClubTitles(locale: Locale) {
  try {
    const gamePageData = await strapiClient.collection('clubs').find({
      fields: ['name'],
      ...getStrapiDefaultParams(locale),
    });

    const data = gamePageData.data as ClubType[];
    if (!data) {
      return [];
    }

    const titles = data.filter((d) => d.name).map((d) => d.name);
    return titles as string[];
  } catch (error) {
    console.error('Club titles not fetched!', error);
    throw new Error('Club titles not fetched!');
  }
}
