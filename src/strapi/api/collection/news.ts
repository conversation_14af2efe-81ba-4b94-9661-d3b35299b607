import { Locale } from '@/hooks/i18n/const';

import { NewsFilter } from '../../../app/[locale]/news/_components/NewsAggregator';
import { NEWS_ARTICLES_COLLECTION_KEY, NewsArticleType } from '../../types/collection/news';
import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils';

export async function fetchNewsArticleSlugs(locale: Locale) {
  try {
    const newsArticles = await strapiClient
      .collection(NEWS_ARTICLES_COLLECTION_KEY)
      .find({ fields: ['slug'], ...getStrapiDefaultParams(locale) });

    const data = newsArticles.data as NewsArticleType[];
    if (!data) {
      return [];
    }

    const slugs = data.map((d) => d.slug);
    return slugs;
  } catch (error) {
    console.error('News Articles slugs not fetched!', error);
    throw new Error('News Articles slugs not fetched!');
  }
}

interface QueryParams {
  pagination?: { page?: number; pageSize?: number; withCount?: boolean; start?: number; limit?: number };
  search?: string;
  filter?: NewsFilter;
}

export async function fetchNewsArticles(locale: Locale, { pagination, search, filter }: QueryParams) {
  const isArchived = filter === 'all' ? undefined : filter === 'archived' ? true : false;

  try {
    const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
      sort: ['isArchived:asc', 'date:desc'],
      populate: ['tags', 'cover'],
      pagination,
      filters: {
        isArchived,
        ...(search && {
          $or: [{ title: { $containsi: search.toLowerCase() } }, { summary: { $containsi: search.toLowerCase() } }],
        }),
      },
      ...getStrapiDefaultParams(locale),
    });
    const { meta, data } = newsArticles;
    return { meta, articles: data as unknown as NewsArticleType[] };
  } catch (error) {
    console.error('News Articles data not fetched!', error);
    throw new Error('News Articles data not fetched!');
  }
}

export async function fetchSingleNewsArticle(locale: Locale, slug: string) {
  try {
    const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
      filters: { slug: { $eq: slug } },
      populate: { tags: { populate: '*' }, cover: { populate: '*' }, blocks: getDynamicZoneBlocks() },
      ...getStrapiDefaultParams(locale),
    });

    const article = newsArticles.data[0] as NewsArticleType;
    if (!article) {
      return null;
    }

    article.blocks = await assembleBlocksWithAdditionalData(article.blocks, locale);
    return article;
  } catch (error) {
    console.error('News single articles data not fetched!', error);
    throw new Error('News single articles data not fetched!');
  }
}

export async function fetchNewsArticleSeo(locale: Locale, slug: string) {
  try {
    const newsArticles = await strapiClient.collection(NEWS_ARTICLES_COLLECTION_KEY).find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    const article = newsArticles.data[0] as NewsArticleType;
    if (!article) {
      return null;
    }

    return article.seo;
  } catch (error) {
    console.error('News Articles SEO not fetched!', error);
    return null;
  }
}
