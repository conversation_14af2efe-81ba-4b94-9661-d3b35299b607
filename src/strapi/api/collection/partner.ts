import { Locale } from '@/hooks/i18n/const';
import { PartnerType } from '@/strapi/types/collection/partner';
import { SeoType } from '@/strapi/types/shared/seo';

import { getDynamicZoneBlocks } from '../blocks';
import { getStrapiDefaultParams, strapiClient } from '../client';
import { assembleBlocksWithAdditionalData } from '../utils';

export async function getSponsor(slug: string, locale: Locale) {
  try {
    const pageData = await strapiClient.collection('partners').find({
      filters: { slug: { $eq: slug } },
      populate: {
        blocks: getDynamicZoneBlocks(),
        coverImage: { populate: '*' },
        logo: { populate: '*' },
        partnerLabel: { populate: '*' },
      },
      ...getStrapiDefaultParams(locale),
    });

    const data = pageData.data[0] as PartnerType;

    if (!data) {
      return null;
    }

    data.blocks = await assembleBlocksWithAdditionalData(data.blocks, locale);
    return data;
  } catch (error) {
    console.error('Sponsor data not fetched', error);
    throw new Error('Sponsor data not fetched');
  }
}

export async function getSponsorSeo(locale: Locale, slug: string) {
  try {
    const sponsorData = await strapiClient.collection('partners').find({
      filters: { slug: { $eq: slug } },
      populate: 'seo',
      ...getStrapiDefaultParams(locale),
    });

    return sponsorData.data[0]?.seo as SeoType | null;
  } catch (error) {
    console.error('Sponsor SEO not fetched!', error);
    return null;
  }
}
