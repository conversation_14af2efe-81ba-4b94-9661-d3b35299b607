import { Locale } from '@/hooks/i18n/const';
import { DynamicZoneBlock, NEWS_BLOCK_KEY, NewsBlockType } from '@/strapi/types/block';
import { GAME_SCHEDULE_BLOCK_KEY, GameScheduleBlockType } from '@/strapi/types/gamePageBlock';

import { fetchNewsArticles } from '../collection/news';

export async function assembleBlocksWithAdditionalData(blocks: DynamicZoneBlock[], locale: Locale, data?: any) {
  const newsBlockIndex = blocks.findIndex((b) => b.__component === NEWS_BLOCK_KEY);
  if (newsBlockIndex !== -1) {
    const assembledBlock = await assembleNewsBlock(blocks[newsBlockIndex] as NewsBlockType, locale);
    blocks[newsBlockIndex] = assembledBlock;
  }

  const gameScheduleBlockIndex = blocks.findIndex((b) => b.__component === GAME_SCHEDULE_BLOCK_KEY);
  if (gameScheduleBlockIndex !== -1) {
    const gameScheduleBlock = blocks[gameScheduleBlockIndex] as GameScheduleBlockType;
    gameScheduleBlock.competitions = data?.competitionSlugs ?? [];
  }

  return blocks;
}

async function assembleNewsBlock(block: NewsBlockType, locale: Locale) {
  if (block.featuredArticles.length === 0) {
    const data = await fetchNewsArticles(locale, { pagination: { start: 0, limit: 4 } });
    block.featuredArticles = data?.articles ?? [];
  }

  return block;
}
