'use client';
import { formatDate } from 'date-fns';

import { useDateLocale } from '@/hooks/i18n/useDateLocale';

import { StrapiImage } from '../../components/StrapiImage/StrapiImage';
import { QualifierType } from '../../strapi/types/collection/qualifier';
import { Button } from '../../ui/components/Button';

export const QualifierCard = ({ title, buttons, startDate, endDate, teamsProgress, logo }: QualifierType) => {
  const locale = useDateLocale();
  const formattedStartDate = startDate ? formatDate(new Date(startDate), 'MMM do', { locale }) : '';
  const formattedEndDate = endDate ? formatDate(new Date(endDate), 'MMM do', { locale }) : '';

  return (
    <div className="flex flex-col gap-8 rounded-3xl bg-white p-4 pt-8 shadow-md lg:flex-row lg:items-center lg:p-8">
      {logo && (
        <StrapiImage alt="" className="h-[55px] w-full object-contain object-center lg:max-w-[153px]" image={logo} />
      )}

      <div className="text-dark-default flex flex-col items-center gap-1 lg:grow lg:items-start">
        <h2 className="text-h2 line-clamp-1 lg:text-[16px] lg:leading-[110%]">{title}</h2>
        <span className="font-fustat text-[16px] leading-[160%]">
          {formattedStartDate} - {formattedEndDate}
        </span>
        <span className="font-fustat text-[16px] leading-[160%] font-bold">{teamsProgress}</span>
      </div>

      {buttons.length > 0 && (
        <div className="flex flex-col gap-2 md:flex-row">
          {buttons.map((button) => (
            <Button
              key={button.id}
              {...button}
              brazeEventProperties={{
                button_name: `Primary Button (${button.text})`,
                location: `Qualifier List Block - Card (${title})`,
              }}
              isFullWidth
            />
          ))}
        </div>
      )}
    </div>
  );
};
