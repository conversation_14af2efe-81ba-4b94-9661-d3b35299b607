import { QualifiersListBlockType } from '../../strapi/types/block';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { QualifierCard } from './QualifierCard';

export const QualifiersListBlock = ({ section, qualifiers }: QualifiersListBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col gap-4">
        {qualifiers.map((qualifier) => (
          <QualifierCard key={qualifier.id} {...qualifier} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
