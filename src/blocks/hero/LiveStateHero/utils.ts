import { Locale } from '@/hooks/i18n/const';
import { MatchSeries, MatchSeriesContestant, MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { Stream } from '@/services/graphql/types/shared';
import { constructImageUrl } from '@/services/graphql/utils';

export interface SidebarMatch {
  id: string;
  type: MatchType;
  startTime: string;
  stream: Stream | null;
  contestants: Contestant[];
}

export enum MatchType {
  COMPLETED,
  LIVE,
  UPCOMING,
}

export interface Contestant {
  id: string;
  name: string;
  logoUrl: string | null;
  points: number | null;
}

export function convertMatchSeriesToHeroData(series: MatchSeries[], locale: Locale) {
  const matches = series.map((s) => {
    const contestants = getContestants(s.contestants);

    const localStreams = s.streams.filter((s) => s.language === locale);
    const stream = localStreams.find((s) => s.primary) ?? localStreams[0] ?? null;

    let type = MatchType.COMPLETED;
    if (s.status === MatchSeriesStatus.LIVE) {
      type = MatchType.LIVE;
    } else if ([MatchSeriesStatus.OPEN, MatchSeriesStatus.DELAYED, MatchSeriesStatus.POSTPONED].includes(s.status)) {
      type = MatchType.UPCOMING;
    }

    return { id: s.id, startTime: s.startTime, type, stream, contestants } as SidebarMatch;
  });

  return matches;
}

function getContestants(contestants: MatchSeriesContestant[]): Contestant[] {
  if (contestants.length === 0) {
    return [...Array(2)].map((_, i) => ({ id: i.toString(), name: 'TBD', points: null, logoUrl: null }));
  }

  return contestants.map((c) => ({
    id: c.team.id,
    name: c.team.name ?? 'TBD',
    logoUrl: constructImageUrl(c.team.images, 'logo_transparent_whitebg', '24x24'),
    points: c.points,
  }));
}
