interface Props {
  url: string;
  shouldAutoplay?: boolean;
  fallback?: React.ReactNode;
}

export const VideoEmbed = ({ fallback, ...rest }: Props) => {
  if (rest.url) {
    const embedUrl = constructEmbedUrl(rest);
    return (
      <iframe
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
        className="size-full"
        frameBorder="0"
        referrerPolicy="strict-origin-when-cross-origin"
        src={embedUrl}
        title="YouTube video player"
      />
    );
  }

  return fallback ?? null;
};

const EMBED_PATH = 'embed';
const AUTOPLAY_QUERY = '&autoplay=1';

function constructEmbedUrl({ url, shouldAutoplay }: Props) {
  if (!url) {
    return '';
  }

  const isEmbeddedVideo = url.includes(EMBED_PATH);
  const isAutoplay = url.includes(AUTOPLAY_QUERY);

  let embedUrl = url;
  if (!isEmbeddedVideo) {
    embedUrl = embedUrl.replace('live', EMBED_PATH);
  }

  if (!isAutoplay && shouldAutoplay) {
    embedUrl += AUTOPLAY_QUERY;
  }

  return embedUrl;
}
