import { useEffect, useState } from 'react';

import { MatchType, SidebarMatch } from './utils';

export function useSelectedMatch(matches: SidebarMatch[] | null) {
  const [selectedMatchId, setSelectedMatchId] = useState(matches ? getInitialSelectedMatchId(matches) : '');

  useEffect(() => {
    if (matches) {
      setSelectedMatchId(getInitialSelectedMatchId(matches));
    }
  }, [matches]);

  return { selectedMatchId, setSelectedMatchId };
}

function getInitialSelectedMatchId(matches: SidebarMatch[]) {
  function sortByTypeAndStartTime(a: SidebarMatch, b: SidebarMatch) {
    if (a.type === b.type) {
      return a.startTime < b.startTime ? -1 : a.startTime > b.startTime ? 1 : 0;
    }

    if (a.type === MatchType.LIVE) {
      return -1;
    }
    if (a.type === MatchType.UPCOMING && b.type === MatchType.COMPLETED) {
      return -1;
    }
    return 1;
  }

  const sortedMatches = matches.toSorted(sortByTypeAndStartTime);
  const viableStreamedMatch = sortedMatches.find(
    (m) => [MatchType.LIVE, MatchType.UPCOMING].includes(m.type) && m.stream,
  );

  return viableStreamedMatch?.id ?? '';
}
