import { format } from 'date-fns';

import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';

import { MatchesList } from './MatchesList';
import { MatchType, SidebarMatch } from './utils';

interface Props {
  matches: SidebarMatch[];
  selectedMatchId: string;
  onMatchSelect: (id: string) => void;
}

export const Sidebar = ({ matches, ...rest }: Props) => {
  const completedMatches = matches.filter((m) => m.type === MatchType.COMPLETED);
  const liveMatches = matches.filter((m) => m.type === MatchType.LIVE);

  const upcomingMatches = matches.filter((m) => m.type === MatchType.UPCOMING);
  const groupedUpcomingMatches = groupMatchesByStartTime(upcomingMatches);
  return (
    <div className="flex max-h-screen flex-1/4 flex-col gap-6 bg-white pt-4 pb-2 lg:max-w-[450px] lg:pt-8 lg:pb-5">
      <Only for="lgAndAbove">
        <div className="px-6">
          <Button
            brazeEventProperties={{
              button_name: 'CTA Button',
              location: 'Live State Hero - Sidebar',
            }}
            isFullWidth
            text="Club championship rankings"
            variant="gold"
          />
        </div>
      </Only>
      <div className="flex gap-4 px-4 max-lg:overflow-x-auto lg:flex-col lg:overflow-y-auto lg:px-6">
        {completedMatches.length !== 0 && (
          <MatchesList matches={completedMatches} type={MatchType.COMPLETED} {...rest} />
        )}
        {liveMatches.length !== 0 && <MatchesList matches={liveMatches} type={MatchType.LIVE} {...rest} />}
        {groupedUpcomingMatches.length !== 0 &&
          groupedUpcomingMatches.map((gm) => (
            <MatchesList
              matches={gm.matches}
              startTime={gm.startTime}
              type={MatchType.UPCOMING}
              {...rest}
              key={gm.startTime}
            />
          ))}
      </div>
    </div>
  );
};

//! matches should already be sorted in the consuming component
function groupMatchesByStartTime(matches: SidebarMatch[]) {
  const matchesWithStartTime: { startTime: string; matches: SidebarMatch[] }[] = [];

  for (const match of matches) {
    const formattedStartTime = format(match.startTime, 'MMM do');
    const groupedMatches = matchesWithStartTime.find((m) => m.startTime === formattedStartTime);

    if (groupedMatches) {
      groupedMatches.matches.push(match);
    } else {
      matchesWithStartTime.push({ startTime: formattedStartTime, matches: [match] });
    }
  }

  return matchesWithStartTime;
}
