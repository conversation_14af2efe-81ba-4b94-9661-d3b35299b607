import { MatchType, SidebarMatch } from '../utils';
import { Header } from './Header';
import { MatchBox } from './MatchBox';

interface Props {
  matches: SidebarMatch[];
  startTime?: string;
  type: MatchType;
  selectedMatchId: string;
  onMatchSelect: (id: string) => void;
}

export const MatchesList = ({ matches, startTime, selectedMatchId, onMatchSelect, type }: Props) => {
  return (
    <div className="flex flex-col-reverse gap-2 lg:flex-col lg:gap-1">
      <Header startTime={startTime} type={type} />
      <div className="flex gap-2 lg:flex-col">
        {matches.map((m) => (
          <MatchBox
            key={m.id}
            {...m}
            isSelected={m.id === selectedMatchId}
            stream={m.stream}
            type={type}
            onSelect={() => onMatchSelect(m.id)}
          />
        ))}
      </div>
    </div>
  );
};
