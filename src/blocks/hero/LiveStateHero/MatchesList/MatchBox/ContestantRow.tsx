import clsx from 'clsx';
import Image from 'next/image';

import ContestantPlaceholderImage from '@/ui/assets/images/ewc-placeholder.png';

import { Contestant, MatchType } from '../../utils';

type ContestantRowProps = Contestant & { type: MatchType; isWinner: boolean };

export const ContestantRow = ({ name, points, logoUrl, type, isWinner }: ContestantRowProps) => {
  return (
    <div className="flex items-center justify-between gap-2">
      <div className={clsx('flex items-center gap-2', type === MatchType.COMPLETED && !isWinner && 'opacity-50')}>
        <Image alt="" className="object-cover" height={24} src={logoUrl ?? ContestantPlaceholderImage} width={24} />
        <p className="font-primary text-dark-default text-sm leading-none font-bold uppercase">{name}</p>
      </div>
      <p
        className={clsx(
          'font-primary flex size-5 items-center justify-center rounded-xs text-xs leading-none font-bold uppercase',
          {
            'bg-dark-default text-white': type === MatchType.COMPLETED && isWinner,
            'bg-gray text-dark-default': type === MatchType.UPCOMING || (type === MatchType.COMPLETED && !isWinner),
            'bg-[#F40F30] text-white': type === MatchType.LIVE,
          },
        )}
      >
        {type === MatchType.UPCOMING ? '-' : points}
      </p>
    </div>
  );
};
