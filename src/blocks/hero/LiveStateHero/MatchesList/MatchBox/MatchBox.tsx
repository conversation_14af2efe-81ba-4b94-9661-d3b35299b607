import clsx from 'clsx';
import { format } from 'date-fns';

import { Stream } from '@/services/graphql/types/shared';

import { Contestant, MatchType } from '../../utils';
import { ContestantRow } from './ContestantRow';

interface MatchBoxProps {
  isSelected: boolean;
  onSelect: () => void;
  startTime: string;
  // stage: string;
  type: MatchType;
  stream: Stream | null;
  contestants: Contestant[];
}

export const MatchBox = ({ isSelected, onSelect, startTime, stream, contestants, type }: MatchBoxProps) => {
  const winnerId = contestants.sort((a, b) => (a?.points ?? 0) - (b?.points ?? 0)).at(-1)?.id;
  return (
    <div className={clsx('border-white-dirty relative rounded-sm max-lg:w-[220px]', !isSelected && 'border')}>
      {isSelected && (
        <div className="absolute start-[-4px] top-[calc(50%-6px)] hidden size-3 rotate-45 bg-[#F40F30] lg:block" />
      )}
      <div
        className={clsx(
          'relative flex h-full flex-col gap-1.5 rounded-sm',
          isSelected ? 'bg-white-dirty px-[15px] py-[11px]' : 'cursor-pointer px-[14px] py-[10px]',
        )}
        onClick={isSelected ? undefined : onSelect}
      >
        {isSelected && (
          <div className="absolute start-0 top-0 h-1 w-full bg-[#F40F30] max-lg:rounded-t-sm lg:h-full lg:w-1 lg:rounded-s-sm" />
        )}
        <div className="flex items-center gap-1">
          <p
            className={clsx(
              'font-primary rounded-[1px] p-0.5 text-[8px] leading-none font-bold text-white uppercase',
              type === MatchType.LIVE ? 'bg-[#F40F30]' : 'bg-dark-default',
            )}
          >
            {type === MatchType.LIVE ? 'live now' : format(new Date(startTime), 'h:mm a')}
          </p>
          {/* <p className="font-base text-dark-default text-[10px] leading-none font-extrabold">{stage}</p> */}
          {stream && (
            <p className="bg-gray-dark font-primary rounded-[1px] p-0.5 text-[8px] leading-none font-bold text-white uppercase">
              {stream.language}
            </p>
          )}
        </div>
        <div className={clsx('h-px w-full', isSelected ? 'bg-gray' : 'bg-white-dirty')} />
        <div className="flex flex-col">
          {contestants.map((c) => (
            <ContestantRow {...c} isWinner={c.id === winnerId} key={c.id} type={type} />
          ))}
        </div>
      </div>
    </div>
  );
};
