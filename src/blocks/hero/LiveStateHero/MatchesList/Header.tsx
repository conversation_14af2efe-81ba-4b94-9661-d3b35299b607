import { MatchType } from '../utils';

export const Header = ({ startTime, type }: { startTime?: string; type: MatchType }) => {
  if (type === MatchType.COMPLETED) {
    return (
      <div className="bg-gray-easy rounded-xs px-2 py-1 text-white">
        <div className="font-primary flex items-center justify-between text-[10px] leading-tight font-bold text-black uppercase">
          <p>completed</p>
        </div>
      </div>
    );
  } else if (type === MatchType.LIVE) {
    return (
      <div className="flex items-center gap-1 rounded-xs bg-[#F40F30] px-2 py-1">
        <div className="size-1.5 rounded-full bg-white" />
        <p className="font-primary text-[10px] leading-none font-bold text-white uppercase">live</p>
      </div>
    );
  }
  return (
    <div className="bg-dark-default rounded-xs px-2 py-1 text-white">
      <div className="font-primary flex items-center justify-between text-[10px] leading-none font-bold text-white uppercase">
        <p>upcoming</p>
        <p>{startTime}</p>
      </div>
    </div>
  );
};
