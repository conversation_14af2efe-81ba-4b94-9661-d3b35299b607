'use client';

import { useMemo } from 'react';

import { useCurrentLocale } from '@/hooks/i18n';
import { useGameMatchSeriesData } from '@/services/graphql/hooks';
import { HeroLiveStateType } from '@/strapi/types/hero';

import { useSelectedMatch } from './hooks';
import { Sidebar } from './Sidebar';
import { convertMatchSeriesToHeroData } from './utils';
import { VideoEmbed } from './VideoEmbed';

export function LiveStateHero({ noStreamLabel }: HeroLiveStateType) {
  const { data, loading, error } = useGameMatchSeriesData();
  const locale = useCurrentLocale();

  const matches = useMemo(
    () => (data ? convertMatchSeriesToHeroData(data.matchSeries.items, locale) : null),
    [data, locale],
  );

  const { selectedMatchId, setSelectedMatchId } = useSelectedMatch(matches);
  const selectedStreamUrl = matches?.find((m) => m.id === selectedMatchId)?.stream?.url ?? '';

  return (
    <section className="w-full">
      <div className="flex max-lg:flex-col">
        <div className="bg-dark-default flex aspect-video w-full flex-3/4 items-center justify-center">
          <VideoEmbed
            fallback={<div className="text-button-default text-white">{noStreamLabel ?? 'no stream to display'}</div>}
            shouldAutoplay
            url={selectedStreamUrl}
          />
        </div>
        {loading && <div>loading...</div>}
        {error && <div>error: {error.message}</div>}
        {matches && <Sidebar matches={matches} selectedMatchId={selectedMatchId} onMatchSelect={setSelectedMatchId} />}
      </div>
    </section>
  );
}
