import clsx from 'clsx';
import Image from 'next/image';

import { StrapiImage } from '@/components/StrapiImage';
import { ParticipatingTeamsBlockType } from '@/strapi/types/block';
import { TeamGridItemType } from '@/strapi/types/collection/participatingTeams';
import ChampionCrown from '@/ui/assets/images/champion-crown.png';
import TBDImage from '@/ui/assets/images/participating-team-tbd.png';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const ParticipatingTeamsBlock = ({ section, gridItems }: ParticipatingTeamsBlockType) => {
  return (
    <BlockSectionWrapper
      {...section}
      className="relative mx-auto w-full max-w-6xl px-4 py-[50px] max-md:px-0 lg:px-8 lg:py-[90px] [&_.section-title]:max-md:px-4"
    >
      <div className="hide-scrollbar grid w-full auto-rows-fr gap-2 overflow-x-auto py-4 max-md:grid-flow-col max-md:grid-rows-2 max-md:px-4 max-md:pt-10 md:grid-cols-4 md:gap-5 md:overflow-visible lg:grid-cols-5 xl:grid-cols-7">
        {gridItems.map((item) => {
          return <GridItem item={item} key={item.id} />;
        })}
      </div>
    </BlockSectionWrapper>
  );
};

const GridItem = ({ item }: { item: TeamGridItemType }) => {
  return (
    <div
      className={clsx(
        'relative flex min-h-[210px] w-full min-w-[170px] flex-col gap-3 rounded-4xl px-4 pt-4 pb-6 text-center shadow-sm',
        item.isTBD ? 'bg-gray-easy justify-end overflow-hidden' : 'justify-between bg-white',
      )}
    >
      {item.isCurrentChampion && (
        <Image
          alt=""
          className="absolute -top-[38px] -left-[30px] z-10 h-auto w-[65px] object-contain"
          quality={100}
          src={ChampionCrown}
        />
      )}
      <div className="relative z-10 flex flex-col items-center gap-1">
        {!item.isPlayer && item.teamLogo && (
          <div className="h-25 w-25 overflow-hidden rounded-xl">
            <StrapiImage className="h-full w-full object-contain" image={item.teamLogo} />
          </div>
        )}
        {item.isPlayer && item.playerPhoto && (
          <div className="relative h-25 w-25 overflow-hidden rounded-xl">
            <StrapiImage className="h-full w-full object-contain" image={item.playerPhoto} />
            {item.teamLogo && (
              <div className="absolute right-1 bottom-1 h-6 w-6">
                <StrapiImage className="h-full w-full object-contain" image={item.teamLogo} />
              </div>
            )}
          </div>
        )}
        {item.teamName && (
          <span className="font-riforma text-dark-default text-sm leading-[110%] font-bold">{item.teamName}</span>
        )}
        {item.playerName && (
          <span className="font-riforma text-dark-default text-sm leading-[110%] font-bold">{item.playerName}</span>
        )}
      </div>
      <div className="z-10 flex flex-col items-center gap-1">
        {item.subtitleText && <span className="text-[10px] leading-[120%]">{item.subtitleText}</span>}
        {item.qualifierName && (
          <div
            className={clsx(
              'flex items-center gap-2 rounded-sm px-2 py-1',
              item.isCurrentChampion ? 'bg-gold-primary' : 'bg-dark-default',
            )}
          >
            {item.qualifierLogo && (
              <div className="h-4 min-h-4 w-4 min-w-4">
                <StrapiImage className="h-full w-full object-contain" image={item.qualifierLogo} />
              </div>
            )}
            {item.qualifierName && (
              <span className="font-riforma text-[10px] font-bold text-white">{item.qualifierName}</span>
            )}
          </div>
        )}
      </div>
      {item.isTBD && (
        <Image alt="" className="absolute inset-0 h-full w-full object-cover" quality={100} src={TBDImage} />
      )}
    </div>
  );
};
