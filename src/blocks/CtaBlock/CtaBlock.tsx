import { CtaBlockType } from '@/strapi/types/block';
import { ButtonType } from '@/strapi/types/shared';
import { Button } from '@/ui/components/Button';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const CtaBlock = ({ ctas, section }: CtaBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col gap-8">
        {ctas.map((cta, i) => (
          <Cta {...cta} key={i} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};

interface CtaProps {
  title: string;
  description: string | null;
  button: ButtonType;
}

const Cta = ({ title, description, button }: CtaProps) => (
  <div className="flex justify-between gap-4 rounded-lg bg-white p-4 shadow-md max-md:flex-col md:items-center md:gap-6 md:rounded-3xl md:p-8">
    <div className="text-dark-default flex grow flex-col gap-1">
      <p className="text-h5">{title}</p>
      {description && <p className="text-base leading-tight font-normal">{description}</p>}
    </div>
    <div className="shrink-0 md:w-fit">
      <Button
        {...button}
        brazeEventProperties={{
          button_name: `Primary Button (${button.text})`,
          location: `CTA Block - Item (${title})`,
        }}
        isFullWidth
      />
    </div>
  </div>
);
