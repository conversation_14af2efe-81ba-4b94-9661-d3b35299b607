import clsx from 'clsx';

import { KeyStatisticsBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const KeyStatistics = ({ section, keyStatistics, hasBackground }: KeyStatisticsBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div
        className="flex flex-col flex-wrap items-center justify-center rounded-3xl md:mx-auto md:flex-row"
        style={{ backgroundColor: hasBackground ? 'white' : 'transparent' }}
      >
        {keyStatistics.map((statistic, i) => {
          return (
            <div
              className={clsx(
                'flex w-full flex-col items-center px-8 py-4 md:w-fit md:items-start md:gap-1 md:gap-y-4 lg:gap-2',
                keyStatistics.length > 1 && 'border-gray-mild not-last:max-md:border-b not-last:md:border-r',
              )}
              key={i}
            >
              {statistic.value && (
                <span className="font-primary text-dark-default text-[42px] leading-[1] font-bold md:text-[32px] lg:text-[48px] xl:text-[64px]">
                  {statistic.value}
                </span>
              )}
              {statistic.displayTitle && (
                <span className="text-gray-very-dark text-[14px]">{statistic.displayTitle}</span>
              )}
            </div>
          );
        })}
      </div>
    </BlockSectionWrapper>
  );
};
