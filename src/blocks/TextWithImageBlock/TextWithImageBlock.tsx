import Image from 'next/image';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { BaseProps } from '../shared/types';

interface Props extends BaseProps {
  heading: string;
  subtitle: string;
  content: string;
  imageUrl: string;
}

export const TextWithImageBlock = ({ heading, subtitle, content, imageUrl, ...rest }: Props) => {
  return (
    <BlockSectionWrapper {...rest}>
      <div className="flex items-center gap-4 max-lg:flex-col-reverse lg:gap-10">
        <div className="flex grow flex-col gap-4 lg:gap-5">
          <p className="text-h3 text-dark-default">{heading}</p>
          <p className="text-subtitle text-gray-dark">{subtitle}</p>
          <p className="text-paragraph text-dark-default">{content}</p>
        </div>

        {/* TODO: format this image url to use CDN url */}
        <Image
          alt="image alt"
          className="aspect-[2] min-h-[328px] w-full rounded-lg object-cover md:rounded-2xl lg:aspect-square lg:w-[512px] lg:rounded-3xl"
          height={512}
          src={imageUrl}
          width={512}
        />
      </div>
    </BlockSectionWrapper>
  );
};
