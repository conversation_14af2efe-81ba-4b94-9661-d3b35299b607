'use client';
import './slick-slide.css';

import { useState } from 'react';
import { useIsClient } from 'usehooks-ts';

import { AutoscrollingShowcaseType } from '@/strapi/types/block';
import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';
import { useScreenType } from '@/ui/providers/ScreenTypeProvider';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { ShowcaseGridRow } from './ShowcaseGridRow';

function splitArrayIntoThree(arr?: Array<any> | null) {
  if (!Array.isArray(arr) || !arr) return [];

  const size = Math.ceil(arr.length / 3);
  return [arr.slice(0, size), arr.slice(size, size * 2), arr.slice(size * 2)];
}

export const AutoscrollingShowcaseBlock = ({
  title,
  subtitle = null,
  description,
  button,
  section,
  clubs,
}: AutoscrollingShowcaseType) => {
  const splitItems = splitArrayIntoThree(clubs);
  const [isPaused, setIsPaused] = useState(false);
  const screenType = useScreenType();
  const isClient = useIsClient();

  return (
    <BlockSectionWrapper {...section}>
      <div className="relative flex w-full flex-col gap-7 lg:flex-row lg:items-center">
        <div className="flex flex-col justify-start gap-4 lg:w-2/5">
          <h2 className="text-h3 text-dark-default">{title}</h2>
          {subtitle && <p className="text-gray-dark text-subtitle">{subtitle}</p>}
          {description && <p className="text-dark-default text-paragraph">{description}</p>}
          {button && (
            <Button
              {...button}
              brazeEventProperties={{
                button_name: `Primary Button (${button.text})`,
                location: `Autoscrolling Showcase Block (${title})`,
              }}
            />
          )}
        </div>
        {isClient && (
          <div
            className="pointer-events-none flex w-full flex-col lg:pointer-events-auto lg:w-3/5"
            onMouseEnter={() => setIsPaused(true)}
            onMouseLeave={() => setIsPaused(false)}
          >
            <Only
              fallback={
                <>
                  {new Array(3).fill(undefined).map((_, index) => {
                    return (
                      <ShowcaseGridRow
                        isPaused={isPaused}
                        isRTL={index % 2 === 0}
                        items={splitItems[index]}
                        key={index}
                        screenType={screenType}
                        totalItems={clubs?.length ?? 0}
                      />
                    );
                  })}
                </>
              }
              for="mdAndBelow"
            >
              {clubs && (
                <ShowcaseGridRow
                  isPaused={isPaused}
                  isRTL={false}
                  items={clubs}
                  screenType={screenType}
                  totalItems={clubs?.length ?? 0}
                />
              )}
            </Only>
          </div>
        )}
      </div>
    </BlockSectionWrapper>
  );
};
