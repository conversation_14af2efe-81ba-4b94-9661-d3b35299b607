import clsx from 'clsx';
import { useEffect, useRef } from 'react';
import Slider from 'react-slick';

import { StrapiImage } from '@/components/StrapiImage';
import { MediaType } from '@/strapi/types/media';
import { ScreenTypeValue } from '@/ui/providers/ScreenTypeProvider';

interface ShowcaseGridRowProps {
  items: any[];
  isRTL: boolean;
  isPaused: boolean;
  screenType: ScreenTypeValue;
  totalItems: number;
}

export const ShowcaseGridRow = ({ items, isRTL, isPaused, screenType, totalItems }: ShowcaseGridRowProps) => {
  const sliderRef = useRef<Slider | null>(null);

  const { isSm, isMd } = screenType;

  const shouldAutoplay = () => {
    if (isSm || isMd) return true;
    if (totalItems > 15) return true;
    return false;
  };

  useEffect(() => {
    if (!shouldAutoplay()) return;

    const interval = setInterval(() => {
      if (isRTL) sliderRef.current?.slickPrev();
      else sliderRef.current?.slickNext();
    }, 2000);

    if (isPaused) clearInterval(interval);

    return () => clearInterval(interval);
  }, [sliderRef, isPaused, shouldAutoplay, isRTL]);

  const settings = {
    infinite: true,
    speed: 250,
    slidesToScroll: 1,
    slidesToShow: isSm ? 2 : 4,
    autoplay: false,
    autoplaySpeed: 2000,
    draggable: false,
    arrows: false,
    rtl: isRTL,
  };
  if (totalItems <= 15 && !isSm && !isMd) {
    return (
      <div className="grid grid-cols-4 gap-2.5 not-last:mb-2.5 2xl:grid-cols-4">
        {items.map((item, index) => {
          return <Card {...item} key={index} />;
        })}
      </div>
    );
  }

  return (
    <Slider ref={sliderRef} {...settings} className="showcase-autoscroll">
      {[...items, ...items].map((item, index) => {
        return <Card {...item} key={index} />;
      })}
    </Slider>
  );
};
interface CardProps {
  logo: MediaType | null;
  name: string | null;
}

const Card = ({ name, logo }: CardProps) => {
  return (
    <div
      className={clsx(
        'rounded-2xl bg-white shadow-[0px_6px_8px_0px_#0000000F] xl:rounded-4xl',
        'px-[19px] pt-3 pb-5',
        'xl:px-[28.5px] xl:pt-[14px] xl:pb-[22px]',
        'relative flex h-[182px] flex-col items-center justify-center gap-3',
      )}
    >
      <div className="h-28 w-28 overflow-hidden">
        {logo && <StrapiImage className="h-full w-auto justify-self-center object-contain" image={logo} />}
      </div>
      {name && (
        <span className="font-primary text-dark-default truncate text-center text-xs leading-normal font-bold whitespace-nowrap uppercase md:text-sm">
          {name}
        </span>
      )}
    </div>
  );
};
