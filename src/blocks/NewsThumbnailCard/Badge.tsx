import clsx from 'clsx';

type Variant = 'gold' | 'black';

const variantsMap: Record<Variant, string> = {
  black: 'bg-dark-default',
  gold: 'bg-[#BE9E59]',
};

export interface NewsCardBadge {
  text: string;
  variant: Variant;
}

export const Badge = ({ text, variant }: NewsCardBadge) => (
  <div className={clsx('rounded-sm px-[5px] py-[3px]', variantsMap[variant])}>
    <p className="font-primary text-xs font-bold text-white uppercase">{text}</p>
  </div>
);
