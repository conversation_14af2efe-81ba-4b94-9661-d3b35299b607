'use client';

import clsx from 'clsx';
import { formatDate } from 'date-fns';

import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { useContentDirection, useCurrentLocale } from '@/hooks/i18n';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { SiteConfigData } from '@/strapi/api/single/siteConfig';
import { GameType } from '@/strapi/types/collection/game';
import { JsonFieldType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';

type Props = Partial<GameType & Pick<SiteConfigData, 'translations'>>;

export const GameHeader = ({
  logoDark,
  logoLight,
  keyArt,
  tournamentStart,
  tournamentEnd,
  summary,
  ticketsUrl,
  prizePool,
  competingTeams,
  competingPlayers,
  translations,
}: Props) => {
  return (
    <section className="relative w-full">
      <div className="h-[196px] w-full md:h-[400px]">
        {keyArt && <StrapiImage className="h-full w-full object-cover" image={keyArt} />}
        {logoLight && (
          <div className="absolute start-[50px] top-[44px] hidden h-[76px] w-fit lg:block">
            <StrapiImage className="h-full w-full" image={logoLight} />
          </div>
        )}
      </div>
      <BasicInfoBlock
        competingPlayers={competingPlayers || null}
        competingTeams={competingTeams || null}
        logoDark={logoDark || null}
        prizePool={prizePool || null}
        summary={summary || null}
        ticketsUrl={ticketsUrl || null}
        tournamentEnd={tournamentEnd || null}
        tournamentStart={tournamentStart || null}
        translations={translations || null}
      />
    </section>
  );
};

type BasicInfoBlockProps = {
  tournamentStart: string | null;
  tournamentEnd: string | null;
  logoDark: MediaType | null;
  summary: string | null;
  competingTeams: number | null;
  competingPlayers: number | null;
  prizePool: string | null;
  ticketsUrl: string | null;
  translations?: JsonFieldType | null;
};

const BasicInfoBlock = ({
  tournamentStart,
  tournamentEnd,
  logoDark,
  summary,
  competingTeams,
  competingPlayers,
  prizePool,
  ticketsUrl,
  translations,
}: BasicInfoBlockProps) => {
  const locale = useDateLocale();
  const currentLocale = useCurrentLocale();
  const isChineseLocale = currentLocale === 'zh';

  return (
    <div className="mt-[clamp(-200px,-15%,-75px)] flex w-full justify-center px-4 lg:px-8 2xl:px-0">
      <div className="flex w-full max-w-[1396px] flex-col rounded-lg bg-white md:rounded-2xl lg:flex-row lg:items-start lg:rounded-4xl">
        <div className="flex grow flex-col items-center gap-2 px-4 pt-4 md:items-start md:gap-4 md:px-8 md:pt-8 lg:gap-5 lg:p-10">
          <div className="flex flex-col items-center gap-2 md:items-start">
            {tournamentStart && tournamentEnd && (
              <p className="text-paragraph font-bold">
                {isChineseLocale
                  ? `${formatDate(tournamentStart, 'MMM do', { locale })} - ${formatDate(tournamentEnd, 'MMM do', { locale })}`
                  : `${formatDate(tournamentStart, 'MMM dd', { locale })} - ${formatDate(tournamentEnd, 'MMM dd, yyyy', { locale })}`}
              </p>
            )}
            <div className="flex h-[72px] md:max-w-[337px]">
              {logoDark && <StrapiImage className="block h-full w-full object-contain" image={logoDark} />}
            </div>
          </div>
          <p className="text-paragraph text-dark-default">{summary}</p>
        </div>
        <StatsBlock
          competingPlayers={competingPlayers}
          competingTeams={competingTeams}
          prizePool={prizePool}
          ticketsUrl={ticketsUrl}
          translations={translations}
        />
      </div>
    </div>
  );
};

type StatsBlockProps = {
  competingTeams: number | null;
  competingPlayers: number | null;
  prizePool: string | null;
  ticketsUrl: string | null;
  translations?: JsonFieldType | null;
};

const StatsBlock = ({ competingTeams, competingPlayers, prizePool, ticketsUrl, translations }: StatsBlockProps) => {
  const { isRTL } = useContentDirection();

  return (
    <div className="border-gray-mild flex h-full flex-col justify-between lg:border-s-1">
      <div className="border-gray-mild mt-4 flex h-full flex-wrap items-center justify-around gap-4 border-t-1 px-4 md:justify-start lg:mt-0 lg:flex-col lg:flex-nowrap lg:items-start lg:justify-center lg:gap-2 lg:border-0 lg:px-0">
        {competingTeams && (
          <StatItem label={translations?.['competingClubs'] ?? 'Competing Clubs'} value={competingTeams.toString()} />
        )}
        {competingPlayers && (
          <StatItem
            label={translations?.['competingPlayers'] ?? 'Competing Players'}
            value={competingPlayers.toString()}
          />
        )}
        {competingTeams && prizePool && <div className="bg-gray-mild hidden h-[1px] w-full lg:block" />}
        {prizePool && <StatItem label={translations?.['prizePool'] ?? 'Prize Pool'} value={prizePool.toString()} />}
      </div>

      {ticketsUrl && (
        <LocalizedLink
          brazeEventProperties={{ location: 'Game Header', button_name: `Tickets Link (${ticketsUrl})` }}
          className={clsx(
            'bg-dark-default lg:rounded-0 flex h-[46px] cursor-pointer items-center justify-center rounded-b-lg md:h-[60px] md:rounded-b-2xl lg:h-[75px] lg:min-w-[200px] lg:rounded-none',
            isRTL ? 'lg:rounded-bl-4xl' : 'lg:rounded-br-4xl',
          )}
          href={ticketsUrl}
          target="_blank"
        >
          <p className="font-primary text-[14px] leading-1.5 font-bold text-white md:text-[18px]">
            {translations?.['buyTickets'] ?? 'BUY TICKETS'}
          </p>
        </LocalizedLink>
      )}
    </div>
  );
};

const StatItem = ({ value, label }: { value: string; label: string }) => (
  <div className="gap-0.5p-4 md: flex flex-col items-center justify-center py-3 md:items-start md:justify-start md:px-8 lg:h-full lg:justify-center lg:gap-2 lg:pl-8">
    <p className="font-primary text-radial-gold text-[32px] leading-8 lg:text-[48px] lg:leading-12 xl:text-[64px] xl:leading-16">
      {value}
    </p>
    <p className="font-base text-gray-very-dark text-[14px]">{label}</p>
  </div>
);
