interface Props {
  title: string;
  subtitle?: string | null;
  titleAlignment?: 'left' | 'center' | 'right' | null;
}

export const BlockSectionTitle = ({ title, subtitle, titleAlignment }: Props) => (
  <div
    className="text-dark-default section-title flex w-full flex-col gap-1"
    style={{ textAlign: titleAlignment ?? 'left' }}
  >
    <h2 className="text-h3">{title}</h2>
    {subtitle && <p className="font-base text-sm leading-[1.6] md:text-[18px]">{subtitle}</p>}
  </div>
);
