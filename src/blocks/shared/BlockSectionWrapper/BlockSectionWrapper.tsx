import clsx, { ClassValue } from 'clsx';
import { MdDoubleArrow } from 'react-icons/md';

import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { BlockSectionWrapperType, LinkType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';

import { BlockSectionDivider } from './BlockSectionDivider';
import { BlockSectionTitle } from './BlockSectionTitle';

// control page or component specific overrides (eg. remove paddings News Pages)
interface OverrideProps {
  className?: ClassValue;
}

interface BlockSectionWrapperProps {
  isCTAButton?: boolean;
}

type Props = React.PropsWithChildren<Partial<BlockSectionWrapperType> & BlockSectionWrapperProps & OverrideProps>;

export const BlockSectionWrapper = ({
  isSectionDividerVisible,
  subtitle,
  title,
  link,
  headerImage,
  isBlockVisible,
  isCTAButton,
  styleConfig,
  className,
  children,
  titleAlignment,
  navigation,
}: Props) => {
  if (isBlockVisible === false) return null;

  const renderLinkCTA = () => {
    if (!link) return null;
    return isCTAButton ? (
      <Button
        brazeEventProperties={{
          button_name: `Primary Button (${link.text})`,
          location: `Block Section Wrapper (${title})`,
        }}
        link={link.url}
        openInNewTab
        text={link.text}
      />
    ) : (
      <SeeAllLink {...link} />
    );
  };

  return (
    <section
      className={clsx('relative w-full', className)}
      id={navigation?.navigationId ?? undefined}
      style={styleConfig ?? undefined}
    >
      <div>
        {isSectionDividerVisible && <BlockSectionDivider />}
        <div className="relative flex flex-col gap-5 md:gap-8">
          {(title || link || headerImage) && (
            <div
              className={clsx('flex items-end justify-between gap-8', titleAlignment === 'right' && 'flex-row-reverse')}
            >
              {(title || headerImage) && (
                <div className="flex w-full flex-col gap-5 lg:flex-row lg:items-center">
                  {headerImage && (
                    <div className="flex h-[60px] w-[195px] items-center justify-center">
                      <StrapiImage className="h-full w-full object-contain" image={headerImage} />
                    </div>
                  )}
                  {title && <BlockSectionTitle subtitle={subtitle} title={title} titleAlignment={titleAlignment} />}
                </div>
              )}
              <Only for="lgAndAbove">{renderLinkCTA()} </Only>
            </div>
          )}
          {children}
          <Only for="mdAndBelow">{renderLinkCTA()}</Only>
        </div>
      </div>
    </section>
  );
};

const SeeAllLink = ({ text, url }: LinkType) => (
  <LocalizedLink
    brazeEventProperties={{ location: 'Block Section Wrapper', button_name: `Section Link (${url})` }}
    className="flex items-center gap-3 text-black"
    href={url}
  >
    <div className="h-px w-[100px] bg-black max-lg:hidden" />
    <p className="font-primary text-xs leading-[13px] font-bold whitespace-nowrap uppercase">{text}</p>
    <div className="py-2">
      <MdDoubleArrow className="size-3 rtl:rotate-180" />
    </div>
  </LocalizedLink>
);
