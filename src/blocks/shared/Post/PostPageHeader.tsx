'use client';
import { formatDate } from 'date-fns';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { StrapiImage } from '@/components/StrapiImage';
import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { RichTextContentType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';
import { PostPageHeaderType } from '@/strapi/types/shared';

interface BaseProps {
  title: string | null;
  date: string | null;
  cover: MediaType | null;
  children: React.ReactNode;
}

export const BaseHeader = ({ title, date, cover, children }: BaseProps) => {
  const locale = useDateLocale();

  return (
    <section className="flex flex-col gap-12">
      <div className="flex flex-col gap-5">
        {title && <h1 className="text-h1">{title}</h1>}
        {date && <p className="text-paragraph font-bold">{formatDate(date, 'MMM dd, yyyy.', { locale })}</p>}
        {children}
      </div>
      {cover && (
        <StrapiImage
          className="aspect-auto w-full rounded-lg object-cover md:rounded-2xl lg:rounded-4xl"
          image={cover}
        />
      )}
    </section>
  );
};

export const PostPageHeader = ({ intro, ...rest }: PostPageHeaderType) => {
  return <BaseHeader {...rest}>{intro && <p className="text-paragraph">{intro}</p>}</BaseHeader>;
};

type NewsPageHeaderProps = Omit<BaseProps, 'children'> & { intro: RichTextContentType | null };

export const NewsPageHeader = ({ intro, ...rest }: NewsPageHeaderProps) => {
  return <BaseHeader {...rest}>{intro && <RichTextContent content={intro} />}</BaseHeader>;
};
