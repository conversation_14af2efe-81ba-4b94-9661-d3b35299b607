'use client';

import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { TimelineBlockType } from '@/strapi/types/block';
import { TimelineItemType } from '@/strapi/types/helper/timelineItem';

import { ScrollWithFadeWrapper } from '../../components/ScrollWithFadeWrapper';
import TimelineLineImage from './timeline.png';
import { TimelineItem } from './TimelineItem';

export const TimelineBlock = ({ section, items }: TimelineBlockType) => {
  let upItems: TimelineItemType[] = [];
  let downItems: TimelineItemType[] = [];

  if (items.length > 0) {
    upItems = items.filter((_, i) => i % 2 === 0) as TimelineItemType[];
    downItems = items.filter((_, i) => i % 2 !== 0) as TimelineItemType[];
  }

  return (
    <BlockSectionWrapper {...section} isCTAButton>
      <ScrollWithFadeWrapper>
        <div className="flex w-fit flex-col justify-end">
          <TimelineRow isUp items={upItems} />
          <TimelineLine />
          <TimelineRow items={downItems} />
        </div>
      </ScrollWithFadeWrapper>
    </BlockSectionWrapper>
  );
};

const TimelineRow = ({ items, isUp }: { items: TimelineItemType[]; isUp?: boolean }) => {
  const itemClassName = isUp ? 'items-end' : 'items-start ps-[160px]';
  return (
    <div className={clsx('flex gap-[74px]', itemClassName)}>
      {items.map((item) => (
        <div className="relative flex flex-col" key={item.id}>
          {!isUp && <Dot position="top-[-24px]" />}
          <TimelineItem {...item} isUpItem={isUp} />
          {isUp && <Dot position="bottom-[-24px]" />}
        </div>
      ))}
    </div>
  );
};

const Dot = ({ position }: { position: string }) => {
  return (
    <div className="flex h-full items-center justify-center">
      <div className={`absolute ${position} h-[24px] w-[24px] rounded-full bg-black`} />
    </div>
  );
};

const TimelineLine = () => {
  return (
    <div className="flex h-6 w-full items-center justify-center">
      <div className="h-[1px] w-full">
        <Image alt="" className="h-full w-full" height={1} src={TimelineLineImage} width={500} />
      </div>
    </div>
  );
};
