import { StrapiImage } from '@/components/StrapiImage';
import { MediaBlockType } from '@/strapi/types/block';
import { isVideoMedia } from '@/utils/media';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { Video } from './Video';

export const MediaBlock = ({ media, autoplay, muted, loop, section, controls }: MediaBlockType) => {
  if (!media) {
    return null;
  }

  return (
    <BlockSectionWrapper {...section}>
      <div className="relative w-full rounded-lg md:rounded-2xl lg:rounded-4xl xl:rounded-[64px]">
        {isVideoMedia(media.url) ? (
          <Video autoplay={autoplay} controls={!!controls} loop={loop} muted={muted} video={media} />
        ) : (
          <StrapiImage
            className="aspect-auto w-full rounded-lg object-cover md:rounded-2xl lg:rounded-4xl"
            image={media}
          />
        )}
      </div>
    </BlockSectionWrapper>
  );
};
