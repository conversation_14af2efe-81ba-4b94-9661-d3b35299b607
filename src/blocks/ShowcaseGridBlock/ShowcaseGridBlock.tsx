import { ShowcaseGridBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { GridCard } from './GridCard';

export const ShowcaseGridBlock = ({ images, section }: ShowcaseGridBlockType) => {
  if (images?.length === 0) {
    return null;
  }

  return (
    <BlockSectionWrapper {...section}>
      <div className="flex w-full justify-center md:justify-start">
        <div className="hide-scrollbar grid w-full grid-flow-col grid-rows-2 gap-2 overflow-x-auto md:flex md:flex-wrap md:gap-4">
          {images?.map((item) => <GridCard {...item} key={item.id} />)}
        </div>
      </div>
    </BlockSectionWrapper>
  );
};
