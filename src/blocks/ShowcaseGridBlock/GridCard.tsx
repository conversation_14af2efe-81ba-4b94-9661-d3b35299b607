'use client';

import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { MediaType } from '@/strapi/types/media';

interface GridCardProps {
  image: MediaType;
  link: string | null;
  title: string | null;
}

export const GridCard = ({ image, link, title }: GridCardProps) => {
  const textRef = useRef<HTMLParagraphElement | null>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const el = textRef.current;
    if (el) {
      setIsTruncated(el.scrollWidth > el.clientWidth);
    }
  }, [title]);

  const CardComponent = (
    <div
      className={clsx(
        'flex h-[159px] w-[180px] flex-col items-center justify-center gap-4 rounded-3xl bg-white',
        link && 'cursor-pointer',
      )}
    >
      {image && (
        <div className="flex aspect-square h-[100px] w-[100px]">
          <StrapiImage className="h-full w-full object-contain" image={image} />
        </div>
      )}
      <div className="group relative w-full">
        <p
          className="text-h6 w-full truncate overflow-hidden px-3 text-center whitespace-nowrap uppercase md:px-5"
          ref={textRef}
        >
          {title}
        </p>
        {isTruncated && (
          <div className="text-h6 absolute bottom-full left-1/2 z-10 mb-1 hidden -translate-x-1/2 rounded bg-black px-2 py-1 whitespace-nowrap text-white uppercase group-hover:block">
            {title}
          </div>
        )}
      </div>
    </div>
  );

  if (link) {
    return (
      <LocalizedLink
        brazeEventProperties={{ location: 'Showcase Grid Block', button_name: `Grid Card Link (${link})` }}
        href={link}
        target="_blank"
      >
        {CardComponent}
      </LocalizedLink>
    );
  }
  return CardComponent;
};
