import React from 'react';

import { StrapiImage } from '@/components/StrapiImage';
import { RibbonBlockType } from '@/strapi/types/block';
import { Marquee } from '@/ui/components/Marquee';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const RibbonBlock = ({ ribbon, section }: RibbonBlockType) => {
  if (!ribbon?.logoPrimary && !ribbon?.logoSecondary) {
    return null;
  }

  const { background, logoPrimary, logoSecondary } = ribbon;
  const logos = [logoPrimary, logoSecondary].filter((logo) => logo !== null);
  return (
    <BlockSectionWrapper {...section}>
      <div
        className="py-6"
        style={{ backgroundImage: background ?? 'linear-gradient(90deg, #4E442D 0%, #987C4B 100%)' }}
      >
        <Marquee duration="20s" repeat={7}>
          <>
            {logos.map((logo, i) => (
              <StrapiImage className="h-[30px] w-[151px]" image={logo} key={`${logo.id}-${i}`} />
            ))}
          </>
        </Marquee>
      </div>
    </BlockSectionWrapper>
  );
};
