import { Prize, PrizeCurrency, Tournament, TournamentContestant } from '@/services/graphql/types/tournament';
import { constructImageUrl } from '@/services/graphql/utils';

import { StandingContestant, TableStanding } from './types';

export function convertToStandingsData(data: Tournament[]) {
  const allContestants = data.flatMap((d) => d.contestants).filter((c) => c?.rank) as TournamentContestant[];
  const contestantsRankMap = getContestantRankMap(allContestants);

  const allPrizepools = data.flatMap((d) => d.prizePool);

  const moneyPrizepools = allPrizepools.filter((p) => p?.currency === PrizeCurrency.USD);
  const moneyRankMap = getPrizeRankMap(moneyPrizepools);

  const pointsPrizepools = allPrizepools.filter((p) => p?.currency === PrizeCurrency.XTS);
  const pointsRankMap = getPrizeRankMap(pointsPrizepools);

  const ranks: TableStanding[] = [];
  for (const rank in moneyRankMap) {
    const prizeMoney = moneyRankMap[rank];
    const existingRank = ranks.find((r) => r.prize === prizeMoney);

    if (existingRank) {
      existingRank.ranks.push(Number(rank));
      existingRank.clubs.push(contestantsRankMap[rank] ?? null);
    } else {
      ranks.push({
        ranks: [Number(rank)],
        clubs: [contestantsRankMap[rank] ?? null],
        prize: prizeMoney,
        points: pointsRankMap[rank] ?? null,
      });
    }
  }

  return ranks;
}

function getContestantRankMap(contestants: TournamentContestant[]) {
  const contestantsRankMap: Record<number, StandingContestant> = {};
  contestants.forEach((c, i) => {
    if (!c?.rank) {
      return;
    }

    if (!contestantsRankMap[c.rank]) {
      contestantsRankMap[c.rank] = {
        rank: c.rank as number,
        name: c.team?.name ?? 'TBD',
        id: c.team?.id ?? i.toString(),
        logo: constructImageUrl(c.team?.images, 'logo_transparent_whitebg', '32x32'),
      };
    }
  });

  return contestantsRankMap;
}

function getPrizeRankMap(prizepool: (Prize | null)[]) {
  const prizeRankMap: Record<number, number> = {};
  prizepool.forEach((p) => {
    if (!p?.rank) {
      return;
    }
    if (!prizeRankMap[p.rank]) {
      prizeRankMap[p.rank] = p.amount as number;
    }
  });

  return prizeRankMap;
}
