'use client';

import { BlockSectionWrapper } from '@/blocks/shared/BlockSectionWrapper';
import { useGameTournamentsData } from '@/services/graphql/hooks';
import { StandingsTableBlockType } from '@/strapi/types/gamePageBlock';

import { StandingsTable } from './StandingsTable';
import { convertToStandingsData } from './utils';

export const StandingsTableBlock = ({ section, translations }: StandingsTableBlockType) => {
  const { data, loading, error } = useGameTournamentsData();
  const rankedData = data?.tournaments?.result ? convertToStandingsData(data.tournaments.result) : null;

  return (
    <BlockSectionWrapper {...section}>
      {loading && <div>loading...</div>}
      {error && <div>error: {error.message}</div>}
      {rankedData && <StandingsTable standings={rankedData} translations={translations} />}
    </BlockSectionWrapper>
  );
};
