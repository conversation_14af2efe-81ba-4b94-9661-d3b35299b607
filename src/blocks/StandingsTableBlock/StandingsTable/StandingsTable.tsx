import clsx from 'clsx';

import { JsonFieldType } from '@/strapi/types/helper';

import { TableStanding } from '../types';
import { Row } from './Row';

interface StandingsTableProps {
  standings: TableStanding[];
  translations?: JsonFieldType | null;
}

export const StandingsTable = ({ standings, translations }: StandingsTableProps) => {
  return (
    <div className="w-full overflow-x-auto">
      <table className="w-full shadow-md">
        <thead>
          <tr>
            <HeaderCell className="w-[50px] text-start md:w-[84px]" text={translations?.['rank'] ?? 'Rank'} />
            <HeaderCell className="w-auto ps-[42px] text-start md:ps-[54px]" text={translations?.['club'] ?? 'club'} />
            <HeaderCell className="w-[100px] text-end md:w-[140px]" text={translations?.['prizePool'] ?? 'prizepool'} />
            <HeaderCell
              className="w-[100px] text-end md:w-[140px]"
              text={translations?.['clubPoints'] ?? 'club points'}
            />
          </tr>
        </thead>
        <tbody>
          {standings.map((s) => (
            <Row {...s} key={s.prize} />
          ))}
        </tbody>
      </table>
    </div>
  );
};

interface HeaderCellProps {
  text: string;
  className?: string;
}

const HeaderCell = ({ text, className }: HeaderCellProps) => {
  return (
    <th
      className={clsx(
        'bg-dark-default px-2.5 py-2 whitespace-nowrap text-white',
        'font-primary text-[10px] leading-none uppercase md:text-xs',
        'first:ps-2.5 last:pe-2.5 md:first:ps-6 md:last:pe-6',
        'first:rounded-ss-lg last:rounded-se-lg md:first:rounded-ss-2xl md:last:rounded-se-2xl',
        className,
      )}
    >
      {text}
    </th>
  );
};
