import { QualifiersGridBlockType } from '../../strapi/types/block';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { QualifiersCard } from './QualifiersCard';

export const QualifiersGridBlock = ({ section, qualifiers }: QualifiersGridBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div className="hide-scrollbar flex gap-2 overflow-x-auto md:grid md:grid-cols-2 md:gap-4 lg:grid-cols-3 lg:gap-8">
        {qualifiers.map((qualifier) => (
          <QualifiersCard key={qualifier.id} {...qualifier} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
