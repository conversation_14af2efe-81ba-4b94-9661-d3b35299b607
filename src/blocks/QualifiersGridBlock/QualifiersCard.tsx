'use client';
import { formatDate } from 'date-fns';
import { FaGlobeAmericas } from 'react-icons/fa';
import { MdOutlineCalendarMonth } from 'react-icons/md';
import { MdPeopleAlt } from 'react-icons/md';

import { useDateLocale } from '@/hooks/i18n/useDateLocale';

import { StrapiImage } from '../../components/StrapiImage/StrapiImage';
import { QualifierType } from '../../strapi/types/collection/qualifier';
import { Button } from '../../ui/components/Button';

export const QualifiersCard = ({
  title,
  startDate,
  endDate,
  region,
  teamsProgress,
  logo,
  buttons,
  game,
}: QualifierType) => {
  const locale = useDateLocale();
  const formattedStartDate = startDate ? formatDate(new Date(startDate), 'MMM do', { locale }) : '';
  const formattedEndDate = endDate ? formatDate(new Date(endDate), 'MMM do', { locale }) : '';

  return (
    <div className="flex w-[300px] min-w-[300px] flex-col justify-between gap-2 rounded-2xl bg-white p-3 shadow-md md:w-full">
      <div className="bg-white-dirty flex items-center justify-between gap-4 rounded-sm">
        <div className="flex w-full flex-col">
          {game && game.gradientHex && (
            <div className="h-1 w-full shrink-0 rounded-tl-sm rounded-tr-sm" style={{ background: game.gradientHex }} />
          )}
          <div className="flex items-center justify-between gap-2 p-2 pr-1">
            <div className="flex flex-col">
              <div className="flex items-center gap-1">
                <MdOutlineCalendarMonth className="text-dark-default" size={14} />
                <span className="font-base text-dark-default text-[10px] leading-[160%] md:text-[13px]">
                  {formattedStartDate} - {formattedEndDate}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <FaGlobeAmericas className="text-dark-default" size={14} />
                <span className="font-base text-dark-default text-[10px] leading-[160%] font-extrabold md:text-[13px]">
                  {region}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <MdPeopleAlt className="text-dark-default" size={14} />
                <span className="font-base text-dark-default text-[10px] leading-[160%] font-extrabold md:text-[13px]">
                  {teamsProgress}
                </span>
              </div>
            </div>

            {logo && (
              <StrapiImage alt="" className="h-[33px] w-[108px] object-contain object-right px-2" image={logo} />
            )}
          </div>
        </div>
      </div>
      <div className="font-primary line-clamp-2 text-[14px] font-bold md:text-[16px]">{title}</div>
      {buttons.length > 0 && (
        <div className="flex flex-col gap-2 lg:flex-row">
          {buttons.map((button) => (
            <Button
              key={button.id}
              {...button}
              brazeEventProperties={{
                button_name: `Primary Button (${button.text})`,
                location: `Qualifiers Grid Block - Card (${title})`,
              }}
              isFullWidth
            />
          ))}
        </div>
      )}
    </div>
  );
};
