import React from 'react';

import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import { PromoBlockType } from '@/strapi/types/block';
import { Marquee } from '@/ui/components/Marquee';
import { isVideoMedia } from '@/utils/media';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export function PromoBlock({ title, subtitle, repeatingText, media, backgroundImage, section }: PromoBlockType) {
  return (
    <BlockSectionWrapper {...section}>
      <div className="relative bg-[#030303]/70 text-white">
        <StrapiImage className="absolute inset-0 aspect-[2] size-full w-full object-cover" image={backgroundImage} />
        <div className="absolute inset-0 size-full bg-[#030303]/70" />
        <div className="relative flex flex-col items-center gap-[54px] py-[144px] md:gap-[100px] md:py-[70px]">
          <div className="flex max-w-[900px] flex-col items-center gap-4 px-11 uppercase md:gap-8">
            <p className="text-h2">{title}</p>
            {subtitle && <p className="font-primary text-center text-sm leading-[1.4] font-bold">{subtitle}</p>}
          </div>
          {repeatingText && (
            <Marquee duration="20s" repeat={8}>
              <p className="flex-[0_0_auto] font-mono text-[21px] leading-[1] font-normal whitespace-nowrap uppercase">
                {repeatingText}
              </p>
            </Marquee>
          )}
          <div className="rounded-[20px] px-5">
            {isVideoMedia(media.url) ? (
              <StrapiVideo
                autoPlay
                className="aspect-video w-full max-w-[600px] rounded-[20px]"
                controls={false}
                loop
                muted
                video={media}
              />
            ) : (
              <StrapiImage
                className="aspect-video h-[338px] w-full max-w-[600px] rounded-[20px] object-cover"
                image={media}
              />
            )}
          </div>
        </div>
      </div>
    </BlockSectionWrapper>
  );
}
