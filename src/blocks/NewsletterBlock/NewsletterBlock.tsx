import Image from 'next/image';

import WinnersImage from '@/ui/assets/images/winners2024.png';
import { Only } from '@/ui/components/Only';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { BaseProps } from '../shared/types';
import { NewsletterBody } from './NewsletterBody';

export interface Props extends BaseProps {
  topText: string;
  title: string;
  buttonText: string;
}

export const NewsletterBlock = ({ topText, title, buttonText, ...rest }: Props) => {
  return (
    <BlockSectionWrapper {...rest}>
      <div className="flex rounded-2xl bg-white max-lg:flex-col lg:gap-[5px] lg:rounded-[54px]">
        <Only for="mdAndBelow">
          <Image
            alt="2024 winners with the EWC trophy"
            className="h-[167px] w-full rounded-tl-2xl rounded-tr-2xl object-cover object-center grayscale md:h-[284px]"
            height={167}
            quality={25}
            src={WinnersImage}
            width={735}
          />
        </Only>
        <div className="shrink-0 p-5 pt-4 lg:py-[54px] lg:pl-[54px]">
          <NewsletterBody buttonText={buttonText} title={title} topText={topText} />
        </div>
        <Only for="lgAndAbove">
          <div className="relative h-auto w-full overflow-hidden rounded-[54px]">
            <Image
              alt="2024 winners with the EWC trophy"
              className="absolute start-[100px] w-full rounded-full object-cover grayscale lg:-top-[75px] lg:h-[600px] xl:-top-[220px] xl:h-[750px]"
              height={750}
              src={WinnersImage}
              width={815}
            />
          </div>
        </Only>
      </div>
    </BlockSectionWrapper>
  );
};
