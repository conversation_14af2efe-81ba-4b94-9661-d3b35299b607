import clsx from 'clsx';

import { Button } from '@/ui/components/Button';

import { Props } from './NewsletterBlock';

export const NewsletterBody = ({ topText, title, buttonText }: Props) => (
  <div className="flex flex-col gap-5 max-lg:items-center">
    <div className="flex flex-col gap-2 max-lg:items-center">
      <p className="font-primary text-gold-primary text-base leading-snug font-bold">{topText}</p>
      <p className="font-primary text-dark-default text-[30px] leading-[0.91] font-bold uppercase max-lg:text-center lg:max-w-[435px] lg:text-5xl xl:max-w-[532px]">
        {title}
      </p>
    </div>
    <div className="flex flex-col gap-3 self-stretch max-lg:items-center">
      <div className="flex flex-col gap-2 self-stretch max-lg:items-center">
        <p className="font-primary text-dark-default text-[10px] leading-[1.1] font-bold uppercase">email address</p>
        <input
          className={clsx(
            'border-gray self-stretch rounded-xl border bg-white px-5 py-3',
            'placeholder:font-primary placeholder:text-gray-dark placeholder:text-[13px] placeholder:font-bold',
          )}
          placeholder="<EMAIL>"
          type="text"
        />
      </div>
      <Button
        brazeEventProperties={{
          button_name: `Primary Button (${buttonText})`,
          location: `Newsletter Block (${title})`,
        }}
        text={buttonText}
      />
    </div>
  </div>
);
