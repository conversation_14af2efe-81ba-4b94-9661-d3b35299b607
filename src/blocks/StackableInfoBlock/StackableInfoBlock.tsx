import { StackableInfoBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { StackableInfoItem } from './StackableInfoItem';

export const StackableInfoBlock = ({ items, isNumbered, section }: StackableInfoBlockType) => {
  if (!items?.length) {
    return null;
  }

  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col gap-8 md:gap-16 lg:gap-[90px]">
        {items.map((item, index) => (
          <StackableInfoItem {...item} key={item.id} number={isNumbered ? index + 1 : undefined} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
