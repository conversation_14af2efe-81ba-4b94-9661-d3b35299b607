import clsx from 'clsx';

import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import { StackableInfoCardType } from '@/strapi/types/helper/stackableInfo';
import { MediaType } from '@/strapi/types/media';
import { isVideoMedia } from '@/utils/media';

const borderStyles = 'rounded-lg md:rounded-2xl lg:rounded-3xl';
const mediaStyles = clsx('w-full max-lg:aspect-[1.8] lg:h-[250px] lg:w-[444px] object-cover', borderStyles);

export const InfoMedia = ({ media, number }: { media: MediaType; number?: number }) => {
  return (
    <div className={clsx('relative shrink-0 lg:mt-[14.5px]', borderStyles)}>
      {isVideoMedia(media.url) ? (
        <StrapiVideo autoPlay className={mediaStyles} loop muted video={media} />
      ) : (
        <StrapiImage className={mediaStyles} image={media} />
      )}
      {number && (
        <div className="absolute bottom-0 md:hidden">
          <NumberBox number={number} />
        </div>
      )}
    </div>
  );
};

export const NumberBox = ({ number }: { number: number }) => {
  return (
    <div className="flex size-[64px] shrink-0 items-center justify-center rounded-lg bg-white shadow-md md:rounded-xl">
      <p className="font-primary text-dark-default text-[40px] leading-[1] font-bold tracking-[-4%] uppercase">
        {number}.
      </p>
    </div>
  );
};

export const InfoCard = ({ title, icon, description }: StackableInfoCardType) => {
  if (!title && !icon && !description) {
    return null;
  }

  return (
    <div className="flex w-full flex-col gap-3 rounded-3xl bg-white p-4 shadow-md xl:w-[260px]">
      <div className="flex items-center gap-2">
        {icon && <span className="material-symbols-outlined size-6">{icon}</span>}
        {title && <p className="font-primary text-dark-default text-base leading-[1.1] font-bold uppercase">{title}</p>}
      </div>
      {description && <p className="font-base text-dark-default text-sm leading-normal font-normal">{description}</p>}
    </div>
  );
};
