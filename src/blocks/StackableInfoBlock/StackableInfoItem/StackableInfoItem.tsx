import clsx from 'clsx';

import { RichTextContent } from '@/blocks/RichTextBlock/RichTextContent';
import { StackableInfoItemType } from '@/strapi/types/helper/stackableInfo';

import { InfoCard, InfoMedia, NumberBox } from './components';

type Props = StackableInfoItemType & { number?: number };

export const StackableInfoItem = ({
  media,
  upperLabel,
  title,
  subtitle,
  descriptionRichText,
  cards,
  isLeftToRight,
  number,
}: Props) => {
  return (
    <div className="flex flex-col gap-4">
      <div
        className={clsx('flex flex-col gap-4 md:gap-8 lg:flex-row xl:gap-15', !isLeftToRight && 'lg:flex-row-reverse')}
      >
        {media && <InfoMedia media={media} number={number} />}
        <div className={clsx('flex gap-4 max-md:flex-col', media && 'grow')}>
          {number && (
            <div className={clsx(media && 'hidden md:block')}>
              <NumberBox number={number} />
            </div>
          )}
          <div className="flex flex-col gap-4">
            {upperLabel && <p className="text-paragraph font-bold text-black">{upperLabel}</p>}
            {title && <p className="text-h4 text-dark-default">{title}</p>}
            {subtitle && <p className="text-subtitle text-gray-dark">{subtitle}</p>}
            {descriptionRichText && <RichTextContent content={descriptionRichText} />}
          </div>
        </div>
      </div>
      {cards && cards.length !== 0 && (
        <div className="grid gap-4 md:grid-cols-3 xl:flex xl:flex-wrap xl:self-end">
          {cards.map((c) => (
            <InfoCard {...c} key={c.id} />
          ))}
        </div>
      )}
    </div>
  );
};
