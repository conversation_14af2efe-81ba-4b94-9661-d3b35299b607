import clsx from 'clsx';
import Image from 'next/image';

interface FullGalleryCardProps {
  imageUrl: string;
  imageCount: number;
}

export const FullGalleryCard = ({ imageUrl, imageCount }: FullGalleryCardProps) => {
  return (
    <div className="relative flex flex-1 cursor-pointer flex-col justify-end overflow-hidden rounded-lg bg-clip-padding md:rounded-2xl">
      <Image
        alt="asefas"
        className="absolute inset-0 aspect-square w-full rounded-lg object-cover md:rounded-2xl"
        height={266}
        src={imageUrl}
        width={266}
      />
      <div className="absolute inset-0 rounded-lg bg-[linear-gradient(225deg,rgba(255,255,255,0)_-0.03%,#FFFFFF_76.68%)] md:rounded-2xl" />
      <div
        className={clsx(
          'relative flex flex-col p-3 md:gap-1 md:p-6',
          'font-primary leading-normal font-bold uppercase',
        )}
      >
        <p className="text-dark-default text-base md:text-lg">{imageCount} pictures</p>
        <p className="text-gray-dark text-xs">full gallery</p>
      </div>
    </div>
  );
};
