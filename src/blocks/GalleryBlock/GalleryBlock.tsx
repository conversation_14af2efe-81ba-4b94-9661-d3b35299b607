import Image from 'next/image';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { BaseProps } from '../shared/types';
import { FullGalleryCard } from './FullGalleryCard';

interface Props extends BaseProps {
  imageUrls: string[];
}

export const GalleryBlock = ({ imageUrls, ...rest }: Props) => {
  const imagesForDisplay = imageUrls.slice(0, 3);
  return (
    <BlockSectionWrapper {...rest}>
      <div className="gap-4 max-md:grid max-md:grid-cols-2 md:flex 2xl:gap-8">
        {imagesForDisplay.map((url, i) => (
          <ImageCard imageUrl={url} key={i} />
        ))}
        <FullGalleryCard imageCount={imageUrls.length} imageUrl={imageUrls[4]} />
      </div>
    </BlockSectionWrapper>
  );
};

interface ImageCardProps {
  imageUrl: string;
}

const ImageCard = ({ imageUrl }: ImageCardProps) => (
  // TODO: format this image url to use CDN url
  <Image
    alt="asefas"
    className="aspect-square w-full min-w-0 flex-1 rounded-lg object-cover md:rounded-2xl"
    height={266}
    src={imageUrl}
    width={266}
  />
);
