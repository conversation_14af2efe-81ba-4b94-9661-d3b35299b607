import Image from 'next/image';
import React from 'react';

import { PullQuoteBlockType } from '@/strapi/types/block';
import QuoteImage from '@/ui/assets/images/quote.png';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const PullQuoteBlock = ({ text, authorName = null, authorTitle = null, section }: PullQuoteBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div className="relative flex flex-col gap-4 rounded-lg bg-white p-4 lg:rounded-4xl lg:p-8">
        <div className="absolute top-[-4px] left-[-16px] size-[50px] w-[50px] lg:top-[16px]">
          <Image alt="" height={70} src={QuoteImage} width={50} />
        </div>
        <p className="text-quote text-dark-default">{text}</p>
        {authorName && (
          <div className="font-base flex flex-col leading-tight">
            <p className="text-dark-default text-lg font-extrabold md:text-[21px]">{authorName}</p>
            {authorTitle && <p className="text-gray-dark text-sm font-semibold md:text-base">{authorTitle}</p>}
          </div>
        )}
      </div>
    </BlockSectionWrapper>
  );
};
