'use client';

import clsx from 'clsx';
import { BsPlusCircle } from 'react-icons/bs';
import { useToggle } from 'usehooks-ts';

import { logButtonClickedEvent } from '@/services/braze';
import { FaqSectionType } from '@/strapi/types/collection/faq';

import { RichTextContent } from '../RichTextBlock/RichTextContent';

export const FaqSection = ({ question, answer }: FaqSectionType) => {
  const [isOpen, toggleIsOpen] = useToggle(false);

  return (
    <div className={clsx('flex flex-col', !isOpen && 'border-[#A5A5A5] not-last:border-b')}>
      <button
        className="flex cursor-pointer items-center justify-between gap-4 py-8 pe-4 md:pt-10 md:pb-12 lg:ps-4"
        onClick={() => {
          toggleIsOpen();
          logButtonClickedEvent({ location: `Faq Block - Section (${question})`, button_name: `Expand Answer Button` });
        }}
      >
        {question && <p className="text-h5 text-start">{question}</p>}
        <BsPlusCircle
          className={clsx('ms-auto shrink-0 p-1 text-black transition-transform duration-300', isOpen && 'rotate-45')}
          size={32}
        />
      </button>
      {answer && (
        <div
          className={clsx(
            'overflow-y-hidden rounded-lg bg-white shadow-md transition-[height] transition-discrete duration-300 md:rounded-2xl xl:rounded-3xl',
            isOpen ? 'h-calc-size' : 'h-0',
          )}
        >
          <div className="p-4 md:p-8">
            <RichTextContent content={answer} />
          </div>
        </div>
      )}
    </div>
  );
};
