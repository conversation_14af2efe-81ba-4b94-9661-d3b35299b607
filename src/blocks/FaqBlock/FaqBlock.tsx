import { FAQBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { FaqSection } from './FaqSection';

export const FaqBlock = ({ faq, section }: FAQBlockType) => {
  if (!faq) {
    return null;
  }

  const { title, faqSections } = faq;
  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-col">
        {title && <p className="text-h1">{title}</p>}
        <div className="text-dark-default flex flex-col">
          {faqSections?.map((f) => <FaqSection key={f.id} {...f} />)}
        </div>
      </div>
    </BlockSectionWrapper>
  );
};
