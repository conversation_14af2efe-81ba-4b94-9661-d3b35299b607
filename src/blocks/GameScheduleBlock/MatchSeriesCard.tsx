'use client';

import clsx from 'clsx';
import { format } from 'date-fns';
import { useState } from 'react';

import { MatchSeries, MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { constructImageUrl } from '@/services/graphql/utils';
import { JsonFieldType } from '@/strapi/types/helper';
import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';
import { getLocalizedMatchDescriptor } from '@/utils/localization/getLocalizedMatchDescriptor';

import MatchesAndPlayersCard from './MatchesAndPlayersCard';
import MatchStatusPill from './MatchStatusPill';
import TeamScoreCard from './TeamScoreCard';

export const MatchSeriesCard = ({
  matchSeries,
  translations,
}: {
  matchSeries: MatchSeries;
  translations?: JsonFieldType | null;
}) => {
  const [isMatchesAndPlayerCardOpen, setIsMatchesAndPlayerCardOpen] = useState(
    matchSeries.status === MatchSeriesStatus.LIVE,
  );

  const team1LogoUrl: string | null = constructImageUrl(
    matchSeries.contestants[0]?.team.images,
    'logo_transparent_whitebg',
    '400x400',
  );
  const team2LogoUrl: string | null = constructImageUrl(
    matchSeries.contestants[1]?.team.images,
    'logo_transparent_whitebg',
    '400x400',
  );

  console.log(matchSeries);

  return (
    <div
      className={clsx('flex flex-col gap-2.5 max-md:gap-0.5', {
        'cursor-pointer': matchSeries?.contestants?.length > 0,
      })}
      onClick={() => {
        if (matchSeries?.contestants?.length > 0) setIsMatchesAndPlayerCardOpen((prev) => !prev);
      }}
    >
      <Only for="sm">
        <div className="grid grid-cols-3 items-center rounded-t-sm bg-white px-2.5 py-1.5">
          {matchSeries.startTime && (
            <span className="text-tag text-dark-default/80">
              {format(new Date(matchSeries.startTime), 'EEE, MMM d - h:mm a')}
            </span>
          )}
          {matchSeries.status && (
            <div className="justify-self-center">
              <MatchStatusPill status={matchSeries.status} translations={translations} />
            </div>
          )}
          <span className="text-tag text-dark-default/80 justify-self-end text-[8px]!">
            {getLocalizedMatchDescriptor(matchSeries.metadata, translations || {})}
          </span>
        </div>
      </Only>
      <div className="flex items-center gap-2.5 max-md:gap-0.5">
        <TeamScoreCard
          contestant={matchSeries.contestants[0]}
          logoUrl={team1LogoUrl}
          matchStatus={matchSeries.status}
        />

        <Only for="mdAndAbove">
          <div className="flex min-w-[130px] flex-col items-center justify-center gap-1 text-center">
            {matchSeries.startTime && (
              <span className="text-tag text-dark-default/80">
                {format(new Date(matchSeries.startTime), 'EEE, MMM d - h:mm a')}
              </span>
            )}
            {matchSeries.status && <MatchStatusPill status={matchSeries.status} translations={translations} />}
            <span className="text-tag text-dark-default/80 text-[8px]!">
              {getLocalizedMatchDescriptor(matchSeries.metadata, translations || {})}
            </span>
          </div>
        </Only>

        <TeamScoreCard
          contestant={matchSeries.contestants[1]}
          isLayoutReversed
          logoUrl={team2LogoUrl}
          matchStatus={matchSeries.status}
        />
      </div>

      {isMatchesAndPlayerCardOpen && <MatchesAndPlayersCard matchSeries={matchSeries} matches={matchSeries.matches} />}

      {matchSeries.status === MatchSeriesStatus.LIVE && (
        <Only for="sm">
          <Button
            brazeEventProperties={{
              button_name: 'Watch Now',
              location: 'Game Schedule Block',
            }}
            isFullWidth
            size="small"
            text="Watch Now"
            variant="red"
            onClick={(e) => {
              e.stopPropagation();
              window.scrollTo({ top: 0, behavior: 'smooth' });
            }}
          />
        </Only>
      )}
    </div>
  );
};
