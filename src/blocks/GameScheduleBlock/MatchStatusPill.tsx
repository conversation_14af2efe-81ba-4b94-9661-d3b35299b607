import clsx from 'clsx';

import { MatchSeriesStatus } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';

const MatchStatusPill = ({
  status,
  translations,
}: {
  status: MatchSeriesStatus;
  translations?: JsonFieldType | null;
}) => {
  const defaultStatusMap = {
    [MatchSeriesStatus.CANCELLED]: 'Cancelled',
    [MatchSeriesStatus.DELAYED]: 'Upcoming',
    [MatchSeriesStatus.FINISHED]: 'Completed',
    [MatchSeriesStatus.LIVE]: 'Live Now',
    [MatchSeriesStatus.OPEN]: 'Upcoming',
    [MatchSeriesStatus.POSTPONED]: 'Upcoming',
  };

  const getStatusText = (status: MatchSeriesStatus): string => {
    const defaultText = defaultStatusMap[status];
    return translations?.[defaultText.toLowerCase()] || defaultText;
  };

  return (
    <div
      className={clsx('flex w-fit items-center justify-center rounded-sm px-2 py-1', {
        'text-dark-default bg-white': status === MatchSeriesStatus.FINISHED,
        'bg-[#F40F30] text-white': status === MatchSeriesStatus.LIVE,
        'bg-dark-default text-white':
          status === MatchSeriesStatus.OPEN ||
          status === MatchSeriesStatus.DELAYED ||
          status === MatchSeriesStatus.POSTPONED,
      })}
    >
      <span className="text-tag flex items-center gap-1 uppercase">
        {status === MatchSeriesStatus.LIVE && <div className="h-1.5 w-1.5 rounded-full bg-white" />}
        {getStatusText(status)}
      </span>
    </div>
  );
};

export default MatchStatusPill;
