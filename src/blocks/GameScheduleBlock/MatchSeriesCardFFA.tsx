'use client';

import clsx from 'clsx';
import { format } from 'date-fns';

import { MatchSeries, MatchSeriesMetadata, MatchSeriesMetadataType } from '@/services/graphql/types/matchSeries';

import MatchStatusPill from './MatchStatusPill';

function getScheduleItemTitle(metadata: MatchSeriesMetadata[], tournamentName: string) {
  const groupNames = metadata
    .filter((m) => m.type === 'GROUP')
    .sort((a, b) => {
      // Handle null positions by treating them as 0 or comparing them safely
      const posA = a.position ?? Number.MAX_SAFE_INTEGER;
      const posB = b.position ?? Number.MAX_SAFE_INTEGER;
      return posA - posB;
    })
    .map((m) => m.name); // e.g. ['Group A', 'Group B']

  if (groupNames.length > 0) {
    // Multiple groups in Battle Royale (e.g., Group A Group B)
    return groupNames;
  }

  const round = metadata.find((m) => m.type === 'ROUND');
  if (round) {
    return round.name; // e.g. "Elimination Finals", "Quarterfinal", "Week 7"
  }

  const bracket = metadata.find((m) => m.type === 'BRACKET');
  if (bracket) {
    return bracket.name; // e.g. "Playoffs", "Regular", "Knockout"
  }

  // Fallback to tournament name
  return tournamentName;
}

export const MatchSeriesCardFFA = ({ matchSeries }: { matchSeries: MatchSeries }) => {
  const displayText = getScheduleItemTitle(matchSeries.metadata, '');

  return (
    <div
      className={clsx(
        'flex items-center justify-between gap-2.5 rounded-md bg-white px-4 py-1.5 max-md:gap-0.5 max-md:px-2',
      )}
    >
      <div className="flex flex-col gap-1 px-2.5 py-1.5">
        {matchSeries.startTime && (
          <span className="text-tag text-dark-default/80">
            {format(new Date(matchSeries.startTime), 'EEE, MMM d - h:mm a')}
          </span>
        )}
        {matchSeries.status && (
          <div className="justify-self-center">
            <MatchStatusPill status={matchSeries.status} />
          </div>
        )}
        <span className="text-tag text-dark-default/80 justify-self-end text-[8px]!">
          {matchSeries?.metadata.find((m) => m.type === MatchSeriesMetadataType.BRACKET)?.name ?? ''}
        </span>
      </div>
      {Array.isArray(displayText) ? (
        <div className="flex items-center gap-2">
          <span className="text-button-big text-[18px]! max-md:text-center max-md:text-[10px]">{displayText[0]}</span>
          <span className="text-tag">vs</span>
          <span className="text-button-big text-[18px]! max-md:text-center max-md:text-[10px]">{displayText[1]}</span>
        </div>
      ) : (
        <span className="text-button-big text-[18px]! max-md:text-center max-md:text-[10px]">{displayText}</span>
      )}
    </div>
  );
};
