'use client';

import clsx from 'clsx';
import { format } from 'date-fns';

import { useDateLocale } from '@/hooks/i18n/useDateLocale';
import { MatchSeries } from '@/services/graphql/types/matchSeries';
import { JsonFieldType } from '@/strapi/types/helper';
import { getLocalizedMatchDescriptor } from '@/utils/localization/getLocalizedMatchDescriptor';

import MatchStatusPill from './MatchStatusPill';

export const MatchSeriesCardFFA = ({
  matchSeries,
  translations,
}: {
  matchSeries: MatchSeries;
  translations?: JsonFieldType | null;
}) => {
  const locale = useDateLocale();
  const localizedDescriptor = getLocalizedMatchDescriptor(matchSeries.metadata, translations || {});

  const groupNames = matchSeries.metadata
    .filter((m) => m.type === 'GROUP')
    .sort((a, b) => (a.position ?? 0) - (b.position ?? 0))
    .map((m) => (translations && translations[m.id]) || m.name);

  const displayText = groupNames.length > 1 ? groupNames : localizedDescriptor;

  return (
    <div
      className={clsx(
        'flex items-center justify-between gap-2.5 rounded-md bg-white px-4 py-1.5 max-md:gap-0.5 max-md:px-2',
      )}
    >
      <div className="flex flex-col gap-1 px-2.5 py-1.5">
        {matchSeries.startTime && (
          <span className="text-tag text-dark-default/80">
            {format(new Date(matchSeries.startTime), 'EEE, MMM d - h:mm a', { locale })}
          </span>
        )}
        {matchSeries.status && (
          <div className="justify-self-center">
            <MatchStatusPill status={matchSeries.status} translations={translations} />
          </div>
        )}
        <span className="text-tag text-dark-default/80 justify-self-end text-[8px]!">
          {getLocalizedMatchDescriptor(matchSeries.metadata, translations || {})}
        </span>
      </div>
      {Array.isArray(displayText) ? (
        <div className="flex items-center gap-2">
          <span className="text-button-big text-[18px]! max-md:text-center max-md:text-[10px]">{displayText[0]}</span>
          <span className="text-tag">vs</span>
          <span className="text-button-big text-[18px]! max-md:text-center max-md:text-[10px]">{displayText[1]}</span>
        </div>
      ) : (
        <span className="text-button-big text-[18px]! max-md:text-center max-md:text-[10px]">{displayText}</span>
      )}
    </div>
  );
};
