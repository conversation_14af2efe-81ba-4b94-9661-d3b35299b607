'use client';

import { format } from 'date-fns';
import { groupBy } from 'lodash';
import { Fragment } from 'react';

import { useMatchSeriesData } from '@/services/graphql/hooks';
import { TournamentVariant } from '@/services/graphql/types/matchSeries';
import { GameScheduleBlockType } from '@/strapi/types/gamePageBlock';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { MatchSeriesCard } from './MatchSeriesCard';
import { MatchSeriesCardFFA } from './MatchSeriesCardFFA';

const competitionVariantMapper = {
  [TournamentVariant.FFA_SQUAD]: 'ffa',
  [TournamentVariant.FFA_DUO]: 'ffa',
  [TournamentVariant.FFA_SOLO]: 'ffa',
  [TournamentVariant.CUSTOM_TEAM]: 'teams',
  [TournamentVariant['10_ON_10']]: 'teams',
  [TournamentVariant['11_ON_11']]: 'teams',
  [TournamentVariant['12_ON_12']]: 'teams',
  [TournamentVariant['1_ON_1']]: 'teams',
  [TournamentVariant['1_ON_1_TEAM']]: 'teams',
  [TournamentVariant['15_ON_15']]: 'teams',
  [TournamentVariant['3_ON_3']]: 'teams',
  [TournamentVariant['5_ON_5']]: 'teams',
  [TournamentVariant['6_ON_6']]: 'teams',
  [TournamentVariant['7_ON_7']]: 'teams',
};

export const GameScheduleBlock = ({ competitions, section, translations }: GameScheduleBlockType) => {
  const { data, loading, error } = useMatchSeriesData(competitions?.map((c) => c.competitionId) ?? []);

  if (loading) return null;
  if (error) return null;
  if (!data || !data.matchSeries || data.matchSeries.items.length === 0) return <div>no data</div>;

  const groupedData = groupBy(
    data.matchSeries.items,
    (item) => item.startTime && format(new Date(item.startTime), 'yyyy-MM-dd'),
  );

  return (
    <BlockSectionWrapper {...section}>
      {competitions &&
        competitions?.length > 0 &&
        Object.entries(groupedData).map(([date, matchSeries]) => {
          return (
            <Fragment key={date}>
              <h4 className="text-h4" key={date}>
                {format(new Date(date), 'EEE, MMM do')}
              </h4>
              <div className="flex flex-col gap-2.5">
                {matchSeries.map((ms) =>
                  competitionVariantMapper[ms.tournament.variant] === 'ffa' ? (
                    <MatchSeriesCardFFA key={ms.id} matchSeries={ms} translations={translations} />
                  ) : (
                    <MatchSeriesCard key={ms.id} matchSeries={ms} translations={translations} />
                  ),
                )}
              </div>
            </Fragment>
          );
        })}
    </BlockSectionWrapper>
  );
};
