'use client';

import { BlocksRenderer } from '@strapi/blocks-react-renderer';
import clsx from 'clsx';

import { LocalizedLink } from '@/components/LocalizedLink';
import { RichTextContentType } from '@/strapi/types/helper';
import { MediaType } from '@/strapi/types/media';

import { StrapiImage } from '../../../components/StrapiImage';

export const RichTextContent = ({
  content,
  paragraphClassName,
}: {
  content: RichTextContentType;
  paragraphClassName?: string;
}) => {
  return (
    <div className="text-dark-default flex flex-col gap-4 lg:gap-8">
      <BlocksRenderer blocks={blocks(paragraphClassName)} content={content} modifiers={modifiers} />
    </div>
  );
};

const Heading = ({ level, children }: { level: number; children?: React.ReactNode }) => {
  switch (level) {
    case 1:
      return <h1 className="text-h1 uppercase">{children}</h1>;
    case 2:
      return <h2 className="text-h2 uppercase">{children}</h2>;
    case 3:
      return <h3 className="text-h3 uppercase">{children}</h3>;
    case 4:
      return <h4 className="text-h4 uppercase">{children}</h4>;
    case 5:
      return <h5 className="text-h5">{children}</h5>;
    default:
      return <h6 className="text-h6">{children}</h6>;
  }
};

const Paragraph = ({ children, className }: { children?: React.ReactNode; className?: string }) => {
  const style = className ?? 'text-paragraph';
  return <p className={clsx(style)}>{children}</p>;
};

const Link = ({ url, children }: { url: string; children?: React.ReactNode }) => (
  <LocalizedLink
    brazeEventProperties={{ location: 'Rich Text Block', button_name: `Link (${url})` }}
    className="text-gold-primary text-paragraph underline"
    href={url}
  >
    {children}
  </LocalizedLink>
);

const listDefaultStyles = 'text-paragraph list-inside';
const List = ({ format, children }: { format: 'unordered' | 'ordered'; children?: React.ReactNode }) =>
  format === 'unordered' ? (
    <ul className={clsx(listDefaultStyles, 'list-disc')}>{children}</ul>
  ) : (
    <ol className={clsx(listDefaultStyles, 'list-decimal')}>{children}</ol>
  );

const CodeBlock = ({ children }: { children?: React.ReactNode }) => (
  <pre className="bg-gray-dark rounded-sm p-2 text-black">{children}</pre>
);

const Image = ({ image }: { image: MediaType }) => {
  return (
    <StrapiImage
      alt={image.alternativeText ?? ''}
      className="aspect-[2] w-full rounded-lg object-cover md:rounded-2xl lg:rounded-4xl"
      image={image}
      isFullUrl
    />
  );
};

export const blocks = (paragraphClassName?: string) => ({
  heading: Heading,
  paragraph: (props: { children?: React.ReactNode }) => (
    <Paragraph className={paragraphClassName}>{props.children}</Paragraph>
  ),
  link: Link,
  list: List,
  code: CodeBlock,
  // eslint-disable-next-line jsx-a11y/alt-text, @typescript-eslint/no-explicit-any
  image: (props: any) => <Image image={props.image} />,
});
const InlineCode = ({ children }: { children: React.ReactNode }) => (
  <code className="bg-gray-dark inline-block rounded-sm px-1 text-black">{children}</code>
);

export const modifiers = {
  code: InlineCode,
};
