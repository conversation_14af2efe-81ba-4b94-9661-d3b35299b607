import clsx from 'clsx';

import { RichTextBlockType } from '@/strapi/types/block';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { RichTextContent } from './RichTextContent';

export const RichTextBlock = ({ columns, section }: RichTextBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div className={clsx('grid gap-8', columns.length === 2 ? 'md:grid-cols-2' : 'grid-cols-1')}>
        {columns.map((c) => (
          <RichTextContent content={c.content} key={c.id} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
