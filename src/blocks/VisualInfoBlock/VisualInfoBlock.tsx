import clsx from 'clsx';

import { StrapiImage } from '@/components/StrapiImage';
import { StrapiVideo } from '@/components/StrapiVideo';
import { VisualInfoBlockType } from '@/strapi/types/block';
import { Button } from '@/ui/components/Button';
import { isVideoMedia } from '@/utils/media';

import { RichTextContent } from '../RichTextBlock/RichTextContent';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

const mediaStyles =
  'max-lg:h-[368px] w-full rounded-lg object-cover md:rounded-2xl lg:aspect-square lg:w-[512px] lg:rounded-3xl';

export const VisualInfoBlock = ({
  title,
  subtitle,
  descriptionRichText,
  button,
  isMediaOnRight,
  media,
  section,
}: VisualInfoBlockType) => {
  return (
    <BlockSectionWrapper {...section}>
      <div
        className={clsx(
          'flex gap-4 max-lg:flex-col-reverse lg:items-center lg:gap-10',
          !isMediaOnRight && 'lg:flex-row-reverse',
        )}
      >
        <div className="flex grow flex-col gap-4 lg:gap-5">
          {title && <p className="text-h4 text-dark-default">{title}</p>}
          {subtitle && <p className="text-subtitle text-gray-dark">{subtitle}</p>}
          {descriptionRichText && <RichTextContent content={descriptionRichText} />}
          {button && (
            <Button
              {...button}
              brazeEventProperties={{
                button_name: `Primary Button (${button.text})`,
                location: `Visual Info Block (${title})`,
              }}
            />
          )}
        </div>
        {isVideoMedia(media.url) ? (
          <StrapiVideo autoPlay className={mediaStyles} loop muted video={media} />
        ) : (
          <StrapiImage className={mediaStyles} image={media} />
        )}
      </div>
    </BlockSectionWrapper>
  );
};
