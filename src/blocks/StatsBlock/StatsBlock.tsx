interface Props {
  statsData: Array<{ value: string; label: string }>;
}

export const StatsBlock = ({ statsData }: Props) => {
  return (
    <div className="flex flex-col rounded-[8px] bg-white p-4 md:w-fit md:flex-row md:rounded-2xl xl:rounded-3xl xl:p-6">
      {statsData.map((stat, index) => {
        return (
          <div
            className="border-gray-mild flex flex-col items-center gap-2 border-b-1 px-2 last:border-0 sm:py-4 md:w-fit md:items-start md:border-r-1 md:border-b-0 lg:px-5 lg:py-6 xl:px-7 xl:py-4"
            key={`${stat} - ${index}`}
          >
            <p className="font-primary text-[32px] leading-8 lg:text-[48px] lg:leading-12 xl:text-[64px] xl:leading-16">
              {stat.value}
            </p>
            <p className="font-base text-gray-very-dark text-[14px]">{stat.label}</p>
          </div>
        );
      })}
    </div>
  );
};
