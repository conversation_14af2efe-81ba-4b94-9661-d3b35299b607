import clsx from 'clsx';
import { formatDate } from 'date-fns';
import Image from 'next/image';

import { logButtonClickedEvent } from '@/services/braze';

import { useDateLocale } from '../../hooks/i18n/useDateLocale';

export interface EventItem {
  title: string;
  location: string;
  imageUrl: string;
  date: string;
  buttonUrl: string;
}

export const EventThumbnailCard = ({ title, imageUrl, date, location, buttonUrl }: EventItem) => {
  const locale = useDateLocale();

  return (
    <div
      className={clsx(
        'group shrink-0 cursor-pointer rounded-2xl bg-white max-lg:w-[300px] xl:rounded-3xl',
        'shadow-[0px_12px_16px_0px_#0000000F] transition-shadow duration-300 hover:shadow-[0px_18px_16px_0px_#0000001A]',
      )}
    >
      <div className="flex flex-col">
        <div className="overflow-hidden rounded-tl-2xl rounded-tr-2xl xl:rounded-tl-3xl xl:rounded-tr-3xl">
          <Image
            alt="Event card alt text"
            className={clsx(
              'aspect-[1.6] w-full object-cover transition-transform duration-300 group-hover:scale-110',
              'rounded-tl-2xl rounded-tr-2xl xl:rounded-tl-3xl xl:rounded-tr-3xl',
            )}
            height={216}
            src={imageUrl}
            width={350}
          />
        </div>
        <div className="flex flex-col gap-[9px] p-6 max-lg:px-[18px]">
          <p className="font-primary text-dark-default text-xl leading-[1.1] font-bold uppercase">{title}</p>
          <div className="text-dark-default border-gray flex items-center justify-between border-b py-2 text-sm leading-[1]">
            <p className="font-medium">Location</p>
            <p className="font-primary font-bold">{location}</p>
          </div>
          <div className="text-dark-default flex items-center justify-between pb-4 text-sm leading-[1]">
            <p className="font-medium">Date</p>
            <p className="font-primary font-bold">{formatDate(date, 'do MMMM yyyy', { locale })}.</p>
          </div>
          <a
            className="text-dark-default bg-gray-easy text-button-default w-full cursor-pointer rounded-[15px] py-3 text-center"
            href={buttonUrl}
            onClick={() =>
              logButtonClickedEvent({
                location: `Event Thumbnail Grid Block - Card (${title})`,
                button_name: `Buy Tickets Link`,
              })
            }
          >
            buy tickets
          </a>
        </div>
      </div>
    </div>
  );
};
