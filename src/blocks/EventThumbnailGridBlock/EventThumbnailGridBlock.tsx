import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { BaseProps } from '../shared/types';
import { EventItem, EventThumbnailCard } from './EventThumbnailCard';

interface Props extends BaseProps {
  items: EventItem[];
}

export const EventThumbnailGridBlock = ({ items, ...rest }: Props) => {
  return (
    <BlockSectionWrapper {...rest}>
      <div className="flex gap-4 overflow-x-auto max-lg:-mx-4 max-lg:px-4 lg:grid lg:grid-cols-3 xl:gap-8 2xl:grid-cols-4">
        {items.map((item, i) => (
          <EventThumbnailCard {...item} key={i} />
        ))}
      </div>
    </BlockSectionWrapper>
  );
};
