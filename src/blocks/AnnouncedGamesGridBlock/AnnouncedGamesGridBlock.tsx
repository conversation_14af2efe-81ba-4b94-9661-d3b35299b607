import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { AnnouncedGamesGridBlockType } from '@/strapi/types/block';
import { MediaType } from '@/strapi/types/media';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const AnnouncedGamesGridBlock = ({ games, section }: AnnouncedGamesGridBlockType) => {
  if (games.length === 0) {
    return null;
  }

  return (
    <BlockSectionWrapper {...section}>
      <div className="flex w-full justify-center md:justify-start">
        <div className="hide-scrollbar grid w-full justify-between gap-2 overflow-x-auto max-md:grid-flow-col max-md:grid-rows-3 md:grid-cols-4 md:gap-4 lg:grid-cols-5">
          {games.map((item) => (
            <GridCard
              isLinkingToGamePageEnabled={item.isLinkingToGamePageEnabled}
              key={item.id}
              link={item.slug ? `/competitions/${item.slug}` : undefined}
              logo={item.logoDark}
            />
          ))}
        </div>
      </div>
    </BlockSectionWrapper>
  );
};

interface GridCardProps {
  logo: MediaType | null;
  link?: string;
  isLinkingToGamePageEnabled: boolean | null;
}

export const GridCard = ({ logo, link, isLinkingToGamePageEnabled }: GridCardProps) => {
  const cardContent = (
    <div className="group flex justify-center rounded-lg bg-white px-4 py-2 shadow-[0px_12px_16px_0px_#0000000F] max-md:h-[60px] max-md:w-[153px] lg:rounded-3xl lg:py-[15px]">
      {logo ? (
        <StrapiImage
          className="aspect-[3] w-40 object-contain transition-transform group-hover:scale-110"
          image={logo}
        />
      ) : null}
    </div>
  );

  return (
    <>
      {isLinkingToGamePageEnabled && link ? (
        <LocalizedLink
          brazeEventProperties={{ location: 'Announced Games Grid Block', button_name: `Grid Card Link (${link})` }}
          className="cursor-pointer"
          href={link}
        >
          {cardContent}
        </LocalizedLink>
      ) : (
        cardContent
      )}
    </>
  );
};
