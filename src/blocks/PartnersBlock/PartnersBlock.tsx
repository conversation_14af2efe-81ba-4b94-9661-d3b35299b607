import clsx from 'clsx';

import { StrapiImage } from '@/components/StrapiImage';
import { PartnersBlockType } from '@/strapi/types/block';

import { LocalizedLink } from '../../components/LocalizedLink';
import { PartnerType } from '../../strapi/types/collection/partner';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const PartnersBlock = ({ section, partners }: PartnersBlockType) => {
  if (!partners) {
    return null;
  }

  const sortedPartners = partners.sort(
    (a, b) => (a?.partnerLabel?.priority ?? Infinity) - (b?.partnerLabel?.priority ?? Infinity),
  );

  return (
    <BlockSectionWrapper {...section}>
      <div className="flex flex-row flex-wrap gap-2.5">
        {sortedPartners.map((partner) => {
          return (
            <div className="flex flex-col justify-end gap-1.5" key={partner.id}>
              {partner.partnerLabel?.title && (
                <span
                  className={clsx('font-primary text-[9px] leading-[110%] font-bold uppercase', {
                    'text-gold-primary': partner.partnerLabel.color === 'gold',
                    'text-dark-default': partner.partnerLabel.color === 'black',
                    'text-gray-dark': partner.partnerLabel.color === 'gray',
                  })}
                >
                  {partner.partnerLabel?.title}
                </span>
              )}
              <SponsorCard sponsor={partner} />
            </div>
          );
        })}
      </div>
    </BlockSectionWrapper>
  );
};

export const SponsorCard = ({ sponsor }: { sponsor: PartnerType }) => {
  const { link, logo, slug } = sponsor;

  const href = slug ? `/sponsors/${slug}` : link;
  const isExternalLink = link?.startsWith('http') && !slug;

  const cardContent = (
    <>
      {logo && (
        <div className="bg-white-dirty flex h-[96px] w-[157px] items-center justify-center overflow-hidden rounded-lg group-hover:bg-white lg:h-[100px] lg:w-[164px]">
          <StrapiImage className="h-auto max-h-full w-auto max-w-full object-contain" image={logo} />
        </div>
      )}
    </>
  );

  const cardClassName = 'flex transition-colors group flex-col justify-between rounded-lg';

  if (!href) {
    return <div className={cardClassName}>{cardContent}</div>;
  }

  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Partners Block', button_name: `Partner Card (${href})` }}
      className={cardClassName}
      href={href}
      target={isExternalLink ? '_blank' : undefined}
    >
      {cardContent}
    </LocalizedLink>
  );
};
