import clsx from 'clsx';
import { groupBy } from 'lodash';

import { PartnerRibbonBlockType } from '@/strapi/types/block';

import { LocalizedLink } from '../../components/LocalizedLink';
import { StrapiImage } from '../../components/StrapiImage';
import { PartnerType } from '../../strapi/types/collection/partner';
import { Marquee } from '../../ui/components/Marquee/Marquee';
import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';

export const PartnerRibbonBlock = ({ section, partnerRibbon, pauseOnHover, duration }: PartnerRibbonBlockType) => {
  const { partners } = partnerRibbon ?? {};

  if (!partners) return null;

  const filteredAndSortedPartners = partners
    .filter((partner) => partner.partnerLabel)
    .sort((a, b) => (a?.partnerLabel?.priority ?? Infinity) - (b?.partnerLabel?.priority ?? Infinity));

  const groupedPartners = groupBy(filteredAndSortedPartners, (partner) => partner.partnerLabel?.title);

  const groupColors = filteredAndSortedPartners.reduce(
    (acc, partner) => {
      if (partner.partnerLabel?.title && partner.partnerLabel?.color) {
        acc[partner.partnerLabel.title] = partner.partnerLabel.color;
      }
      return acc;
    },
    {} as Record<string, string>,
  );

  return (
    <BlockSectionWrapper {...section}>
      <Marquee duration={duration ? `${duration}s` : undefined} gap="2rem" pauseOnHover={pauseOnHover}>
        {Object.entries(groupedPartners).map(([label, partners], i) => (
          <div className="flex flex-col gap-4" key={`group-${label}-${i}`}>
            <div
              className={clsx('font-primary text-[14px] leading-[110%] font-bold capitalize lg:text-[16px]', {
                'text-gold-primary': groupColors[label] === 'gold',
                'text-dark-default': groupColors[label] === 'black',
              })}
            >
              {label}
            </div>
            <div className="flex items-center gap-4">
              {partners.map((partner) => (
                <SponsorCard key={partner.id} sponsor={partner} />
              ))}
            </div>
          </div>
        ))}
      </Marquee>
    </BlockSectionWrapper>
  );
};

export const SponsorCard = ({ sponsor }: { sponsor: PartnerType }) => {
  const { link, logo, slug } = sponsor;

  const href = slug ? `/sponsors/${slug}` : link;
  const isExternalLink = link?.startsWith('http') && !slug;

  const cardContent = (
    <>
      {logo && (
        <div className="bg-white-dirty flex h-[80px] w-[131px] items-center justify-center overflow-hidden group-hover:bg-white lg:h-[120px] lg:w-[196px]">
          <StrapiImage className="h-auto max-h-full w-auto max-w-full object-contain" image={logo} />
        </div>
      )}
    </>
  );

  const cardClassName =
    'flex transition-colors group flex-[1_1_0] flex-col justify-between gap-2 rounded-sm bg-white p-2 lg:p-4';

  if (!href) {
    return <div className={cardClassName}>{cardContent}</div>;
  }

  return (
    <LocalizedLink
      brazeEventProperties={{ location: 'Sponsors Ribbon Block', button_name: `Sponsor Link (${href})` }}
      className={cardClassName}
      href={href}
      target={isExternalLink ? '_blank' : undefined}
    >
      {cardContent}
    </LocalizedLink>
  );
};
