'use client';

import clsx from 'clsx';
import { AnimatePresence, motion } from 'motion/react';
import { useState } from 'react';

import { LocalizedLink } from '@/components/LocalizedLink';
import { StrapiImage } from '@/components/StrapiImage';
import { ContentNavigationGridBlockType } from '@/strapi/types/block';
import { ContentNavigationItemType } from '@/strapi/types/collection/contentNavigationItems';
import { Button } from '@/ui/components/Button';
import { Only } from '@/ui/components/Only';

import { BlockSectionWrapper } from '../shared/BlockSectionWrapper';
import { getMasonryPlacementMap } from './utils';

export function ContentNavigationGridBlock({ contentNavigationItems, section }: ContentNavigationGridBlockType) {
  const gridMap = getMasonryPlacementMap(contentNavigationItems.length);

  return (
    <BlockSectionWrapper {...section}>
      <div className="grid grid-flow-row-dense auto-rows-[240px] grid-cols-6 gap-4 xl:gap-8">
        {contentNavigationItems.map((item, i) => {
          return (
            <div className={clsx('col-span-full row-span-1', gridMap[i])} key={item.id}>
              <ContentNavigationCard item={item} />
            </div>
          );
        })}
      </div>
    </BlockSectionWrapper>
  );
}

function ContentNavigationCard({ item }: { item: ContentNavigationItemType }) {
  const [isButtonVisible, setIsButtonVisible] = useState(false);

  const card = (
    <div
      className="bg-dark-default group relative flex h-full w-full cursor-pointer items-end overflow-hidden rounded-4xl"
      onMouseEnter={() => setIsButtonVisible(true)}
      onMouseLeave={() => setIsButtonVisible(false)}
    >
      {item.backgroundImage && (
        <>
          <StrapiImage className="absolute inset-0 h-full w-full object-cover" image={item.backgroundImage} />
          <div className="absolute bottom-0 h-full w-full bg-[linear-gradient(206.57deg,_rgba(0,_0,_0,_0)_16.67%,_rgba(0,_0,_0,_0.4)_100%)]" />
        </>
      )}

      <motion.div className="relative m-5 flex w-full flex-col text-white lg:m-7 lg:w-[60%]" layout>
        {item.title && (
          <motion.h2 className="font-riforma mb-4 text-[21px] leading-[115%] font-bold lg:text-[28px]" layout>
            {item.title}
          </motion.h2>
        )}
        {item.subtitle && (
          <motion.p className="font-riforma text-h6 mb-4 leading-[130%] font-bold" layout>
            {item.subtitle}
          </motion.p>
        )}
        <Only for="lgAndAbove">
          <AnimatePresence>
            {item.buttonText && item.link && isButtonVisible && (
              <motion.div
                animate={{ height: 'auto', y: 0 }}
                className="origin-bottom"
                exit={{ height: 0, y: 20 }}
                initial={{ height: 0, y: 20 }}
                layout
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                <Button
                  brazeEventProperties={{
                    button_name: `Primary Button (${item.buttonText})`,
                    location: `Content Navigation Grid Block - Card (${item.title})`,
                  }}
                  text={item.buttonText}
                  variant="glassy"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </Only>
        <Only for="mdAndBelow">
          {item.buttonText && item.link && (
            <Button
              brazeEventProperties={{
                button_name: `Primary Button (${item.buttonText})`,
                location: `Content Navigation Grid Block - Card (${item.title})`,
              }}
              text={item.buttonText}
              variant="glassy"
            />
          )}
        </Only>
      </motion.div>
    </div>
  );

  if (item.link) {
    return (
      <LocalizedLink
        brazeEventProperties={{ location: 'Content Navigation Grid Block', button_name: `Card Link (${item.link})` }}
        href={item.link}
      >
        {card}
      </LocalizedLink>
    );
  }

  return card;
}
