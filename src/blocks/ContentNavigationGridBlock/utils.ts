const masonryPlacementMap: Record<number, string[]> = {
  1: ['md:row-span-2 md:col-span-full'],
  2: ['md:row-span-2 md:col-span-3', 'md:row-span-2 md:col-span-3'],
  3: ['md:row-span-2 md:col-span-4', 'md:row-span-3 md:col-span-2', 'md:row-span-1 md:col-span-4'],
  4: [
    'md:row-span-2 md:col-span-4',
    'md:row-span-1 md:col-span-2',
    'md:row-span-1 md:col-span-4',
    'md:row-span-2 md:col-span-2',
  ],
  5: [
    'md:row-span-2 md:col-span-4',
    'md:row-span-1 md:col-span-2',
    'md:row-span-1 md:col-span-4',
    'md:row-span-2 md:col-span-2',
    'md:row-span-2 md:col-span-full',
  ],
  6: [
    'md:row-span-2 md:col-span-4',
    'md:row-span-1 md:col-span-2',
    'md:row-span-1 md:col-span-4',
    'md:row-span-2 md:col-span-2',
    'md:row-span-1 md:col-span-2',
    'md:row-span-1 md:col-span-4',
  ],
  7: [
    'md:row-span-2 md:col-span-4',
    'md:row-span-1 md:col-span-2',
    'md:row-span-1 md:col-span-4',
    'md:row-span-2 md:col-span-2',
    'md:row-span-1 md:col-span-2',
    'md:row-span-3 md:col-span-4',
    'md:row-span-2 md:col-span-2',
  ],
  8: [
    'md:row-span-2 md:col-span-4',
    'md:row-span-1 md:col-span-2',
    'md:row-span-1 md:col-span-4',
    'md:row-span-2 md:col-span-2',
    'md:row-span-1 md:col-span-2',
    'md:row-span-2 md:col-span-4',
    'md:row-span-2 md:col-span-2',
    'md:row-span-1 md:col-span-4',
  ],
};

export function getMasonryPlacementMap(requiredPlacements: number) {
  const totalPlacements = Object.keys(masonryPlacementMap).length;

  const noOfMaxElements = Math.floor(requiredPlacements / totalPlacements);
  const remainderElements = requiredPlacements % totalPlacements;

  const placements = [];
  for (let i = 0; i < noOfMaxElements; i++) {
    placements.push(masonryPlacementMap[totalPlacements]);
  }
  placements.push(masonryPlacementMap[remainderElements]);

  return placements.flat();
}
