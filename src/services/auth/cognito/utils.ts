import { COGNITO_DOMAIN } from '@/config/env/client';

import { SocialProvider } from '../types';

function buildCognitoProviderUrl(provider: SocialProvider, params: string) {
  let url = `https://${COGNITO_DOMAIN}/oauth2/authorize?${params}`;
  switch (provider) {
    case SocialProvider.GOOGLE:
      url = `${url}&identity_provider=Google`;
      break;
    case SocialProvider.APPLE:
      url = `${url}&identity_provider=SignInWithApple`;
      break;
    case SocialProvider.TWITCH:
      url = `${url}&identity_provider=Twitch`;
      break;
    case SocialProvider.DISCORD:
      url = `${url}&identity_provider=Discord`;
      break;
  }

  return url;
}

export function signInWithProviderExternal(provider: SocialProvider, params: URLSearchParams) {
  const url = buildCognitoProviderUrl(provider, params.toString());
  window.location.assign(url);
}

export function removeUndefinedValues(obj: Record<string, any>) {
  const newObj: Record<string, any> = {};

  for (const key in obj) {
    const value = obj[key];
    if (value !== undefined) {
      newObj[key] = value;
    }
  }

  return newObj;
}
