import './config';

import {
  confirmResetPassword,
  confirmSignUp,
  deleteUser,
  fetchAuthSession,
  fetchUserAttributes,
  resetPassword,
  signIn,
  signInWithRedirect,
  signOut,
  signUp,
  updatePassword,
  updateUserAttributes,
} from 'aws-amplify/auth';

import { SignupValues, SocialProvider, UpdateUserValues, User } from '../types';
import { removeUndefinedValues } from './utils';

export async function signup({ email, password, username, name, surname, country }: SignupValues) {
  return signUp({
    username: email,
    password,
    options: {
      userAttributes: {
        email,
        preferred_username: username,
        name: `${name.trim()} ${surname.trim()}`,
        given_name: name,
        family_name: surname,
        'custom:country': country,
      },
    },
  });
}

export async function fetchCurrentUser() {
  const attributes = await fetchUserAttributes();
  const identities = JSON.parse((attributes as any).identities ?? '[]');

  return { ...attributes, identities } as unknown as User;
}

export async function signInWithProviderInternal(provider: SocialProvider) {
  if (['Amazon', 'Apple', 'Facebook', 'Google'].includes(provider)) {
    return signInWithRedirect({ provider: provider as 'Amazon' | 'Apple' | 'Facebook' | 'Google' });
  }
  return signInWithRedirect({ provider: { custom: provider } });
}

export async function updateUser({
  name,
  surname,
  username,
  zipcode,
  city,
  state,
  country,
  favoriteGames,
  favoriteClubs,
  favoritePlayers,
  ...rest
}: UpdateUserValues) {
  const attributes: any = {
    preferred_username: username,
    given_name: name,
    family_name: surname,
    name: name && surname ? `${name.trim()} ${surname.trim()}` : undefined,
    'custom:zipcode': zipcode,
    'custom:city': city,
    'custom:state': state,
    'custom:country': country,
    'custom:favourite_games': favoriteGames?.join(';'),
    'custom:favourite_clubs': favoriteClubs?.join(';'),
    'custom:favourite_players': favoritePlayers?.join(';'),
    ...rest,
  };

  const cleanUserAttributes = removeUndefinedValues(attributes);
  return updateUserAttributes({ userAttributes: cleanUserAttributes });
}

export {
  confirmResetPassword,
  confirmSignUp,
  deleteUser,
  fetchAuthSession,
  resetPassword,
  signIn,
  signOut,
  updatePassword,
};
