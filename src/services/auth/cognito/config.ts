import { Amplify } from 'aws-amplify';

import { APP_BASE_URL, COGNITO_CLIENT_ID, COGNITO_DOMAIN, COGNITO_USER_POOL_ID } from '@/config/env/client';

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolId: COGNITO_USER_POOL_ID,
      userPoolClientId: COGNITO_CLIENT_ID,
      loginWith: {
        oauth: {
          domain: COGNITO_DOMAIN,
          scopes: ['openid', 'aws.cognito.signin.user.admin'],
          responseType: 'code',
          redirectSignIn: [`${APP_BASE_URL}/profile`],
          redirectSignOut: [APP_BASE_URL],
        },
      },
    },
  },
});
