export interface User {
  sub: string;
  email: string;
  email_verified: boolean;
  preferred_username: string;
  given_name: string;
  family_name: string;
  name: string;
  gender?: string;
  birthdate?: string;
  address?: string;
  'custom:city'?: string;
  'custom:country'?: string;
  'custom:zipcode'?: string;
  'custom:state'?: string;
  'custom:favourite_games'?: string;
  'custom:favourite_clubs'?: string;
  'custom:favourite_players'?: string;
  identities?: UserIdentity[];
}

export interface UserIdentity {
  dateCreated: string;
  userId: string;
  providerName: string;
  providerType: string;
  issuer: null;
  primary: boolean;
}

export interface SignupValues {
  email: string;
  password: string;
  username: string;
  name: string;
  surname: string;
  country: string;
}

export type UpdateUserValues = Partial<Omit<SignupValues, 'password'>> & {
  address?: string;
  birthdate?: string;
  gender?: string;
  zipcode?: string;
  city?: string;
  state?: string;
  favoriteGames?: string[];
  favoriteClubs?: string[];
  favoritePlayers?: string[];
};

export enum SocialProvider {
  GOOGLE = 'Google',
  APPLE = 'Apple',
  TWITCH = 'Twitch',
  DISCORD = 'Discord',
}

export enum AuthMethod {
  USERNAME_PASSWORD = 'username_password',
  SOCIAL = 'social',
}
