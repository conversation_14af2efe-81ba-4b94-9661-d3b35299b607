import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

import { TTL_1_HOUR } from '@/config/cache';

import { logBrazeEvent, updateBrazeUser } from '../braze';
import { deleteUser, fetchCurrentUser, signOut, updateUser } from './cognito';
import { UpdateUserValues, User } from './types';

const USER_QUERY_KEY = ['user'];

export function useCurrentUser() {
  const { data: user, ...rest } = useQuery({
    queryKey: USER_QUERY_KEY,
    queryFn: fetchCurrentUser,
    staleTime: TTL_1_HOUR,
  });

  return { user, ...rest };
}

export function useSetCurrentUserQuery() {
  const queryClient = useQueryClient();

  return async (user: User) => {
    queryClient.setQueryData(USER_QUERY_KEY, user);
  };
}

export function useUpdateUser() {
  const invalidateCurrentUser = useInvalidateCurrentUserQuery();

  return useMutation({
    mutationFn: async (values: UpdateUserValues) => {
      await updateUser(values);
      updateBrazeUser(values);
    },
    onSuccess: invalidateCurrentUser,
  });
}

export function useInvalidateCurrentUserQuery() {
  const queryClient = useQueryClient();

  const invalidateCurrentUser = useCallback(
    () => queryClient.invalidateQueries({ queryKey: USER_QUERY_KEY, exact: true }),
    [queryClient],
  );

  return invalidateCurrentUser;
}

function useRemoveCurrentUser(by: 'logout' | 'delete', redirectPath = '/') {
  const router = useRouter();
  const queryClient = useQueryClient();

  return async () => {
    await (by === 'logout' ? signOut() : deleteUser());
    logBrazeEvent('user_logged_out', { platform: 'web', timestamp: Date.now() });

    queryClient.resetQueries({ queryKey: USER_QUERY_KEY, exact: true });
    router.replace(redirectPath);
  };
}

export function useLogout(redirectPath?: string) {
  //todo when signing out with social provider, the redirect is handled by the provider
  //     remove redirect logic in this case since it happens two times (first by social provider, and then by us explicitly)
  return useRemoveCurrentUser('logout', redirectPath);
}

export function useDeleteCurrentUser(redirectPath?: string) {
  return useRemoveCurrentUser('delete', redirectPath);
}
