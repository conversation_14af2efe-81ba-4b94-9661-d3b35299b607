'use client';

import 'aws-amplify/auth/enable-oauth-listener';

import { Hub } from 'aws-amplify/utils';

import { changeBrazeUser, updateBrazeUser } from '@/services/braze';

import { fetchCurrentUser } from '../cognito';
import { AuthMethod } from '../types';

Hub.listen('auth', async ({ payload }) => {
  if (payload.event === 'signInWithRedirect') {
    const user = await fetchCurrentUser();
    await changeBrazeUser(user.sub, AuthMethod.SOCIAL);
    updateBrazeUser({ email: user.email });
  }
});

export const SocialLoginListener = () => null;
