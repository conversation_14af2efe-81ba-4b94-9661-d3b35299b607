import { ID, Stream, Team } from './shared';
import { Member } from './tournament';

export interface MatchSeriesQueryResponse {
  matchSeries: {
    items: MatchSeries[];
  };
}

export interface MatchSeries {
  id: ID;
  createdAt: string;
  updatedAt: string;
  startTime: string | null;
  contestants: MatchSeriesContestant[];
  matches: Match[];
  metadata: MatchSeriesMetadata[];
  position: number | null;
  status: MatchSeriesStatus;
  streams: Stream[];
  tournament: { id: ID; variant: TournamentVariant };
}

export enum MatchSeriesStatus {
  CANCELLED = 'CANCELLED',
  DELAYED = 'DELAYED',
  FINISHED = 'FINISHED',
  LIVE = 'LIVE',
  OPEN = 'OPEN',
  POSTPONED = 'POSTPONED',
}

export enum TournamentVariant {
  '1_ON_1' = '1on1',
  '3_ON_3' = '3on3',
  '5_ON_5' = '5on5',
  '6_ON_6' = '6on6',
  '1_ON_1_TEAM' = '1on1_team',
  '15_ON_15' = '15on15',
  'FFA_SQUAD' = 'ffa_squad',
  'FFA_DUO' = 'ffa_duo',
  'FFA_SOLO' = 'ffa_solo',
  'CUSTOM_TEAM' = 'custom_team',
  '7_ON_7' = '7on7',
  '10_ON_10' = '10on10',
  '11_ON_11' = '11on11',
  '12_ON_12' = '12on12',
}

export interface MatchSeriesContestant {
  points: number | null;
  rank: number | null;
  result: MatchSeriesGameResult;
  score: number | null;
  team: Team;
  members: Member[];
}

enum MatchSeriesGameResult {
  DEFAULT_LOSS = 'DEFAULT_LOSS',
  DEFAULT_WIN = 'DEFAULT_WIN',
  DRAW = 'DRAW',
  LOSE = 'LOSE',
  WIN = 'WIN',
}

export interface Match {
  contestants: MatchSeriesContestant[];
  gameMap: GameMap;
  sequence: number | null;
  status: MatchStatus;
}

export enum MatchStatus {
  CLOSED = 'CLOSED',
  FINISHED = 'FINISHED',
  LIVE = 'LIVE',
  OPEN = 'OPEN',
}

export interface GameMap {
  id: ID;
  name: string;
}

export interface MatchSeriesMetadata {
  id: ID;
  name: string | null;
  position: number | null;
  type: MatchSeriesMetadataType;
}

export enum MatchSeriesMetadataType {
  BRACKET = 'BRACKET',
  GROUP = 'GROUP',
  ROUND = 'ROUND',
}
