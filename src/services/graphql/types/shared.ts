export type ID = string;

export interface Team {
  id: ID;
  name: string | null;
  club: Club;
  images: TeamImage[];
}

export interface Club {
  id: ID;
  name: string | null;
}

export type ImageType = 'logo_transparent_whitebg' | 'logo_transparent' | 'logo_transparent_blackbg' | (string & {});

export interface TeamImage {
  id: ID;
  type: ImageType | null;
}

export interface Stream {
  url: string;
  language: string;
  primary: boolean;
}
