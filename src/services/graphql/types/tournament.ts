import { ID, Stream, Team } from './shared';

// Root query response
export interface TournamentsQueryResponse {
  tournaments: {
    result: Tournament[] | null;
  };
}

export interface Tournament {
  id: ID;
  name: string | null;
  startTime: string | null;
  endTime: string | null;
  prizePool: Prize[] | null;
  streams: Stream[] | null;
  contestants: TournamentContestant[] | null;
}

export interface Prize {
  rank: number | null;
  amount: number | null;
  currency: PrizeCurrency | string | null;
}

export enum PrizeCurrency {
  USD = 'USD',
  XTS = 'XTS',
}

export interface TournamentContestant {
  rank: number | null;
  team: Team | null;
  members: Member[] | null;
}

export interface Member {
  role: {
    name: string | null;
  };
  player: {
    name: string | null;
    nationality: string | null;
  };
}
