import { useQuery } from '@apollo/client';

import { useCompetitionSlugs } from '@/app/[locale]/competitions/[slug]/_components/CompetitionSlugsProvider';

import { MATCH_SERIES_QUERY, TOURNAMENTS_QUERY } from './queries';
import { MatchSeriesQueryResponse } from './types/matchSeries';
import { TournamentsQueryResponse } from './types/tournament';

//todo examine option where we fetch all data (`useAllTournamentsData`) and then filter by ids inside the hook
//  pro: query is reused on every page, no need to refetch
//  con: larger data payload
export function useTournamentsData(tournamentIds: string[]) {
  return useQuery<TournamentsQueryResponse>(TOURNAMENTS_QUERY, {
    variables: { tournamentIds },
  });
}

export function useGameTournamentsData() {
  const competitionSlugs = useCompetitionSlugs();
  return useTournamentsData(competitionSlugs.map((s) => s.competitionId));
}

export function useAllTournamentsData() {
  return useTournamentsData([]);
}

export function useMatchSeriesData(tournamentIds: string[]) {
  return useQuery<MatchSeriesQueryResponse>(MATCH_SERIES_QUERY, {
    variables: { tournamentIds },
  });
}

export function useGameMatchSeriesData() {
  const competitionSlugs = useCompetitionSlugs();
  return useMatchSeriesData(competitionSlugs.map((s) => s.competitionId));
}

export function useAllMatchSeriesData() {
  return useMatchSeriesData([]);
}
