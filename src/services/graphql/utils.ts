import { GRAPH_API_URL } from '@/config/env/client';

import { ImageType, TeamImage } from './types/shared';

const IMAGES_URL = GRAPH_API_URL.replace('graphql', 'images');

/**
 * Constructs a CDN image URL from the GraphQL API image response
 * @param images - Array of image objects from the GraphQL API
 * @param type - The desired image type (e.g., 'logo_transparent_whitebg', 'logo_transparent')
 * @param size - The desired image size (default: '400x400')
 * @returns The constructed CDN URL or null if image not found
 */
export const constructImageUrl = (images?: TeamImage[], type?: ImageType, size: '400x400' | string = '400x400') => {
  if (!images?.length) return null;

  // If type is specified, find that specific image type
  if (type) {
    const typeImage = images.find((img) => img.type === type);
    if (!typeImage?.id) return null;
    return `${IMAGES_URL}/${typeImage.id}/${size}`;
  }

  // If no type specified, use the first image
  return `${IMAGES_URL}/${images[0].id}/${size}`;
};
