import { gql } from '@apollo/client';

export const TOURNAMENTS_QUERY = gql`
  query Tournaments($tournamentIds: [String!]!) {
    tournaments(filter: [{ ids: $tournamentIds }], limit: 100, sort: [{ field: START_TIME, order: ORDER_ASC }]) {
      result {
        id
        name
        startTime
        endTime
        prizePool {
          rank
          amount
          currency
        }
        streams {
          url
          language
          primary
        }
        contestants {
          rank
          team {
            id
            name
            club {
              id
              name
            }
            images {
              id
              type
            }
          }
          members {
            role {
              name
            }
            player {
              name
              nationality
            }
          }
        }
      }
    }
  }
`;

export const MATCH_SERIES_QUERY = gql`
  query MatchSeries($tournamentIds: [String!]!) {
    matchSeries(
      filters: [{ tournamentIds: $tournamentIds }]
      limit: 100
      sorts: [{ field: START_TIME, order: ORDER_ASC }]
    ) {
      items {
        id
        status
        startTime
        position
        tournament {
          id
          variant
        }
        streams {
          url
          language
          primary
        }
        metadata {
          id
          type
          position
          name
        }
        contestants {
          score
          result
          rank
          points
          team {
            id
            name
            images {
              id
              type
            }
          }
          members {
            player {
              name
              nationality
            }
            role {
              id
              name
            }
          }
        }
        links {
          direction
          result
          linkedMatchSeries {
            id
          }
        }
        matches {
          contestants {
            points
            rank
            result
            score
            team {
              id
            }
          }
          sequence
          status
          gameMap {
            id
            name
          }
        }
      }
    }
  }
`;
