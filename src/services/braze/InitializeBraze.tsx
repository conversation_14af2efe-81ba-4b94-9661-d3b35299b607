'use client';

import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { initializeBraze, logBrazeEvent } from '.';

export const InitializeBraze = ({ isDisabled = false }: { isDisabled?: boolean }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  useLogPageViewed(isInitialized);

  useEffect(() => {
    if (isInitialized || isDisabled) {
      return;
    }

    initializeBraze().then(() => setIsInitialized(true));
  }, [isDisabled, isInitialized]);

  return null;
};

//! use this only in root layout in order to work properly
//  previous page `pathname` is saved in a ref - it is used as a referrer for `page_viewed` log event
//  because of that it needs to be present in some root component (layout)
function useLogPageViewed(isInitialized: boolean) {
  const prevPathRef = useRef<string | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    if (!isInitialized) {
      return;
    }

    const data = { referrer: prevPathRef.current ?? document.referrer, url: pathname };
    logBrazeEvent('page_viewed', data);

    prevPathRef.current = pathname;
  }, [isInitialized, pathname]);
}
