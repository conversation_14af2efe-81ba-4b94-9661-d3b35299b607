'use client';

import clsx from 'clsx';
import { IoChevronForwardSharp } from 'react-icons/io5';

import { useInViewport } from '../hooks/useInViewport';
import { TopNavigationType } from '../strapi/types/helper';
import { LocalizedLink } from './LocalizedLink';

const TopNavigation = ({ navigationItems }: { navigationItems: TopNavigationType[] }) => {
  const activeId = useInViewport(navigationItems.map((item) => item.navigationId));

  if (navigationItems.length === 0) {
    return null;
  }

  return (
    <>
      <div className="md:bg-dark-default/30 hide-scrollbar fixed inset-x-0 z-[99] flex w-full gap-1 overflow-x-auto bg-white p-1 backdrop-blur-2xl max-md:pr-6 md:top-8 md:mx-auto md:w-fit md:rounded-sm md:p-0.5 lg:rounded-xl lg:p-1.5 xl:rounded-[18px] xl:p-1.5">
        {navigationItems.map((item) => (
          <LocalizedLink
            brazeEventProperties={{
              button_name: `Navigation Item (${item.navigationLabel}) Link`,
              location: 'Top Navigation',
            }}
            className={clsx(
              'font-riforma rounded-[3px] px-4 py-2.5 text-[12px] leading-[150%] font-bold tracking-[-1%] whitespace-nowrap text-black uppercase transition-colors duration-200 md:rounded-sm md:px-4 md:py-2.5 md:text-[13px] md:text-white lg:rounded-lg xl:rounded-[15px] xl:px-6 xl:py-2.5',
              {
                'bg-dark-default max-md:text-white': activeId === item.navigationId,
              },
            )}
            href={`#${item.navigationId}`}
            key={item.navigationId}
          >
            {item.navigationLabel}
          </LocalizedLink>
        ))}
      </div>
      <div className="fixed top-0 right-0 z-20 flex h-[46px] w-6 items-center justify-center bg-white md:hidden">
        <div className="absolute inset-y-0 right-full w-[26px] bg-gradient-to-l from-white to-transparent" />
        <IoChevronForwardSharp className="" />
      </div>
    </>
  );
};

export default TopNavigation;
