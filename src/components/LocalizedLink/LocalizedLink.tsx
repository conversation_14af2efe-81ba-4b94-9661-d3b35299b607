'use client';

import { Url } from 'next/dist/shared/lib/router/router';
import Link, { LinkProps } from 'next/link';

import { useCurrentLocale } from '@/hooks/i18n';
import { Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';

type Props = Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &
  LinkProps & {
    isDefaultLink?: boolean;
    brazeEventProperties: { button_name: string; location: string };
    children?: React.ReactNode | undefined;
  } & React.RefAttributes<HTMLAnchorElement>;

export const LocalizedLink = ({ href, isDefaultLink = true, brazeEventProperties, onClick, ...rest }: Props) => {
  const locale = useCurrentLocale();

  const normalizedHref = getNormalizedHref(href, locale);
  //todo serialize UrlObject to string
  if (isDefaultLink && typeof normalizedHref === 'string') {
    return (
      <a
        href={normalizedHref}
        onClick={(e) => {
          onClick?.(e);
          logButtonClickedEvent(brazeEventProperties);
        }}
        {...rest}
      />
    );
  }

  return (
    <Link
      href={normalizedHref}
      {...rest}
      onClick={(e) => {
        onClick?.(e);
        logButtonClickedEvent(brazeEventProperties);
      }}
    />
  );
};

const HTTP_PROTOCOL_REGEX = /^https?:\/\//;

function getNormalizedHref(href: Url, locale?: Locale) {
  const isUrlObject = typeof href !== 'string';
  if (isUrlObject) {
    return href;
  }

  const isInternalLink = href.startsWith('#');
  if (isInternalLink) {
    return href;
  }

  if (HTTP_PROTOCOL_REGEX.test(href)) {
    return href;
  }

  const normalizedHref = href.startsWith('/') ? href : `/${href}`;
  return `/${locale}${normalizedHref}`;
}
