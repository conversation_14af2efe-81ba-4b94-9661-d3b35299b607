'use client';

import { GoogleAnalytics } from '@next/third-parties/google';
import { useLocalStorage } from 'usehooks-ts';

export const GoogleAnalyticsProvider = ({ isProductionEnvironment }: { isProductionEnvironment: boolean }) => {
  const [acceptCookies] = useLocalStorage('accept-cookies', false);
  const [skipAnalytics] = useLocalStorage('skip-analytics', false);

  if (!isProductionEnvironment) return;

  if (!acceptCookies || skipAnalytics) return;

  return <GoogleAnalytics gaId="G-9103JK2F74" />;
};
