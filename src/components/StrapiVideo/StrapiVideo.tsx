import { MediaType } from '@/strapi/types/media';
import { getMediaUrl } from '@/strapi/utils';

type VideoProps = React.ComponentProps<'video'>;
type Props = Omit<VideoProps, 'src'> & { video: MediaType };

export const StrapiVideo = ({ video, ...rest }: Props) => {
  const videoUrl = getMediaUrl(video.url);
  return (
    <video {...rest} playsInline={rest.autoPlay}>
      <source src={videoUrl} />
      Your browser does not support HTML video.{' '}
      <a className="text-gold-primary cursor-pointer underline" href={videoUrl} target="__blank">
        Download the video from the link instead
      </a>
      .
    </video>
  );
};
