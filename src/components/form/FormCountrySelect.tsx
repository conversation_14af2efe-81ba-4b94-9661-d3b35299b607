import country from 'country-list-js';

import { SingleSelectProps } from '@/ui/components/SelectInput';

import { FormSingleSelect } from './FormSingleSelect';

const countries = country.names().toSorted();
const countryOptions = countries.map((c) => ({ value: c, label: c }));

type Props = Omit<SingleSelectProps, 'value' | 'onSelect' | 'options'>;

export const FormCountrySelect = (props: Props) => {
  return <FormSingleSelect name="country" options={countryOptions} {...props} />;
};
