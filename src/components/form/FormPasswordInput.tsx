import { useController } from 'react-hook-form';

import { PasswordInput } from '@/ui/components/TextInput/PasswordInput';
import { TextInputProps } from '@/ui/components/TextInput/TextInput';

type Props = {
  name: string;
} & Omit<TextInputProps, 'value' | 'onChange' | 'onBlur' | 'iconRight' | 'onClickIconRight'>;

export const FormPasswordInput = ({ name, ...rest }: Props) => {
  const {
    field,
    fieldState: { error },
  } = useController({ name });

  return <PasswordInput {...field} {...rest} error={error?.message} />;
};
