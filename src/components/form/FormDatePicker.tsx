import { formatDate, formatISO, parse } from 'date-fns';
import { useController } from 'react-hook-form';

import { DatePicker, DatePickerProps } from '@/ui/components/DatePicker';
import { TextInput } from '@/ui/components/TextInput';
import { TextInputProps } from '@/ui/components/TextInput/TextInput';

type Props = Pick<TextInputProps, 'label' | 'placeholder'> &
  Pick<DatePickerProps, 'areFutureDatesDisabled'> & {
    name: string;
  };

export const FormDatePicker = ({ name, areFutureDatesDisabled, ...rest }: Props) => {
  const {
    field: { value, onChange, ...field },
  } = useController({ name });

  const parsedValue = revertFormatToDateString(value);
  return (
    <DatePicker
      areFutureDatesDisabled={areFutureDatesDisabled}
      date={parsedValue}
      trigger={<TextInput {...field} {...rest} value={value} />}
      onDateSelect={(d) => {
        const value = d ? formatDate(d, US_DATE_FORMAT) : d;
        onChange(value);
      }}
    />
  );
};

const US_DATE_FORMAT = 'MM/dd/yyyy';

function revertFormatToDateString(value: string) {
  if (value) {
    const date = parse(value, US_DATE_FORMAT, new Date());
    return formatISO(date, { representation: 'date' });
  }
  return value;
}
