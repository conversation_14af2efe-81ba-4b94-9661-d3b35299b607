import { useController } from 'react-hook-form';

import { TextInput } from '@/ui/components/TextInput';
import { TextInputProps } from '@/ui/components/TextInput/TextInput';

type Props = {
  name: string;
} & Omit<TextInputProps, 'value' | 'onChange' | 'onBlur'>;

export const FormTextInput = ({ name, ...rest }: Props) => {
  const {
    field,
    fieldState: { error },
  } = useController({ name });

  return <TextInput {...field} {...rest} error={error?.message} />;
};
