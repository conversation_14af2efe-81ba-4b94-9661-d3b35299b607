import { useController } from 'react-hook-form';

import { SingleSelect, SingleSelectProps } from '@/ui/components/SelectInput';

type Props = {
  name: string;
} & Omit<SingleSelectProps, 'value' | 'onSelect'>;

export const FormSingleSelect = ({ name, ...rest }: Props) => {
  const {
    field: { onChange, ...field },
    fieldState: { error },
  } = useController({ name });

  return <SingleSelect error={error?.message} onSelect={onChange} {...field} {...rest} />;
};
