import { useController } from 'react-hook-form';

import { MultiSelect, MultiSelectProps } from '@/ui/components/SelectInput';

type Props = {
  name: string;
} & Omit<MultiSelectProps, 'values' | 'onSelect'>;

export const FormMultiSelect = ({ name, ...rest }: Props) => {
  const {
    field: { onChange, value, ...field },
    fieldState: { error },
  } = useController({ name });

  return <MultiSelect error={error?.message} values={value} onSelect={onChange} {...field} {...rest} />;
};
