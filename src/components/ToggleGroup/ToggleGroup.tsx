'use client';

import clsx from 'clsx';

import { logButtonClickedEvent } from '@/services/braze';

export interface ToggleFilter {
  value: string;
  label: string;
}

interface Props {
  filters: ToggleFilter[];
  selectedFilter: string;
  onFilterSelect: (f: string) => void;
}

export const ToggleGroup = ({ filters, selectedFilter, onFilterSelect }: Props) => {
  return (
    <div className="bg-dark-default/5 flex gap-1 rounded-xl p-1.5 lg:rounded-[18px]">
      {filters.map((f) => (
        <ToggleButton
          isSelected={selectedFilter === f.value}
          key={f.value}
          text={f.label}
          onClick={() => onFilterSelect(f.value)}
        />
      ))}
    </div>
  );
};

interface ToggleButtonProps {
  text: string;
  isSelected?: boolean;
  onClick: () => void;
}

const ToggleButton = ({ text, isSelected, onClick }: ToggleButtonProps) => (
  <button
    className={clsx(
      'w-full rounded-xl px-5 py-[13.5px] transition-colors md:px-6 lg:whitespace-nowrap',
      'font-primary text-[11px] leading-[1] font-bold uppercase lg:text-[13px]',
      isSelected ? 'bg-dark-default text-white' : 'text-dark-default cursor-pointer',
    )}
    onClick={
      isSelected
        ? undefined
        : () => {
            onClick();
            logButtonClickedEvent({ location: 'Toggle Group', button_name: `Filter Button (${text})` });
          }
    }
  >
    {text}
  </button>
);
