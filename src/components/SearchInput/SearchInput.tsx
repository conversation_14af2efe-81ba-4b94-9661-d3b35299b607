'use client';

import clsx from 'clsx';
import { useState } from 'react';
import { MdSearch } from 'react-icons/md';

import { logButtonClickedEvent } from '@/services/braze';

export interface SearchInputProps {
  initialValue?: string;
  onSearch: (value: string) => void;
  placeholder?: string;
  analyticsLocation?: string;
  analyticsButtonName?: string;
}

export const SearchInput = ({
  initialValue = '',
  onSearch,
  placeholder = 'Search...',
  analyticsLocation = 'Search Input',
  analyticsButtonName = 'Trigger Search Button',
}: SearchInputProps) => {
  const [searchValue, setSearchValue] = useState(initialValue);

  return (
    <div className="flex w-full items-center justify-between gap-2 rounded-[15px] bg-white px-6 py-[15px] shadow-[4px_8px_16px_0px_#00000014]">
      <input
        className={clsx(
          'text-dark-default grow outline-none',
          'placeholder:font-base placeholder:text-base placeholder:leading-normal placeholder:font-bold placeholder:text-[#D1D1D1]',
        )}
        placeholder={placeholder}
        type="text"
        value={searchValue}
        onChange={(e) => setSearchValue(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            onSearch(searchValue);
          }
        }}
      />
      <button
        onClick={() => {
          onSearch(searchValue);
          logButtonClickedEvent({
            location: analyticsLocation,
            button_name: analyticsButtonName,
          });
        }}
      >
        <MdSearch className="text-[#272727]" size={21} />
      </button>
    </div>
  );
};
