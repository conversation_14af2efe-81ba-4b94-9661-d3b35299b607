'use client';

import { BlocksRenderer } from '@strapi/blocks-react-renderer';

import { LocalizedLink } from '@/components/LocalizedLink';
import { RichTextContentType } from '@/strapi/types/helper';

interface Props {
  content: RichTextContentType;
  paragraphClassname?: string;
  linkClassname?: string;
}

export const SimpleRichTextContent = ({ content, ...rest }: Props) => {
  return <BlocksRenderer blocks={blocks(rest)} content={content} />;
};

const Paragraph = ({ classname, children }: React.PropsWithChildren<{ classname?: string }>) => {
  return <p className={classname}>{children}</p>;
};

const Link = ({ classname, url, children }: React.PropsWithChildren<{ classname?: string; url: string }>) => (
  <LocalizedLink
    brazeEventProperties={{ location: 'Rich Text Content', button_name: `Link (${url})` }}
    className={classname}
    href={url}
  >
    {children}
  </LocalizedLink>
);

const Empty = () => null;

const blocks = ({ paragraphClassname, linkClassname }: Omit<Props, 'content'>) => ({
  paragraph: (props: React.PropsWithChildren) => <Paragraph classname={paragraphClassname} {...props} />,
  link: (props: React.PropsWithChildren<{ url: string }>) => <Link classname={linkClassname} {...props} />,
  heading: Empty,
  list: Empty,
  code: Empty,
  image: Empty,
});
