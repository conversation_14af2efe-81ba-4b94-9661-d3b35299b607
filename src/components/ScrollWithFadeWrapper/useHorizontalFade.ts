import { useCallback, useEffect, useState } from 'react';

export const useHorizontalFade = (scrollRef: React.RefObject<HTMLDivElement | null>, isRTL: boolean) => {
  const [showLeftFade, setShowLeftFade] = useState(false);
  const [showRightFade, setShowRightFade] = useState(false);

  const updateFadeVisibility = useCallback(() => {
    const el = scrollRef.current;
    if (!el) return;

    const { scrollLeft, scrollWidth, clientWidth } = el;
    if (isRTL) {
      setShowRightFade(scrollLeft < 0);
      setShowLeftFade(scrollLeft * -1 + clientWidth < scrollWidth - 1);
    } else {
      setShowLeftFade(scrollLeft > 0);
      setShowRightFade(scrollLeft + clientWidth < scrollWidth - 1);
    }
  }, [scrollRef, isRTL]);

  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;

    let frame: number;
    const onScroll = () => {
      cancelAnimationFrame(frame);
      frame = requestAnimationFrame(updateFadeVisibility);
    };

    frame = requestAnimationFrame(updateFadeVisibility);

    el.addEventListener('scroll', onScroll);
    window.addEventListener('resize', onScroll);

    return () => {
      el.removeEventListener('scroll', onScroll);
      window.removeEventListener('resize', onScroll);
      cancelAnimationFrame(frame);
    };
  }, [updateFadeVisibility, scrollRef]);

  return { showLeftFade, showRightFade };
};
