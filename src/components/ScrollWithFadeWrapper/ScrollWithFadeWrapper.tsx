import clsx from 'clsx';
import React, { useRef } from 'react';

import { useCurrentLocale } from '@/hooks/i18n';

import { useDragToScroll } from './useDragToScroll';
import { useHorizontalFade } from './useHorizontalFade';

type ScrollableWithFadeProps = {
  children: React.ReactNode;
  className?: string;
  fadeSize?: string; // optional: customize gradient width
};

export const ScrollWithFadeWrapper = ({
  children,
  className = '',
  fadeSize = 'w-20 md:w-50',
}: ScrollableWithFadeProps) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  const currentLocale = useCurrentLocale();

  const isRTL = currentLocale === 'ar';
  const { showLeftFade, showRightFade } = useHorizontalFade(scrollRef, isRTL);
  useDragToScroll(scrollRef);

  return (
    <div className={clsx('relative', className)}>
      <div
        className={clsx(
          'from-white-dirty pointer-events-none absolute inset-y-0 left-0 z-10 bg-gradient-to-r to-transparent',
          fadeSize,
          showLeftFade ? 'visible' : 'invisible',
        )}
      />
      <div className="hide-scrollbar overflow-x-scroll" ref={scrollRef}>
        {children}
      </div>
      <div
        className={clsx(
          'from-white-dirty pointer-events-none absolute inset-y-0 right-0 z-10 bg-gradient-to-l to-transparent',
          fadeSize,
          showRightFade ? 'visible' : 'invisible',
        )}
      />
    </div>
  );
};
