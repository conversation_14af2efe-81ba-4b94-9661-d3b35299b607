import { useEffect } from 'react';

import { isTouchDevice } from '@/utils/device';

export const useDragToScroll = (scrollRef: React.RefObject<HTMLDivElement | null>) => {
  useEffect(() => {
    const el = scrollRef.current;
    if (!el) return;
    if (isTouchDevice()) return;

    //TODO: Edge case: resizing
    el.style.cursor = 'grab';
    if (el.scrollWidth > el.clientWidth) {
      let isDragging = false;
      let startX = 0;
      let scrollStart = 0;

      const onMouseDown = (e: MouseEvent) => {
        isDragging = true;
        startX = e.pageX - el.offsetLeft;
        scrollStart = el.scrollLeft;
        el.style.cursor = 'grabbing';
      };

      const stopDragging = () => {
        isDragging = false;
        el.style.cursor = 'grab';
      };

      const onMouseMove = (e: MouseEvent) => {
        if (!isDragging) return;
        e.preventDefault();
        const x = e.pageX - el.offsetLeft;
        const walk = x - startX;
        el.scrollLeft = scrollStart - walk;
      };

      el.addEventListener('mousedown', onMouseDown);
      el.addEventListener('mouseup', stopDragging);
      el.addEventListener('mouseleave', stopDragging);
      el.addEventListener('mousemove', onMouseMove);

      return () => {
        el.removeEventListener('mousedown', onMouseDown);
        el.removeEventListener('mouseup', stopDragging);
        el.removeEventListener('mouseleave', stopDragging);
        el.removeEventListener('mousemove', onMouseMove);
      };
    }
  }, [scrollRef]);
};
