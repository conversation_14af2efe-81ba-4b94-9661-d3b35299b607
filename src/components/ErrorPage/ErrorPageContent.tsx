import Image from 'next/image';

import { ErrorType } from '@/strapi/types/collection/error-page';
import { ButtonType } from '@/strapi/types/shared';

// import { Button } from '@/ui/components/Button';
import ErrorBgImage from './error-bg-image.png';

interface Props {
  type: ErrorType;
  description: string | null;
  button: ButtonType | null;
}

export const ErrorPageContent = ({ type, description }: Props) => {
  const errorType = () => {
    if (type && type === 'not-found') {
      return '404';
    } else if (type && type === 'server-error') {
      return '500';
    }
    return null;
  };

  return (
    <div className="flex h-full flex-1 items-center justify-center bg-cover bg-center bg-no-repeat">
      <Image alt="" className="absolute inset-0 size-full object-cover" quality={65} src={ErrorBgImage} />
      <div className="to-white-dirty absolute inset-0 size-full bg-gradient-to-r from-transparent" />
      <div className="font-primary relative mx-4 flex flex-col items-center gap-8 text-center md:mx-18 md:w-[640px]">
        <p className="text-radial-gold text-[160px] font-bold md:text-[280px] md:leading-[1]">{errorType()}</p>
        {description && (
          <p className="text-dark-default text-[14px] font-bold uppercase md:text-[18px] xl:text-[20px]">
            {description}
          </p>
        )}
        {/* {button && <Button {...button} />} */}
      </div>
    </div>
  );
};
