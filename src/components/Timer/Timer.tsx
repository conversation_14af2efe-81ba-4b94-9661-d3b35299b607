'use client';

import clsx from 'clsx';
import { differenceInDays, interval, intervalToDuration } from 'date-fns';
import { useEffect, useState } from 'react';

export const Timer = ({ targetDatetime }: { targetDatetime: string }) => {
  const [timeLeft, setTimeLeft] = useState(() => calculateTimeLeft(targetDatetime));

  useEffect(() => {
    const timer = setInterval(() => setTimeLeft(calculateTimeLeft(targetDatetime)), 1000);
    return () => clearInterval(timer);
  }, [targetDatetime]);

  const { days, hours, minutes, seconds } = timeLeft;
  return (
    <div className="rounded-2xl bg-white/10 p-3 pb-2 backdrop-blur-[32px] md:rounded-4xl md:p-[18px] md:pb-[9.5px]">
      <div
        className={clsx(
          'grid gap-y-[7px] md:gap-y-[9.5px]',
          'md:grid-cols-[repeat(3,minmax(128px,auto)_31px)_minmax(128px,auto)]',
          'grid-cols-[repeat(3,minmax(60px,auto)_21px)_minmax(60px,auto)]',
        )}
      >
        <TimeCell time={days} />
        <SeparatorCell />
        <TimeCell time={hours} />
        <SeparatorCell />
        <TimeCell time={minutes} />
        <SeparatorCell />
        <TimeCell time={seconds} />
        <MetricCell text="days" />
        <div className="col-start-3">
          <MetricCell text="hours" />
        </div>
        <div className="col-start-5">
          <MetricCell text="minutes" />
        </div>
        <div className="-col-start-2">
          <MetricCell text="seconds" />
        </div>
      </div>
    </div>
  );
};

const TimeCell = ({ time }: { time: number }) => (
  <div className="flex items-center justify-center rounded-2xl bg-white/10 px-3 py-2">
    <p className="text-[32px] leading-tight text-white md:text-7xl" suppressHydrationWarning>
      {time.toString().padStart(2, '0')}
    </p>
  </div>
);

const SeparatorCell = () => (
  <div className="flex items-center justify-center">
    <p className="text-[32px] leading-tight text-white md:text-5xl">:</p>
  </div>
);

const MetricCell = ({ text }: { text: string }) => (
  <div className="flex justify-center">
    <p className="text-bold text-[8px] leading-tight text-white uppercase md:text-[10px]">{text}</p>
  </div>
);

const calculateTimeLeft = (targetDatetime: string) => {
  const now = Date.now();

  const timeLeftInterval = interval(now, targetDatetime);
  const { hours, minutes, seconds } = intervalToDuration(timeLeftInterval);
  const days = differenceInDays(targetDatetime, now);

  return { days, hours: hours ?? 0, minutes: minutes ?? 0, seconds: seconds ?? 0 };
};
