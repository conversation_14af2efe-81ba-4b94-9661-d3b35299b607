'use client';

import { clsx } from 'clsx';
import { motion } from 'motion/react';
import { MdChevronRight, MdLanguage } from 'react-icons/md';
import { useToggle } from 'usehooks-ts';

import { useCurrentLocale, useReplaceLocale } from '@/hooks/i18n';
import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';
import { logButtonClickedEvent } from '@/services/braze';

import { localeTextMap } from './const';
import { Text } from './LanguageDropdown';

const BUTTON_HEIGHT = 47.5;
const GAP = 4;

interface Props {
  isOpenUpwards?: boolean;
  disabledLocales?: Locale[];
}

export const DarkLanguageDropdown = ({ isOpenUpwards = false, disabledLocales }: Props) => {
  const [isOpen, toggleIsOpen] = useToggle(false);
  const replaceLocale = useReplaceLocale();

  const locale = useCurrentLocale();
  const otherLocales = ALL_LOCALES.filter((l) => l !== locale && !disabledLocales?.includes(l));

  return (
    <div className="text-white">
      <Button onClick={toggleIsOpen}>
        <div className="flex items-center gap-6">
          <MdLanguage className="size-5" />
          <div className="mt-px">
            <Text>{localeTextMap[locale]}</Text>
          </div>
          <MdChevronRight
            className={clsx(
              'size-5 transition-transform',
              isOpen ? (isOpenUpwards ? 'rotate-90' : 'rotate-270') : isOpenUpwards ? 'rotate-270' : 'rotate-90',
            )}
          />
        </div>
      </Button>
      {isOpen &&
        otherLocales.map((l, i) => (
          <motion.div
            animate={{ opacity: 1 }}
            className="w-full"
            initial={{ opacity: 0 }}
            key={l}
            style={{ position: 'absolute', top: (BUTTON_HEIGHT + GAP) * (i + 1) * (isOpenUpwards ? -1 : 1) }}
          >
            <Button
              onClick={() => {
                replaceLocale(l);
                logButtonClickedEvent({
                  location: 'Footer - Language Dropdown',
                  button_name: `Change Language to ${localeTextMap[l]}`,
                });
              }}
            >
              <Text>{localeTextMap[l]}</Text>
            </Button>
          </motion.div>
        ))}
    </div>
  );
};

const Button = ({ onClick, children }: { onClick: () => void; children: React.ReactNode }) => (
  <button
    className="bg-dark-default flex w-full cursor-pointer justify-center rounded-xl px-6 py-[14px]"
    onClick={onClick}
  >
    {children}
  </button>
);
