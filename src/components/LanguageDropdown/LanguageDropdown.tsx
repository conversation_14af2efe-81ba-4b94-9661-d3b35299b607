'use client';

import { clsx } from 'clsx';
import { motion } from 'motion/react';
import { MdChevronRight, MdLanguage } from 'react-icons/md';
import { useToggle } from 'usehooks-ts';

import { useCurrentLocale, useReplaceLocale } from '@/hooks/i18n';
import { ALL_LOCALES, Locale } from '@/hooks/i18n/const';

import { localeTextMap } from './const';

interface Props {
  disabledLocales?: Locale[];
}

// TODO: delete this file
// no longer in use, this was floating language dropdown in top right corner, replaced by sidebar integrated picker
// https://www.figma.com/design/CXDy8sk5X2loJcSKmwHH6u/EWC-2025-Web?node-id=5300-28139&t=RuO6FRg56ox5xE0G-4
export const LanguageDropdown = ({ disabledLocales }: Props) => {
  const [isOpen, toggleIsOpen] = useToggle(false);
  const replaceLocale = useReplaceLocale();

  const locale = useCurrentLocale();
  const otherLocales = ALL_LOCALES.filter((l) => l !== locale && !disabledLocales?.includes(l));

  return (
    <div className="flex flex-col gap-1 text-[#272727] shadow-[4px_8px_16px_0px_#00000014]">
      <Button onClick={toggleIsOpen}>
        <div className="flex items-center gap-2 lg:gap-2.5">
          <MdLanguage className="size-5" />
          <div
            className={clsx(
              'mt-px overflow-x-hidden transition-[width] transition-discrete',
              locale === 'en' && (isOpen ? 'w-calc-size' : 'w-[19px]'),
            )}
          >
            <Text>{localeTextMap[locale]}</Text>
          </div>
          <MdChevronRight className={clsx('size-5 transition-transform', isOpen ? 'rotate-270' : 'rotate-90')} />
        </div>
      </Button>
      {isOpen &&
        otherLocales.map((l) => (
          <motion.div animate={{ opacity: 1 }} initial={{ opacity: 0 }} key={l}>
            <Button onClick={() => replaceLocale(l)}>
              <Text>{localeTextMap[l]}</Text>
            </Button>
          </motion.div>
        ))}
    </div>
  );
};

const Button = ({ onClick, children }: { onClick: () => void; children: React.ReactNode }) => (
  <button
    className="w-full cursor-pointer rounded-sm bg-white p-3 lg:rounded-xl lg:px-[14px] lg:py-4"
    onClick={onClick}
  >
    {children}
  </button>
);

export const Text = ({ children }: { children: React.ReactNode }) => (
  <p className="font-primary text-center text-[13px] leading-normal uppercase">{children}</p>
);
