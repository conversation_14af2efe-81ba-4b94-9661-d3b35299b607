import bundleAnalyser from '@next/bundle-analyzer';
import type { NextConfig } from 'next';

import { ASSETS_CACHE_HEADERS, TTL_1_HOUR } from '@/config/cache';
import { CDN_BASE_URL, STRAPI_MEDIA_HOSTNAME } from '@/config/env/client';
import { isLocalEnvironment } from '@/config/env/server';

const nextConfig: NextConfig = {
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });
    return config;
  },
  output: 'standalone',
  devIndicators: false,
  poweredByHeader: false,
  images: {
    remotePatterns: [
      {
        protocol: isLocalEnvironment ? 'http' : 'https',
        hostname: STRAPI_MEDIA_HOSTNAME,
        port: isLocalEnvironment ? '1337' : undefined,
        pathname: isLocalEnvironment ? '/uploads/**' : undefined,
      },
      {
        protocol: 'https',
        hostname: CDN_BASE_URL,
      },
      {
        // CDN FOR GENERATING IMAGES COMING FROM GQL API
        // check src/services/graphql/utils.ts
        protocol: 'https',
        hostname: 'dzuvpjnlus2rx.cloudfront.net',
      },
    ],
    minimumCacheTTL: TTL_1_HOUR,
  },
  headers: async () => [
    ...ASSETS_CACHE_HEADERS,
    {
      source: '/:path*',
      headers: [
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff',
        },
        {
          key: 'Strict-Transport-Security',
          value: 'max-age=31536000; includeSubDomains',
        },
      ],
    },
  ],
};

const withBundleAnalyser = bundleAnalyser({ enabled: process.env.ANALYZE === 'true' });
export default withBundleAnalyser(nextConfig);
